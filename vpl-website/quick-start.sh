#!/bin/bash

# VPL Website Quick Start Script
# This script provides the fastest way to get VPL website running

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Docker on different systems
install_docker() {
    print_status "Installing Docker..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command_exists apt-get; then
            # Ubuntu/Debian
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            print_warning "Please log out and log back in for Docker permissions to take effect"
        elif command_exists yum; then
            # CentOS/RHEL
            sudo yum install -y docker
            sudo systemctl start docker
            sudo systemctl enable docker
            sudo usermod -aG docker $USER
        else
            print_error "Unsupported Linux distribution. Please install Docker manually."
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command_exists brew; then
            brew install --cask docker
            print_warning "Please start Docker Desktop manually"
        else
            print_error "Please install Docker Desktop from https://docker.com/products/docker-desktop"
            exit 1
        fi
    else
        print_error "Unsupported operating system. Please install Docker manually."
        exit 1
    fi
}

# Function to wait for Docker to be ready
wait_for_docker() {
    print_status "Waiting for Docker to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker info >/dev/null 2>&1; then
            print_success "Docker is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts: Waiting for Docker..."
        sleep 2
        ((attempt++))
    done
    
    print_error "Docker failed to start within expected time"
    print_status "Please start Docker manually and run this script again"
    exit 1
}

# Main function
main() {
    print_header "VPL Website Quick Start"
    echo
    print_status "This script will set up VPL Website with Docker in just a few minutes!"
    echo
    
    # Check if Docker is installed
    if ! command_exists docker; then
        print_warning "Docker is not installed"
        read -p "Would you like to install Docker automatically? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_docker
        else
            print_error "Docker is required. Please install it manually and run this script again."
            print_status "Visit: https://docs.docker.com/get-docker/"
            exit 1
        fi
    fi
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_warning "Docker is not running"
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            print_status "Starting Docker Desktop..."
            open -a Docker
            wait_for_docker
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            print_status "Starting Docker service..."
            sudo systemctl start docker
            wait_for_docker
        else
            print_error "Please start Docker manually and run this script again"
            exit 1
        fi
    fi
    
    # Check Docker Compose
    if ! command_exists docker-compose && ! docker compose version >/dev/null 2>&1; then
        print_error "Docker Compose is not available"
        print_status "Please install Docker Compose and run this script again"
        print_status "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_success "Docker is ready!"
    echo
    
    # Choose environment
    print_status "Choose deployment environment:"
    echo "1) Production (recommended for testing the full application)"
    echo "2) Development (includes additional development tools)"
    echo
    read -p "Enter your choice (1-2) [1]: " choice
    choice=${choice:-1}
    
    case $choice in
        1)
            environment="production"
            print_status "Selected: Production environment"
            ;;
        2)
            environment="development"
            print_status "Selected: Development environment"
            ;;
        *)
            print_warning "Invalid choice, using production environment"
            environment="production"
            ;;
    esac
    
    echo
    print_header "Starting Deployment"
    
    # Run deployment script
    if [[ -f "./deploy.sh" ]]; then
        print_status "Running deployment script..."
        ./deploy.sh -e "$environment"
    else
        print_error "Deployment script not found. Are you in the correct directory?"
        exit 1
    fi
    
    echo
    print_header "Deployment Complete!"
    
    print_success "VPL Website is now running!"
    echo
    echo "🌐 Access your application:"
    echo "   Main Website: http://localhost:3000"
    echo "   Admin Panel:  http://localhost:3000/admin"
    echo
    
    if [[ "$environment" == "development" ]]; then
        echo "🛠️  Development Tools:"
        echo "   pgAdmin:        http://localhost:8080"
        echo "   Redis Commander: http://localhost:8081"
        echo
    fi
    
    echo "📋 Default Admin Credentials:"
    echo "   Username: admin"
    echo "   Email:    <EMAIL>"
    echo "   Password: (check .env file for ADMIN_PASSWORD)"
    echo
    echo "🔧 Useful Commands:"
    echo "   View logs:      docker-compose logs -f"
    echo "   Stop services:  docker-compose down"
    echo "   Restart:        ./deploy.sh -e $environment"
    echo
    echo "📚 For more information, see:"
    echo "   - README.md"
    echo "   - DOCKER_DEPLOYMENT.md"
    echo
    
    print_success "Setup completed successfully! 🎉"
}

# Run main function
main "$@"
