# VPL - 专业外贸网络线路与VPN服务官网

VPL是一个专业的B2B官方网站，为外贸企业提供网络线路、跨境电商外网线路和VPN服务。网站采用现代化的技术栈构建，支持多语言、响应式设计，并包含完整的后台管理系统。

## 🌟 主要特性

### 前端功能
- **多语言支持**: 中文（默认）、英文、俄语
- **响应式设计**: 完美适配移动端、平板和桌面设备
- **专业B2B设计**: 针对企业客户的专业界面设计
- **服务展示**: 详细的服务介绍页面
- **联系表单**: 多种联系方式的B2B咨询表单
- **验证码系统**: 字母数字混合验证码

### 后端功能
- **管理后台**: 安全的管理员登录和仪表板
- **实时通知**: Socket.IO实现的实时通知系统
- **邮件集成**: 自动发送确认邮件和管理员通知
- **表单管理**: 客户咨询的管理和跟踪
- **配置管理**: 可配置的SMTP设置

### 技术特性
- **安全加密**: AES、RSA、TLS等多重加密技术展示
- **高性能**: Next.js优化的服务端渲染
- **可扩展**: 模块化的组件架构
- **测试覆盖**: 完整的单元测试和集成测试

## 🛠 技术栈

- **前端框架**: Next.js 14 (Pages Router)
- **UI框架**: Tailwind CSS
- **语言**: TypeScript
- **国际化**: next-i18next
- **表单处理**: React Hook Form + Zod
- **图标**: Heroicons
- **实时通信**: Socket.IO
- **邮件服务**: Nodemailer
- **认证**: JWT
- **测试**: Jest + Testing Library
- **部署**: Docker + Docker Compose

## 📦 安装和运行

### 环境要求
- Node.js 18+
- npm 或 yarn
- Docker (推荐，用于生产部署)

### 🐳 Docker 一键部署 (推荐)

最简单的部署方式，适合生产环境：

```bash
# 克隆项目
git clone <repository-url>
cd vpl-website

# 一键部署生产环境
./deploy.sh

# 或部署开发环境
./deploy.sh -e development
```

部署完成后访问：
- **主网站**: http://localhost:3000
- **管理后台**: http://localhost:3000/admin
- **开发工具** (仅开发环境):
  - pgAdmin: http://localhost:8080
  - Redis Commander: http://localhost:8081

详细的Docker部署指南请参考：[DOCKER_DEPLOYMENT.md](./DOCKER_DEPLOYMENT.md)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd vpl-website
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问应用**
- 前端: http://localhost:3000
- 管理后台: http://localhost:3000/admin/login

### 生产部署

1. **使用部署脚本**
```bash
./deploy.sh
```

2. **或手动部署**
```bash
# 构建应用
npm run build

# 使用Docker Compose部署
docker-compose up -d
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DATABASE_URL` | 数据库连接字符串 | - |
| `SMTP_HOST` | SMTP服务器地址 | smtp.gmail.com |
| `SMTP_PORT` | SMTP端口 | 587 |
| `SMTP_USER` | SMTP用户名 | - |
| `SMTP_PASS` | SMTP密码 | - |
| `SMTP_FROM` | 发件人邮箱 | <EMAIL> |
| `ADMIN_EMAIL` | 管理员邮箱 | <EMAIL> |
| `NEXTAUTH_SECRET` | NextAuth密钥 | - |
| `JWT_SECRET` | JWT密钥 | - |
| `ADMIN_USERNAME` | 管理员用户名 | admin |
| `ADMIN_PASSWORD` | 管理员密码 | admin123 |

### 邮件配置

在管理后台可以配置SMTP设置：
1. 登录管理后台
2. 进入系统设置
3. 配置SMTP服务器信息
4. 测试邮件发送功能

## 🧪 测试

```bash
# 运行所有测试
npm run test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage

# CI环境测试
npm run test:ci
```

## 📁 项目结构

```
vpl-website/
├── public/                 # 静态资源
│   └── locales/           # 多语言文件
├── src/
│   ├── components/        # React组件
│   │   ├── admin/        # 管理后台组件
│   │   ├── forms/        # 表单组件
│   │   ├── layout/       # 布局组件
│   │   └── ui/           # UI组件
│   ├── lib/              # 工具库
│   ├── pages/            # 页面文件
│   │   ├── admin/        # 管理后台页面
│   │   ├── api/          # API路由
│   │   └── services/     # 服务页面
│   ├── types/            # TypeScript类型定义
│   └── __tests__/        # 测试文件
├── docker-compose.yml    # Docker Compose配置
├── Dockerfile           # Docker镜像配置
├── deploy.sh           # 部署脚本
└── README.md           # 项目文档
```

## 🌐 多语言支持

网站支持三种语言：
- **中文** (zh) - 默认语言
- **英文** (en)
- **俄语** (ru)

语言文件位于 `public/locales/` 目录下，可以根据需要添加新的语言支持。

## 🔐 安全特性

- **JWT认证**: 管理员登录使用JWT令牌
- **密码加密**: 使用bcrypt加密存储密码
- **CSRF保护**: 表单提交包含CSRF保护
- **输入验证**: 前后端双重数据验证
- **权限控制**: 管理后台访问权限控制

## 📧 联系表单功能

- **多种联系方式**: 电话、邮箱、微信、QQ
- **服务类型选择**: 外贸网络线路、跨境电商线路、VPN服务、定制解决方案
- **验证码验证**: 字母数字混合验证码
- **实时通知**: 新提交立即通知管理员
- **邮件确认**: 自动发送确认邮件给客户和管理员

## 🚀 部署建议

### 生产环境
1. 使用HTTPS证书
2. 配置CDN加速
3. 设置数据库备份
4. 配置监控和日志
5. 使用负载均衡器

### 性能优化
1. 启用Next.js图片优化
2. 配置缓存策略
3. 使用Redis缓存
4. 优化数据库查询
5. 启用Gzip压缩

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或需要支持，请联系：
- 邮箱: <EMAIL>
- 电话: +86 400-xxx-xxxx

---

© 2024 VPL. 保留所有权利。
