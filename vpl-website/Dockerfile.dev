# VPL Website Development Docker Configuration
FROM node:20-alpine

# Install system dependencies
RUN apk add --no-cache libc6-compat curl wget

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm ci

# Create necessary directories with proper permissions
RUN mkdir -p /app/public/uploads/services
RUN mkdir -p /app/logs
RUN chown -R nextjs:nodejs /app

# Copy source code
COPY --chown=nextjs:nodejs . .

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=development
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# Start development server
CMD ["npm", "run", "dev"]
