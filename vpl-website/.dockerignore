# VPL Website Docker Ignore File
# This file specifies which files and directories should be excluded from Docker builds

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build output
.next/
out/

# Production build
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Next.js cache
.next

# Temporary folders
tmp/
temp/

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
DOCKER_DEPLOYMENT.md
*.md

# Scripts
scripts/
deploy.sh

# Backups
backups/

# Test files
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx
__tests__/
jest.config.js
jest.setup.js

# Development tools
.eslintrc.json
.prettierrc
.prettierignore

# Data files (development only)
data/

# Uploads (will be handled by volumes)
public/uploads/

# Vercel
.vercel

# Archive files
*.tgz
*.tar.gz
