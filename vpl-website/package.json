{"name": "vpl-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.12.0", "@types/formidable": "^3.4.5", "@types/papaparse": "^5.3.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "formidable": "^3.5.4", "framer-motion": "^12.23.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.4.2", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "nodemailer": "^6.10.1", "papaparse": "^5.5.3", "prisma": "^6.12.0", "react": "19.1.0", "react-csv": "^2.2.2", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-intersection-observer": "^9.16.0", "recharts": "^3.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "typescript": "^5"}}