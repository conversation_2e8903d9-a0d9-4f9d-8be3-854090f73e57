# VPL Website Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=production
NEXTAUTH_URL=http://localhost:3000

# =============================================================================
# SECURITY SECRETS (REQUIRED - Generate strong random values)
# =============================================================================
# Generate with: openssl rand -base64 32
NEXTAUTH_SECRET=your-nextauth-secret-here
JWT_SECRET=your-jwt-secret-here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database Password (REQUIRED)
POSTGRES_PASSWORD=your-secure-postgres-password

# Full database URL (auto-generated in Docker, but can be overridden)
# DATABASE_URL=postgresql://vpl_user:${POSTGRES_PASSWORD}@postgres:5432/vpl_website

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis Password (REQUIRED for production)
REDIS_PASSWORD=your-secure-redis-password

# Redis URL (auto-generated in Docker, but can be overridden)
# REDIS_URL=redis://redis:6379

# =============================================================================
# ADMIN ACCOUNT SETTINGS
# =============================================================================
ADMIN_EMAIL=<EMAIL>
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-admin-password

# =============================================================================
# EMAIL CONFIGURATION (SMTP)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================
# Maximum file size for uploads (in bytes)
MAX_FILE_SIZE=5242880

# Allowed file types for uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/gif

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable/disable request logging
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# DEVELOPMENT SETTINGS (for development environment only)
# =============================================================================
# Set to true to enable debug mode
DEBUG=false

# Set to true to enable verbose logging
VERBOSE_LOGGING=false

# Socket.IO Configuration
SOCKET_IO_PORT=3001
