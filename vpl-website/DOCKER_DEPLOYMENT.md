# VPL Website Docker Deployment Guide

This guide provides comprehensive instructions for deploying the VPL Website using Docker containers.

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+ installed
- Docker Compose 2.0+ installed
- At least 4GB RAM available
- 10GB free disk space

### One-Click Deployment

```bash
# Clone the repository
git clone <repository-url>
cd vpl-website

# Deploy production environment
./deploy.sh

# Or deploy development environment
./deploy.sh -e development
```

That's it! The application will be available at http://localhost:3000

## 📋 Table of Contents

- [System Requirements](#system-requirements)
- [Installation](#installation)
- [Configuration](#configuration)
- [Deployment Options](#deployment-options)
- [Service Management](#service-management)
- [Database Management](#database-management)
- [Monitoring & Health Checks](#monitoring--health-checks)
- [Backup & Restore](#backup--restore)
- [Troubleshooting](#troubleshooting)
- [Security Considerations](#security-considerations)

## 🔧 System Requirements

### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 10GB free space
- **OS**: Linux, macOS, or Windows with WSL2

### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 20GB+ SSD
- **Network**: Stable internet connection

### Software Dependencies
- Docker Engine 20.10+
- Docker Compose 2.0+
- Git
- curl (for health checks)

## 📦 Installation

### 1. Install Docker

#### Ubuntu/Debian
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

#### macOS
```bash
# Install Docker Desktop from https://docker.com/products/docker-desktop
# Or using Homebrew
brew install --cask docker
```

#### Windows
Download and install Docker Desktop from https://docker.com/products/docker-desktop

### 2. Verify Installation
```bash
docker --version
docker-compose --version
```

### 3. Clone Repository
```bash
git clone <repository-url>
cd vpl-website
```

## ⚙️ Configuration

### Environment Variables

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

#### Required Variables
```bash
# Database
POSTGRES_PASSWORD=your-secure-password

# Redis
REDIS_PASSWORD=your-redis-password

# Security
NEXTAUTH_SECRET=your-nextauth-secret
JWT_SECRET=your-jwt-secret

# Admin Account
ADMIN_PASSWORD=your-admin-password

# Email (Optional)
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

#### Auto-Generation
The deployment script can automatically generate secure passwords:

```bash
./deploy.sh  # Will prompt to generate .env if it doesn't exist
```

### Email Configuration

For email functionality, configure SMTP settings in `.env`:

```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password  # Use App Password for Gmail
SMTP_FROM=<EMAIL>
```

## 🚀 Deployment Options

### Production Deployment

```bash
# Full production deployment
./deploy.sh

# With backup before deployment
./deploy.sh --backup

# Force rebuild images
./deploy.sh --rebuild

# Skip system checks
./deploy.sh --skip-checks
```

### Development Deployment

```bash
# Development environment with hot reload
./deploy.sh -e development

# Development with additional tools (pgAdmin, Redis Commander)
./deploy.sh -e development --rebuild
```

### Manual Deployment

```bash
# Production
docker-compose up -d

# Development
docker-compose -f docker-compose.dev.yml up -d
```

## 🔧 Service Management

### Using Service Management Script

```bash
# Start all services
./scripts/service-manage.sh start

# Stop all services
./scripts/service-manage.sh stop

# Restart specific service
./scripts/service-manage.sh restart app

# View logs
./scripts/service-manage.sh logs -f

# Check service health
./scripts/service-manage.sh health

# Scale application
./scripts/service-manage.sh scale app 3
```

### Manual Service Management

```bash
# View running containers
docker-compose ps

# View logs
docker-compose logs -f app

# Restart services
docker-compose restart

# Stop and remove containers
docker-compose down

# Stop and remove everything including volumes
docker-compose down -v
```

## 🗄️ Database Management

### Using Database Management Script

```bash
# Create backup
./scripts/db-manage.sh backup

# Create backup with custom name
./scripts/db-manage.sh backup my-backup.sql

# Restore from backup
./scripts/db-manage.sh restore backup.sql

# Reset database to initial state
./scripts/db-manage.sh reset

# Open database shell
./scripts/db-manage.sh shell

# Check database status
./scripts/db-manage.sh status
```

### Manual Database Operations

```bash
# Connect to database
docker exec -it vpl-website-postgres psql -U vpl_user -d vpl_website

# Create backup
docker exec vpl-website-postgres pg_dump -U vpl_user vpl_website > backup.sql

# Restore backup
docker exec -i vpl-website-postgres psql -U vpl_user -d vpl_website < backup.sql
```

## 📊 Monitoring & Health Checks

### Health Check Endpoints

- **Application Health**: http://localhost:3000/api/health
- **Admin Dashboard**: http://localhost:3000/admin
- **Main Website**: http://localhost:3000

### Development Tools (Development Environment Only)

- **pgAdmin**: http://localhost:8080
  - Email: <EMAIL>
  - Password: admin123

- **Redis Commander**: http://localhost:8081

### Container Health Status

```bash
# Check container health
docker ps

# View health check logs
docker inspect vpl-website-app --format='{{.State.Health}}'

# Monitor resource usage
docker stats
```

## 💾 Backup & Restore

### Automated Backups

```bash
# Create backup before deployment
./deploy.sh --backup

# Manual backup
./scripts/db-manage.sh backup
```

### Backup Contents

- Database dump (PostgreSQL)
- Uploaded files (images, documents)
- Configuration files

### Restore Process

```bash
# Restore database
./scripts/db-manage.sh restore backup_20240101_120000.sql

# Restore uploaded files (if needed)
docker run --rm -v vpl-website_uploads:/data -v $(pwd)/backups:/backup alpine \
  tar xzf /backup/uploads.tar.gz -C /data
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using port 3000
lsof -i :3000

# Kill the process
kill -9 <PID>

# Or use different port
PORT=3001 docker-compose up -d
```

#### 2. Database Connection Failed
```bash
# Check database container
docker logs vpl-website-postgres

# Restart database
docker-compose restart postgres

# Check database connectivity
docker exec vpl-website-postgres pg_isready -U vpl_user
```

#### 3. Application Won't Start
```bash
# Check application logs
docker logs vpl-website-app

# Check environment variables
docker exec vpl-website-app env | grep -E "(DATABASE|REDIS|NEXTAUTH)"

# Restart application
docker-compose restart app
```

#### 4. Out of Disk Space
```bash
# Clean up Docker resources
./scripts/service-manage.sh clean

# Remove unused images
docker image prune -a

# Remove unused volumes
docker volume prune
```

### Log Analysis

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f app

# View last 100 lines
docker-compose logs --tail=100 app

# Search logs for errors
docker-compose logs app | grep -i error
```

### Performance Issues

```bash
# Check resource usage
docker stats

# Check disk usage
docker system df

# Optimize database
./scripts/db-manage.sh shell
# Then run: VACUUM ANALYZE;
```

## 🔒 Security Considerations

### Environment Security

1. **Never commit `.env` files** to version control
2. **Use strong passwords** for all services
3. **Regularly rotate secrets** and passwords
4. **Limit network exposure** in production

### Production Security Checklist

- [ ] Change default admin password
- [ ] Configure firewall rules
- [ ] Enable HTTPS with SSL certificates
- [ ] Set up log monitoring
- [ ] Configure backup encryption
- [ ] Review and update dependencies regularly

### Network Security

```bash
# Production: Don't expose database ports
# Remove these lines from docker-compose.yml:
# ports:
#   - "5432:5432"  # PostgreSQL
#   - "6379:6379"  # Redis
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [PostgreSQL Docker Guide](https://hub.docker.com/_/postgres)

## 🆘 Getting Help

If you encounter issues:

1. Check the [Troubleshooting](#troubleshooting) section
2. Review container logs: `docker-compose logs`
3. Check system resources: `docker stats`
4. Verify configuration: `cat .env`
5. Create an issue with detailed error logs

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.
