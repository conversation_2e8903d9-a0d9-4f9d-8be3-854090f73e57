{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/services/index.ts"], "sourcesContent": ["import { NextApiRequest, NextApiResponse } from 'next';\nimport { z } from 'zod';\n\n// Service content schema for validation\nconst ServiceSchema = z.object({\n  name: z.string().min(1, '服务名称不能为空'),\n  slug: z.string().min(1, 'URL标识不能为空').regex(/^[a-z0-9_-]+$/, 'URL标识只能包含小写字母、数字、下划线和连字符'),\n  title: z.string().min(1, '页面标题不能为空'),\n  description: z.string().min(1, '服务描述不能为空'),\n  content: z.string().optional(),\n  features: z.array(z.string()).default([]),\n  status: z.enum(['active', 'inactive', 'draft']).default('draft'),\n  sortOrder: z.number().default(0),\n  seoTitle: z.string().optional(),\n  seoDescription: z.string().optional(),\n  seoKeywords: z.string().optional(),\n  images: z.array(z.string()).default([]),\n  tags: z.array(z.string()).default([]),\n});\n\nconst UpdateServiceSchema = ServiceSchema.partial().extend({\n  id: z.string().min(1, '服务ID不能为空')\n});\n\n// Mock database - in production, this would be a real database\nlet services = [\n  {\n    id: '1',\n    name: 'VPN服务',\n    slug: 'vpn_services',\n    title: 'VPN服务 - 安全可靠的网络连接',\n    description: '军用级加密的VPN连接服务，保护您的数据安全',\n    content: '<h2>VPN服务详情</h2><p>我们提供军用级加密的VPN连接服务...</p>',\n    features: ['军用级加密', '零日志政策', '全球服务器', '无限带宽'],\n    status: 'active' as const,\n    sortOrder: 1,\n    seoTitle: 'VPN服务 - 安全可靠的网络连接 | VPL',\n    seoDescription: '军用级加密的VPN连接服务，保护您的数据安全，零日志政策，全球服务器覆盖',\n    seoKeywords: 'VPN,网络安全,加密,隐私保护',\n    images: ['/images/services/vpn-hero.jpg'],\n    tags: ['网络安全', 'VPN', '加密'],\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z',\n    version: 1\n  },\n  {\n    id: '2',\n    name: '跨境电商专线',\n    slug: 'ecommerce_lines',\n    title: '跨境电商专线 - 优化购物体验',\n    description: '优化的跨境电商网络连接解决方案，提升用户购物体验',\n    content: '<h2>跨境电商专线详情</h2><p>专为跨境电商设计的网络解决方案...</p>',\n    features: ['多平台支持', '高可用性', '流量优化', '实时监控'],\n    status: 'active' as const,\n    sortOrder: 2,\n    seoTitle: '跨境电商专线 - 优化购物体验 | VPL',\n    seoDescription: '优化的跨境电商网络连接解决方案，提升用户购物体验，支持多平台',\n    seoKeywords: '跨境电商,网络专线,购物体验,电商优化',\n    images: ['/images/services/ecommerce-hero.jpg'],\n    tags: ['跨境电商', '网络专线', '购物优化'],\n    createdAt: '2024-01-14T10:00:00Z',\n    updatedAt: '2024-01-14T10:00:00Z',\n    version: 1\n  },\n  {\n    id: '3',\n    name: '外贸专线',\n    slug: 'foreign_trade_lines',\n    title: '外贸专线 - 全球贸易网络',\n    description: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',\n    content: '<h2>外贸专线详情</h2><p>专为外贸企业设计的高速稳定网络线路...</p>',\n    features: ['专用带宽', '全球覆盖', '低延迟', '企业级支持'],\n    status: 'active' as const,\n    sortOrder: 3,\n    seoTitle: '外贸专线 - 全球贸易网络 | VPL',\n    seoDescription: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',\n    seoKeywords: '外贸专线,全球贸易,网络线路,企业网络',\n    images: ['/images/services/trade-hero.jpg'],\n    tags: ['外贸', '专线', '全球网络'],\n    createdAt: '2024-01-13T10:00:00Z',\n    updatedAt: '2024-01-13T10:00:00Z',\n    version: 1\n  },\n  {\n    id: '4',\n    name: '定制解决方案',\n    slug: 'custom_solution',\n    title: '定制解决方案 - 专属网络方案',\n    description: '根据您的特殊需求定制专属网络解决方案',\n    content: '<h2>定制解决方案详情</h2><p>根据您的特殊需求定制专属网络解决方案...</p>',\n    features: ['定制设计', '专家咨询', '可扩展架构', '持续支持'],\n    status: 'active' as const,\n    sortOrder: 4,\n    seoTitle: '定制解决方案 - 专属网络方案 | VPL',\n    seoDescription: '根据您的特殊需求定制专属网络解决方案，专家咨询，可扩展架构',\n    seoKeywords: '定制方案,网络解决方案,专家咨询,企业定制',\n    images: ['/images/services/custom-hero.jpg'],\n    tags: ['定制', '解决方案', '专家咨询'],\n    createdAt: '2024-01-12T10:00:00Z',\n    updatedAt: '2024-01-12T10:00:00Z',\n    version: 1\n  }\n];\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  try {\n    // Simple auth check - in production, use proper JWT verification\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return res.status(401).json({ error: '未授权访问' });\n    }\n\n    switch (req.method) {\n      case 'GET':\n        return handleGet(req, res);\n      case 'POST':\n        return handlePost(req, res);\n      case 'PUT':\n        return handlePut(req, res);\n      case 'DELETE':\n        return handleDelete(req, res);\n      default:\n        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);\n        return res.status(405).json({ error: `方法 ${req.method} 不被允许` });\n    }\n  } catch (error) {\n    console.error('Services API error:', error);\n    return res.status(500).json({ error: '服务器内部错误' });\n  }\n}\n\nasync function handleGet(req: NextApiRequest, res: NextApiResponse) {\n  const { \n    page = '1', \n    limit = '10', \n    search = '', \n    status = '', \n    sortBy = 'sortOrder',\n    sortOrder = 'asc'\n  } = req.query;\n\n  let filteredServices = [...services];\n\n  // Search filter\n  if (search) {\n    const searchTerm = search.toString().toLowerCase();\n    filteredServices = filteredServices.filter(service =>\n      service.name.toLowerCase().includes(searchTerm) ||\n      service.description.toLowerCase().includes(searchTerm) ||\n      service.tags.some(tag => tag.toLowerCase().includes(searchTerm))\n    );\n  }\n\n  // Status filter\n  if (status && status !== 'all') {\n    filteredServices = filteredServices.filter(service => service.status === status);\n  }\n\n  // Sorting\n  filteredServices.sort((a, b) => {\n    const aValue = a[sortBy as keyof typeof a];\n    const bValue = b[sortBy as keyof typeof b];\n    \n    if (sortOrder === 'desc') {\n      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n    }\n    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n  });\n\n  // Pagination\n  const pageNum = parseInt(page.toString());\n  const limitNum = parseInt(limit.toString());\n  const startIndex = (pageNum - 1) * limitNum;\n  const endIndex = startIndex + limitNum;\n  const paginatedServices = filteredServices.slice(startIndex, endIndex);\n\n  return res.status(200).json({\n    services: paginatedServices,\n    pagination: {\n      page: pageNum,\n      limit: limitNum,\n      total: filteredServices.length,\n      totalPages: Math.ceil(filteredServices.length / limitNum)\n    }\n  });\n}\n\nasync function handlePost(req: NextApiRequest, res: NextApiResponse) {\n  try {\n    const validatedData = ServiceSchema.parse(req.body);\n    \n    // Check if slug already exists\n    const existingService = services.find(s => s.slug === validatedData.slug);\n    if (existingService) {\n      return res.status(400).json({ error: 'URL标识已存在，请使用其他标识' });\n    }\n\n    const newService = {\n      id: Date.now().toString(),\n      ...validatedData,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n\n    services.push(newService);\n\n    return res.status(201).json({\n      message: '服务创建成功',\n      service: newService\n    });\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return res.status(400).json({\n        error: '数据验证失败',\n        details: error.errors\n      });\n    }\n    throw error;\n  }\n}\n\nasync function handlePut(req: NextApiRequest, res: NextApiResponse) {\n  try {\n    const validatedData = UpdateServiceSchema.parse(req.body);\n    const { id, ...updateData } = validatedData;\n\n    const serviceIndex = services.findIndex(s => s.id === id);\n    if (serviceIndex === -1) {\n      return res.status(404).json({ error: '服务不存在' });\n    }\n\n    // Check if slug already exists (excluding current service)\n    if (updateData.slug) {\n      const existingService = services.find(s => s.slug === updateData.slug && s.id !== id);\n      if (existingService) {\n        return res.status(400).json({ error: 'URL标识已存在，请使用其他标识' });\n      }\n    }\n\n    const updatedService = {\n      ...services[serviceIndex],\n      ...updateData,\n      updatedAt: new Date().toISOString(),\n      version: services[serviceIndex].version + 1\n    };\n\n    services[serviceIndex] = updatedService;\n\n    return res.status(200).json({\n      message: '服务更新成功',\n      service: updatedService\n    });\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return res.status(400).json({\n        error: '数据验证失败',\n        details: error.errors\n      });\n    }\n    throw error;\n  }\n}\n\nasync function handleDelete(req: NextApiRequest, res: NextApiResponse) {\n  const { ids } = req.body;\n\n  if (!ids || !Array.isArray(ids)) {\n    return res.status(400).json({ error: '请提供要删除的服务ID列表' });\n  }\n\n  const deletedServices = [];\n  const notFoundIds = [];\n\n  for (const id of ids) {\n    const serviceIndex = services.findIndex(s => s.id === id);\n    if (serviceIndex !== -1) {\n      deletedServices.push(services[serviceIndex]);\n      services.splice(serviceIndex, 1);\n    } else {\n      notFoundIds.push(id);\n    }\n  }\n\n  return res.status(200).json({\n    message: `成功删除 ${deletedServices.length} 个服务`,\n    deletedServices,\n    notFoundIds: notFoundIds.length > 0 ? notFoundIds : undefined\n  });\n}\n"], "names": [], "mappings": ";;;AACA;;;;;;AAEA,wCAAwC;AACxC,MAAM,gBAAgB,sGAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,KAAK,CAAC,iBAAiB;IAC5D,OAAO,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,SAAS,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,UAAU,sGAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sGAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACxC,QAAQ,sGAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;QAAY;KAAQ,EAAE,OAAO,CAAC;IACxD,WAAW,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B,UAAU,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,gBAAgB,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,aAAa,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,QAAQ,sGAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sGAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACtC,MAAM,sGAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sGAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;AACtC;AAEA,MAAM,sBAAsB,cAAc,OAAO,GAAG,MAAM,CAAC;IACzD,IAAI,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACxB;AAEA,+DAA+D;AAC/D,IAAI,WAAW;IACb;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;YAAC;YAAS;YAAS;YAAS;SAAO;QAC7C,QAAQ;QACR,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,QAAQ;YAAC;SAAgC;QACzC,MAAM;YAAC;YAAQ;YAAO;SAAK;QAC3B,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;YAAC;YAAS;YAAQ;YAAQ;SAAO;QAC3C,QAAQ;QACR,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,QAAQ;YAAC;SAAsC;QAC/C,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;YAAC;YAAQ;YAAQ;YAAO;SAAQ;QAC1C,QAAQ;QACR,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,QAAQ;YAAC;SAAkC;QAC3C,MAAM;YAAC;YAAM;YAAM;SAAO;QAC1B,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;YAAC;YAAQ;YAAQ;YAAS;SAAO;QAC3C,QAAQ;QACR,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,QAAQ;YAAC;SAAmC;QAC5C,MAAM;YAAC;YAAM;YAAQ;SAAO;QAC5B,WAAW;QACX,WAAW;QACX,SAAS;IACX;CACD;AAEc,eAAe,QAAQ,GAAmB,EAAE,GAAoB;IAC7E,IAAI;QACF,iEAAiE;QACjE,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAAQ;QAC/C;QAEA,OAAQ,IAAI,MAAM;YAChB,KAAK;gBACH,OAAO,UAAU,KAAK;YACxB,KAAK;gBACH,OAAO,WAAW,KAAK;YACzB,KAAK;gBACH,OAAO,UAAU,KAAK;YACxB,KAAK;gBACH,OAAO,aAAa,KAAK;YAC3B;gBACE,IAAI,SAAS,CAAC,SAAS;oBAAC;oBAAO;oBAAQ;oBAAO;iBAAS;gBACvD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAAE,OAAO,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;gBAAC;QACjE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAAE,OAAO;QAAU;IACjD;AACF;AAEA,eAAe,UAAU,GAAmB,EAAE,GAAoB;IAChE,MAAM,EACJ,OAAO,GAAG,EACV,QAAQ,IAAI,EACZ,SAAS,EAAE,EACX,SAAS,EAAE,EACX,SAAS,WAAW,EACpB,YAAY,KAAK,EAClB,GAAG,IAAI,KAAK;IAEb,IAAI,mBAAmB;WAAI;KAAS;IAEpC,gBAAgB;IAChB,IAAI,QAAQ;QACV,MAAM,aAAa,OAAO,QAAQ,GAAG,WAAW;QAChD,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eACpC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC3C,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;IAExD;IAEA,gBAAgB;IAChB,IAAI,UAAU,WAAW,OAAO;QAC9B,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAC3E;IAEA,UAAU;IACV,iBAAiB,IAAI,CAAC,CAAC,GAAG;QACxB,MAAM,SAAS,CAAC,CAAC,OAAyB;QAC1C,MAAM,SAAS,CAAC,CAAC,OAAyB;QAE1C,IAAI,cAAc,QAAQ;YACxB,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;QACtD;QACA,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;IACtD;IAEA,aAAa;IACb,MAAM,UAAU,SAAS,KAAK,QAAQ;IACtC,MAAM,WAAW,SAAS,MAAM,QAAQ;IACxC,MAAM,aAAa,CAAC,UAAU,CAAC,IAAI;IACnC,MAAM,WAAW,aAAa;IAC9B,MAAM,oBAAoB,iBAAiB,KAAK,CAAC,YAAY;IAE7D,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;QAC1B,UAAU;QACV,YAAY;YACV,MAAM;YACN,OAAO;YACP,OAAO,iBAAiB,MAAM;YAC9B,YAAY,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;QAClD;IACF;AACF;AAEA,eAAe,WAAW,GAAmB,EAAE,GAAoB;IACjE,IAAI;QACF,MAAM,gBAAgB,cAAc,KAAK,CAAC,IAAI,IAAI;QAElD,+BAA+B;QAC/B,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc,IAAI;QACxE,IAAI,iBAAiB;YACnB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAAmB;QAC1D;QAEA,MAAM,aAAa;YACjB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,GAAG,aAAa;YAChB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;QACX;QAEA,SAAS,IAAI,CAAC;QAEd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,sGAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,OAAO;gBACP,SAAS,MAAM,MAAM;YACvB;QACF;QACA,MAAM;IACR;AACF;AAEA,eAAe,UAAU,GAAmB,EAAE,GAAoB;IAChE,IAAI;QACF,MAAM,gBAAgB,oBAAoB,KAAK,CAAC,IAAI,IAAI;QACxD,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,MAAM,eAAe,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,iBAAiB,CAAC,GAAG;YACvB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAAQ;QAC/C;QAEA,2DAA2D;QAC3D,IAAI,WAAW,IAAI,EAAE;YACnB,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,IAAI,IAAI,EAAE,EAAE,KAAK;YAClF,IAAI,iBAAiB;gBACnB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAAE,OAAO;gBAAmB;YAC1D;QACF;QAEA,MAAM,iBAAiB;YACrB,GAAG,QAAQ,CAAC,aAAa;YACzB,GAAG,UAAU;YACb,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS,QAAQ,CAAC,aAAa,CAAC,OAAO,GAAG;QAC5C;QAEA,QAAQ,CAAC,aAAa,GAAG;QAEzB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,sGAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,OAAO;gBACP,SAAS,MAAM,MAAM;YACvB;QACF;QACA,MAAM;IACR;AACF;AAEA,eAAe,aAAa,GAAmB,EAAE,GAAoB;IACnE,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,IAAI;IAExB,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM;QAC/B,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAAE,OAAO;QAAgB;IACvD;IAEA,MAAM,kBAAkB,EAAE;IAC1B,MAAM,cAAc,EAAE;IAEtB,KAAK,MAAM,MAAM,IAAK;QACpB,MAAM,eAAe,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,iBAAiB,CAAC,GAAG;YACvB,gBAAgB,IAAI,CAAC,QAAQ,CAAC,aAAa;YAC3C,SAAS,MAAM,CAAC,cAAc;QAChC,OAAO;YACL,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;QAC1B,SAAS,CAAC,KAAK,EAAE,gBAAgB,MAAM,CAAC,IAAI,CAAC;QAC7C;QACA,aAAa,YAAY,MAAM,GAAG,IAAI,cAAc;IACtD;AACF", "debugId": null}}]}