module.exports = {

"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsonwebtoken", () => require("jsonwebtoken"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/pages/api/admin/system-config.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>handler
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const CONFIG_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'system-config.json');
// Ensure data directory and file exist
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
        recursive: true
    });
}
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(CONFIG_FILE)) {
    // Create default system configurations
    const defaultConfigs = [
        // General Settings
        {
            id: '1',
            category: 'general',
            key: 'site_name',
            value: 'VPL - 专业网络线路服务商',
            description: '网站名称',
            type: 'string',
            isEditable: true
        },
        {
            id: '2',
            category: 'general',
            key: 'company_name',
            value: 'VPL网络科技有限公司',
            description: '公司名称',
            type: 'string',
            isEditable: true
        },
        {
            id: '3',
            category: 'general',
            key: 'contact_email',
            value: '<EMAIL>',
            description: '联系邮箱',
            type: 'string',
            isEditable: true
        },
        {
            id: '4',
            category: 'general',
            key: 'contact_phone',
            value: '+86 ************',
            description: '联系电话',
            type: 'string',
            isEditable: true
        },
        // Email Settings
        {
            id: '5',
            category: 'email',
            key: 'auto_reply_enabled',
            value: 'true',
            description: '启用自动回复邮件',
            type: 'boolean',
            isEditable: true
        },
        {
            id: '6',
            category: 'email',
            key: 'email_queue_enabled',
            value: 'true',
            description: '启用邮件队列',
            type: 'boolean',
            isEditable: true
        },
        // Notification Settings
        {
            id: '7',
            category: 'notifications',
            key: 'browser_notifications',
            value: 'true',
            description: '启用浏览器通知',
            type: 'boolean',
            isEditable: true
        },
        {
            id: '8',
            category: 'notifications',
            key: 'email_notifications',
            value: 'true',
            description: '启用邮件通知',
            type: 'boolean',
            isEditable: true
        },
        // Security Settings
        {
            id: '9',
            category: 'security',
            key: 'session_timeout',
            value: '3600',
            description: '会话超时时间（秒）',
            type: 'number',
            isEditable: true
        },
        {
            id: '10',
            category: 'security',
            key: 'max_login_attempts',
            value: '5',
            description: '最大登录尝试次数',
            type: 'number',
            isEditable: true
        },
        // Maintenance Settings
        {
            id: '11',
            category: 'maintenance',
            key: 'maintenance_mode',
            value: 'false',
            description: '维护模式',
            type: 'boolean',
            isEditable: true
        },
        {
            id: '12',
            category: 'maintenance',
            key: 'backup_enabled',
            value: 'true',
            description: '启用自动备份',
            type: 'boolean',
            isEditable: true
        }
    ];
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(CONFIG_FILE, JSON.stringify(defaultConfigs, null, 2));
}
async function handler(req, res) {
    // Verify admin token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
            success: false,
            message: 'No token provided'
        });
    }
    const token = authHeader.substring(7);
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__["default"].verify(token, process.env.JWT_SECRET || 'your-secret-key');
    } catch (jwtError) {
        return res.status(401).json({
            success: false,
            message: 'Invalid token'
        });
    }
    if (req.method === 'GET') {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(CONFIG_FILE, 'utf8');
            const configs = JSON.parse(data);
            return res.status(200).json({
                success: true,
                message: 'System configurations retrieved successfully',
                data: configs
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to retrieve system configurations',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else if (req.method === 'PUT') {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(CONFIG_FILE, 'utf8');
            const configs = JSON.parse(data);
            const { configs: updatedConfigs } = req.body;
            // Update configurations
            configs.forEach((config)=>{
                if (updatedConfigs.hasOwnProperty(config.key) && config.isEditable) {
                    let newValue = updatedConfigs[config.key];
                    // Convert value based on type
                    if (config.type === 'boolean') {
                        newValue = newValue.toString();
                    } else if (config.type === 'number') {
                        newValue = newValue.toString();
                    } else if (config.type === 'json') {
                        newValue = typeof newValue === 'object' ? JSON.stringify(newValue) : newValue;
                    }
                    config.value = newValue;
                    config.updatedAt = new Date().toISOString();
                    config.updatedBy = 'admin'; // TODO: Get from JWT token
                }
            });
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(CONFIG_FILE, JSON.stringify(configs, null, 2));
            return res.status(200).json({
                success: true,
                message: 'System configurations updated successfully',
                data: configs
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to update system configurations',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else {
        return res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e2f80f36._.js.map