{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/socket.ts"], "sourcesContent": ["import { NextApiRequest, NextApiResponse } from 'next';\nimport { Server as ServerIO } from 'socket.io';\nimport { Server as NetServer } from 'http';\n\nexport type NextApiResponseServerIO = NextApiResponse & {\n  socket: {\n    server: NetServer & {\n      io: ServerIO;\n    };\n  };\n};\n\nexport default function SocketHandler(req: NextApiRequest, res: NextApiResponseServerIO) {\n  if (res.socket.server.io) {\n    console.log('Socket.IO server already running');\n    res.end();\n    return;\n  }\n\n  console.log('Initializing Socket.IO server...');\n\n  const io = new ServerIO(res.socket.server, {\n    path: '/api/socket',\n    addTrailingSlash: false,\n    cors: {\n      origin: \"*\",\n      methods: [\"GET\", \"POST\"]\n    }\n  });\n\n  res.socket.server.io = io;\n\n  io.on('connection', (socket) => {\n    console.log('Client connected:', socket.id);\n\n    socket.on('join-admin', () => {\n      socket.join('admin');\n      console.log('Admin joined:', socket.id);\n      socket.emit('admin-joined', { success: true });\n    });\n\n    socket.on('disconnect', () => {\n      console.log('Client disconnected:', socket.id);\n    });\n\n    socket.on('error', (error) => {\n      console.error('Socket error:', error);\n    });\n  });\n\n  console.log('Socket.IO server initialized successfully');\n  res.end();\n}\n\nexport const config = {\n  api: {\n    bodyParser: false,\n  },\n};\n"], "names": [], "mappings": ";;;;AACA;;;;;;AAWe,SAAS,cAAc,GAAmB,EAAE,GAA4B;IACrF,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE;QACxB,QAAQ,GAAG,CAAC;QACZ,IAAI,GAAG;QACP;IACF;IAEA,QAAQ,GAAG,CAAC;IAEZ,MAAM,KAAK,IAAI,wHAAA,CAAA,SAAQ,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;QACzC,MAAM;QACN,kBAAkB;QAClB,MAAM;YACJ,QAAQ;YACR,SAAS;gBAAC;gBAAO;aAAO;QAC1B;IACF;IAEA,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG;IAEvB,GAAG,EAAE,CAAC,cAAc,CAAC;QACnB,QAAQ,GAAG,CAAC,qBAAqB,OAAO,EAAE;QAE1C,OAAO,EAAE,CAAC,cAAc;YACtB,OAAO,IAAI,CAAC;YACZ,QAAQ,GAAG,CAAC,iBAAiB,OAAO,EAAE;YACtC,OAAO,IAAI,CAAC,gBAAgB;gBAAE,SAAS;YAAK;QAC9C;QAEA,OAAO,EAAE,CAAC,cAAc;YACtB,QAAQ,GAAG,CAAC,wBAAwB,OAAO,EAAE;QAC/C;QAEA,OAAO,EAAE,CAAC,SAAS,CAAC;YAClB,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,QAAQ,GAAG,CAAC;IACZ,IAAI,GAAG;AACT;AAEO,MAAM,SAAS;IACpB,KAAK;QACH,YAAY;IACd;AACF", "debugId": null}}]}