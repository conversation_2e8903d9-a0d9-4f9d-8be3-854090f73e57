{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/dashboard.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\n\ninterface DashboardData {\n  stats: {\n    totalInquiries: number;\n    todayInquiries: number;\n    pendingInquiries: number;\n    totalUsers: number;\n  };\n  recentSubmissions: Array<{\n    id: string;\n    companyName: string;\n    contactPerson: string;\n    email: string;\n    phone: string;\n    serviceType: string;\n    message: string;\n    submittedAt: string;\n    status: 'pending' | 'contacted' | 'closed';\n  }>;\n}\n\ninterface ApiResponse {\n  success: boolean;\n  message: string;\n  stats?: DashboardData['stats'];\n  recentSubmissions?: DashboardData['recentSubmissions'];\n}\n\n// Mock data - in a real application, this would come from a database\nconst mockSubmissions = [\n  {\n    id: '1',\n    companyName: '深圳贸易有限公司',\n    contactPerson: '张经理',\n    email: '<EMAIL>',\n    phone: '+86 138-0000-0000',\n    serviceType: 'foreign_trade_lines',\n    message: '我们需要稳定的外贸网络线路，主要连接欧美市场，请提供详细方案和报价。',\n    submittedAt: new Date().toISOString(),\n    status: 'pending' as const\n  },\n  {\n    id: '2',\n    companyName: '广州电商科技',\n    contactPerson: '李总',\n    email: '<EMAIL>',\n    phone: '+86 139-0000-0000',\n    serviceType: 'ecommerce_lines',\n    message: '跨境电商平台需要优化网络连接，提升用户访问速度。',\n    submittedAt: new Date(Date.now() - 86400000).toISOString(),\n    status: 'contacted' as const\n  },\n  {\n    id: '3',\n    companyName: '北京科技集团',\n    contactPerson: '王主管',\n    email: '<EMAIL>',\n    phone: '+86 137-0000-0000',\n    serviceType: 'vpn_services',\n    message: '企业需要安全的VPN服务，支持多设备接入。',\n    submittedAt: new Date(Date.now() - *********).toISOString(),\n    status: 'closed' as const\n  }\n];\n\nexport default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse<ApiResponse>\n) {\n  if (req.method !== 'GET') {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n\n  try {\n    // Verify admin token\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return res.status(401).json({\n        success: false,\n        message: 'No token provided'\n      });\n    }\n\n    const token = authHeader.substring(7);\n    \n    try {\n      jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n    } catch (jwtError) {\n      return res.status(401).json({\n        success: false,\n        message: 'Invalid token'\n      });\n    }\n\n    // In a real application, you would fetch this data from your database\n    // For now, we'll return mock data\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    \n    const todaySubmissions = mockSubmissions.filter(\n      submission => new Date(submission.submittedAt) >= today\n    );\n    \n    const pendingSubmissions = mockSubmissions.filter(\n      submission => submission.status === 'pending'\n    );\n\n    const stats = {\n      totalInquiries: mockSubmissions.length,\n      todayInquiries: todaySubmissions.length,\n      pendingInquiries: pendingSubmissions.length,\n      totalUsers: 156, // Mock data\n    };\n\n    return res.status(200).json({\n      success: true,\n      message: 'Dashboard data retrieved successfully',\n      stats,\n      recentSubmissions: mockSubmissions.slice(0, 10) // Return latest 10 submissions\n    });\n\n  } catch (error) {\n    console.error('Dashboard data error:', error);\n    return res.status(500).json({\n      success: false,\n      message: 'Internal server error'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AA6BA,qEAAqE;AACrE,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,aAAa;QACb,eAAe;QACf,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa,IAAI,OAAO,WAAW;QACnC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aAAa;QACb,eAAe;QACf,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,WAAW;QACxD,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aAAa;QACb,eAAe;QACf,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW;QACzD,QAAQ;IACV;CACD;AAEc,eAAe,QAC5B,GAAmB,EACnB,GAAiC;IAEjC,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI;QACF,qBAAqB;QACrB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QAEnC,IAAI;YACF,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC9C,EAAE,OAAO,UAAU;YACjB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,sEAAsE;QACtE,kCAAkC;QAClC,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;QAExB,MAAM,mBAAmB,gBAAgB,MAAM,CAC7C,CAAA,aAAc,IAAI,KAAK,WAAW,WAAW,KAAK;QAGpD,MAAM,qBAAqB,gBAAgB,MAAM,CAC/C,CAAA,aAAc,WAAW,MAAM,KAAK;QAGtC,MAAM,QAAQ;YACZ,gBAAgB,gBAAgB,MAAM;YACtC,gBAAgB,iBAAiB,MAAM;YACvC,kBAAkB,mBAAmB,MAAM;YAC3C,YAAY;QACd;QAEA,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;YACT;YACA,mBAAmB,gBAAgB,KAAK,CAAC,GAAG,IAAI,+BAA+B;QACjF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}