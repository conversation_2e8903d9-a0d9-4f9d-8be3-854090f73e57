{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/system/config.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\nimport { SystemConfig, ConfigResponse, ConfigUpdateRequest } from '@/types/system';\n\n// Mock data for system configurations\nconst mockConfigs: SystemConfig[] = [\n  // General Settings\n  {\n    id: '1',\n    category: 'general',\n    key: 'company_name',\n    value: 'VPL专业网络解决方案',\n    type: 'string',\n    label: '公司名称',\n    description: '网站显示的公司名称',\n    required: true,\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  {\n    id: '2',\n    category: 'general',\n    key: 'company_name_en',\n    value: 'VPL Professional Network Solutions',\n    type: 'string',\n    label: '公司名称（英文）',\n    required: true,\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  {\n    id: '3',\n    category: 'general',\n    key: 'company_phone',\n    value: '+86-************',\n    type: 'string',\n    label: '联系电话',\n    required: true,\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  {\n    id: '4',\n    category: 'general',\n    key: 'company_email',\n    value: '<EMAIL>',\n    type: 'email',\n    label: '联系邮箱',\n    required: true,\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  // Email Settings\n  {\n    id: '5',\n    category: 'email',\n    key: 'smtp_host',\n    value: 'smtp.gmail.com',\n    type: 'string',\n    label: 'SMTP服务器',\n    required: true,\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  {\n    id: '6',\n    category: 'email',\n    key: 'smtp_port',\n    value: 587,\n    type: 'number',\n    label: 'SMTP端口',\n    required: true,\n    validation: { min: 1, max: 65535 },\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  {\n    id: '7',\n    category: 'email',\n    key: 'smtp_secure',\n    value: false,\n    type: 'boolean',\n    label: '启用SSL/TLS',\n    required: false,\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  // Form Settings\n  {\n    id: '8',\n    category: 'form',\n    key: 'verification_code_expiry',\n    value: 10,\n    type: 'number',\n    label: '验证码有效期（分钟）',\n    required: true,\n    validation: { min: 1, max: 60 },\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  {\n    id: '9',\n    category: 'form',\n    key: 'max_message_length',\n    value: 1000,\n    type: 'number',\n    label: '消息最大长度',\n    required: true,\n    validation: { min: 100, max: 5000 },\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  // Security Settings\n  {\n    id: '10',\n    category: 'security',\n    key: 'session_timeout',\n    value: 480,\n    type: 'number',\n    label: '会话超时时间（分钟）',\n    required: true,\n    validation: { min: 30, max: 1440 },\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  },\n  {\n    id: '11',\n    category: 'security',\n    key: 'max_login_attempts',\n    value: 5,\n    type: 'number',\n    label: '最大登录尝试次数',\n    required: true,\n    validation: { min: 3, max: 10 },\n    updatedAt: new Date().toISOString(),\n    updatedBy: 'admin'\n  }\n];\n\nfunction verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n}\n\nexport default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse<ConfigResponse>\n) {\n  try {\n    // Verify authentication\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return res.status(401).json({\n        success: false,\n        message: 'No token provided'\n      });\n    }\n\n    const token = authHeader.substring(7);\n    const decoded = verifyToken(token);\n\n    if (req.method === 'GET') {\n      const { category } = req.query;\n      \n      let configs = mockConfigs;\n      if (category && typeof category === 'string') {\n        configs = mockConfigs.filter(config => config.category === category);\n      }\n\n      return res.status(200).json({\n        success: true,\n        message: 'Configurations retrieved successfully',\n        data: configs\n      });\n\n    } else if (req.method === 'PUT') {\n      const updateRequest: ConfigUpdateRequest = req.body;\n      \n      if (!updateRequest.configs || !Array.isArray(updateRequest.configs)) {\n        return res.status(400).json({\n          success: false,\n          message: 'Invalid request format'\n        });\n      }\n\n      // Validate and update configurations\n      const updatedConfigs: SystemConfig[] = [];\n      \n      for (const configUpdate of updateRequest.configs) {\n        const existingConfig = mockConfigs.find(c => c.key === configUpdate.key);\n        \n        if (!existingConfig) {\n          return res.status(400).json({\n            success: false,\n            message: `Configuration key '${configUpdate.key}' not found`\n          });\n        }\n\n        // Validate value based on type\n        if (!validateConfigValue(existingConfig, configUpdate.value)) {\n          return res.status(400).json({\n            success: false,\n            message: `Invalid value for '${configUpdate.key}'`\n          });\n        }\n\n        // Update the configuration\n        const updatedConfig: SystemConfig = {\n          ...existingConfig,\n          value: configUpdate.value,\n          updatedAt: new Date().toISOString(),\n          updatedBy: decoded.username\n        };\n\n        // Update in mock data\n        const index = mockConfigs.findIndex(c => c.key === configUpdate.key);\n        if (index !== -1) {\n          mockConfigs[index] = updatedConfig;\n        }\n\n        updatedConfigs.push(updatedConfig);\n      }\n\n      return res.status(200).json({\n        success: true,\n        message: 'Configurations updated successfully',\n        data: updatedConfigs\n      });\n\n    } else {\n      return res.status(405).json({\n        success: false,\n        message: 'Method not allowed'\n      });\n    }\n\n  } catch (error) {\n    console.error('System config error:', error);\n    return res.status(500).json({\n      success: false,\n      message: 'Internal server error'\n    });\n  }\n}\n\nfunction validateConfigValue(config: SystemConfig, value: any): boolean {\n  switch (config.type) {\n    case 'string':\n      if (typeof value !== 'string') return false;\n      if (config.required && !value.trim()) return false;\n      break;\n    \n    case 'number':\n      if (typeof value !== 'number' || isNaN(value)) return false;\n      if (config.validation?.min !== undefined && value < config.validation.min) return false;\n      if (config.validation?.max !== undefined && value > config.validation.max) return false;\n      break;\n    \n    case 'boolean':\n      if (typeof value !== 'boolean') return false;\n      break;\n    \n    case 'email':\n      if (typeof value !== 'string') return false;\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(value)) return false;\n      break;\n    \n    case 'url':\n      if (typeof value !== 'string') return false;\n      try {\n        new URL(value);\n      } catch {\n        return false;\n      }\n      break;\n    \n    default:\n      return true;\n  }\n  \n  return true;\n}\n"], "names": [], "mappings": ";;;AACA;;AAGA,sCAAsC;AACtC,MAAM,cAA8B;IAClC,mBAAmB;IACnB;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA,iBAAiB;IACjB;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,YAAY;YAAE,KAAK;YAAG,KAAK;QAAM;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA,gBAAgB;IAChB;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,YAAY;YAAE,KAAK;YAAG,KAAK;QAAG;QAC9B,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,YAAY;YAAE,KAAK;YAAK,KAAK;QAAK;QAClC,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA,oBAAoB;IACpB;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,YAAY;YAAE,KAAK;YAAI,KAAK;QAAK;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,YAAY;YAAE,KAAK;YAAG,KAAK;QAAG;QAC9B,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;CACD;AAED,SAAS,YAAY,KAAa;IAChC,IAAI;QACF,OAAO,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IACrD,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEe,eAAe,QAC5B,GAAmB,EACnB,GAAoC;IAEpC,IAAI;QACF,wBAAwB;QACxB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAE5B,IAAI,IAAI,MAAM,KAAK,OAAO;YACxB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,KAAK;YAE9B,IAAI,UAAU;YACd,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,UAAU,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;YAC7D;YAEA,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QAEF,OAAO,IAAI,IAAI,MAAM,KAAK,OAAO;YAC/B,MAAM,gBAAqC,IAAI,IAAI;YAEnD,IAAI,CAAC,cAAc,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,GAAG;gBACnE,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,qCAAqC;YACrC,MAAM,iBAAiC,EAAE;YAEzC,KAAK,MAAM,gBAAgB,cAAc,OAAO,CAAE;gBAChD,MAAM,iBAAiB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,aAAa,GAAG;gBAEvE,IAAI,CAAC,gBAAgB;oBACnB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;wBAC1B,SAAS;wBACT,SAAS,CAAC,mBAAmB,EAAE,aAAa,GAAG,CAAC,WAAW,CAAC;oBAC9D;gBACF;gBAEA,+BAA+B;gBAC/B,IAAI,CAAC,oBAAoB,gBAAgB,aAAa,KAAK,GAAG;oBAC5D,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;wBAC1B,SAAS;wBACT,SAAS,CAAC,mBAAmB,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC;oBACpD;gBACF;gBAEA,2BAA2B;gBAC3B,MAAM,gBAA8B;oBAClC,GAAG,cAAc;oBACjB,OAAO,aAAa,KAAK;oBACzB,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,sBAAsB;gBACtB,MAAM,QAAQ,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,aAAa,GAAG;gBACnE,IAAI,UAAU,CAAC,GAAG;oBAChB,WAAW,CAAC,MAAM,GAAG;gBACvB;gBAEA,eAAe,IAAI,CAAC;YACtB;YAEA,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QAEF,OAAO;YACL,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEA,SAAS,oBAAoB,MAAoB,EAAE,KAAU;IAC3D,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,IAAI,OAAO,UAAU,UAAU,OAAO;YACtC,IAAI,OAAO,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC7C;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,OAAO;YACtD,IAAI,OAAO,UAAU,EAAE,QAAQ,aAAa,QAAQ,OAAO,UAAU,CAAC,GAAG,EAAE,OAAO;YAClF,IAAI,OAAO,UAAU,EAAE,QAAQ,aAAa,QAAQ,OAAO,UAAU,CAAC,GAAG,EAAE,OAAO;YAClF;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,WAAW,OAAO;YACvC;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,UAAU,OAAO;YACtC,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,OAAO;YACpC;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,UAAU,OAAO;YACtC,IAAI;gBACF,IAAI,IAAI;YACV,EAAE,OAAM;gBACN,OAAO;YACT;YACA;QAEF;YACE,OAAO;IACX;IAEA,OAAO;AACT", "debugId": null}}]}