{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/lib/socket.ts"], "sourcesContent": ["import { Server as NetServer } from 'http';\nimport { NextApiRequest, NextApiResponse } from 'next';\nimport { Server as ServerIO } from 'socket.io';\n\nexport type NextApiResponseServerIO = NextApiResponse & {\n  socket: {\n    server: NetServer & {\n      io: ServerIO;\n    };\n  };\n};\n\nexport const config = {\n  api: {\n    bodyParser: false,\n  },\n};\n\nconst SocketHandler = (req: NextApiRequest, res: NextApiResponseServerIO) => {\n  if (res.socket.server.io) {\n    console.log('Socket is already running');\n  } else {\n    console.log('Socket is initializing');\n    const io = new ServerIO(res.socket.server);\n    res.socket.server.io = io;\n\n    io.on('connection', (socket) => {\n      console.log('Client connected:', socket.id);\n\n      socket.on('join-admin', () => {\n        socket.join('admin');\n        console.log('Admin joined:', socket.id);\n      });\n\n      socket.on('disconnect', () => {\n        console.log('Client disconnected:', socket.id);\n      });\n    });\n  }\n  res.end();\n};\n\nexport default SocketHandler;\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAUO,MAAM,SAAS;IACpB,KAAK;QACH,YAAY;IACd;AACF;AAEA,MAAM,gBAAgB,CAAC,KAAqB;IAC1C,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE;QACxB,QAAQ,GAAG,CAAC;IACd,OAAO;QACL,QAAQ,GAAG,CAAC;QACZ,MAAM,KAAK,IAAI,wHAAA,CAAA,SAAQ,CAAC,IAAI,MAAM,CAAC,MAAM;QACzC,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG;QAEvB,GAAG,EAAE,CAAC,cAAc,CAAC;YACnB,QAAQ,GAAG,CAAC,qBAAqB,OAAO,EAAE;YAE1C,OAAO,EAAE,CAAC,cAAc;gBACtB,OAAO,IAAI,CAAC;gBACZ,QAAQ,GAAG,CAAC,iBAAiB,OAAO,EAAE;YACxC;YAEA,OAAO,EAAE,CAAC,cAAc;gBACtB,QAAQ,GAAG,CAAC,wBAAwB,OAAO,EAAE;YAC/C;QACF;IACF;IACA,IAAI,GAAG;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/socket.ts"], "sourcesContent": ["import SocketHandler from '../../lib/socket';\n\nexport default SocketHandler;\nexport { config } from '../../lib/socket';\n"], "names": [], "mappings": ";;;AAAA;;;;;;uCAEe,6GAAA,CAAA,UAAa", "debugId": null}}]}