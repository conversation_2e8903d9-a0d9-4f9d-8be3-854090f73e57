{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/lib/dataService.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { ContactSubmission, SubmissionFilters, PaginationParams, ApiResponse } from '../types/admin';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst SUBMISSIONS_FILE = path.join(DATA_DIR, 'submissions.json');\nconst USERS_FILE = path.join(DATA_DIR, 'users.json');\nconst CONFIG_FILE = path.join(DATA_DIR, 'config.json');\n\n// Ensure data directory exists\nif (!fs.existsSync(DATA_DIR)) {\n  fs.mkdirSync(DATA_DIR, { recursive: true });\n}\n\n// Initialize files if they don't exist\nconst initializeFiles = () => {\n  if (!fs.existsSync(SUBMISSIONS_FILE)) {\n    fs.writeFileSync(SUBMISSIONS_FILE, JSON.stringify([], null, 2));\n  }\n  if (!fs.existsSync(USERS_FILE)) {\n    fs.writeFileSync(USERS_FILE, JSON.stringify([], null, 2));\n  }\n  if (!fs.existsSync(CONFIG_FILE)) {\n    fs.writeFileSync(CONFIG_FILE, JSON.stringify({}, null, 2));\n  }\n};\n\ninitializeFiles();\n\n// Contact Submissions Service\nexport class ContactSubmissionService {\n  static async getAll(\n    filters?: SubmissionFilters,\n    pagination?: PaginationParams\n  ): Promise<ApiResponse<ContactSubmission[]>> {\n    try {\n      const data = fs.readFileSync(SUBMISSIONS_FILE, 'utf8');\n      let submissions: ContactSubmission[] = JSON.parse(data);\n\n      // Apply filters\n      if (filters) {\n        submissions = this.applyFilters(submissions, filters);\n      }\n\n      // Apply sorting\n      if (pagination?.sortBy) {\n        submissions = this.applySorting(submissions, pagination.sortBy, pagination.sortOrder || 'desc');\n      }\n\n      // Calculate pagination\n      const total = submissions.length;\n      const page = pagination?.page || 1;\n      const limit = pagination?.limit || 10;\n      const totalPages = Math.ceil(total / limit);\n      const startIndex = (page - 1) * limit;\n      const endIndex = startIndex + limit;\n\n      const paginatedSubmissions = submissions.slice(startIndex, endIndex);\n\n      return {\n        success: true,\n        message: 'Submissions retrieved successfully',\n        data: paginatedSubmissions,\n        pagination: {\n          page,\n          limit,\n          total,\n          totalPages,\n        },\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Failed to retrieve submissions',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  static async getById(id: string): Promise<ApiResponse<ContactSubmission>> {\n    try {\n      const data = fs.readFileSync(SUBMISSIONS_FILE, 'utf8');\n      const submissions: ContactSubmission[] = JSON.parse(data);\n      const submission = submissions.find(s => s.id === id);\n\n      if (!submission) {\n        return {\n          success: false,\n          message: 'Submission not found',\n        };\n      }\n\n      return {\n        success: true,\n        message: 'Submission retrieved successfully',\n        data: submission,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Failed to retrieve submission',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  static async create(submission: Omit<ContactSubmission, 'id' | 'submittedAt' | 'status'>): Promise<ApiResponse<ContactSubmission>> {\n    try {\n      const data = fs.readFileSync(SUBMISSIONS_FILE, 'utf8');\n      const submissions: ContactSubmission[] = JSON.parse(data);\n\n      const newSubmission: ContactSubmission = {\n        ...submission,\n        id: Date.now().toString(),\n        submittedAt: new Date().toISOString(),\n        status: 'pending',\n        priority: 'medium',\n        source: 'website',\n      };\n\n      submissions.push(newSubmission);\n      fs.writeFileSync(SUBMISSIONS_FILE, JSON.stringify(submissions, null, 2));\n\n      return {\n        success: true,\n        message: 'Submission created successfully',\n        data: newSubmission,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Failed to create submission',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  static async update(id: string, updates: Partial<ContactSubmission>): Promise<ApiResponse<ContactSubmission>> {\n    try {\n      const data = fs.readFileSync(SUBMISSIONS_FILE, 'utf8');\n      const submissions: ContactSubmission[] = JSON.parse(data);\n      const index = submissions.findIndex(s => s.id === id);\n\n      if (index === -1) {\n        return {\n          success: false,\n          message: 'Submission not found',\n        };\n      }\n\n      submissions[index] = { ...submissions[index], ...updates };\n      fs.writeFileSync(SUBMISSIONS_FILE, JSON.stringify(submissions, null, 2));\n\n      return {\n        success: true,\n        message: 'Submission updated successfully',\n        data: submissions[index],\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Failed to update submission',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  static async delete(id: string): Promise<ApiResponse<void>> {\n    try {\n      const data = fs.readFileSync(SUBMISSIONS_FILE, 'utf8');\n      const submissions: ContactSubmission[] = JSON.parse(data);\n      const filteredSubmissions = submissions.filter(s => s.id !== id);\n\n      if (submissions.length === filteredSubmissions.length) {\n        return {\n          success: false,\n          message: 'Submission not found',\n        };\n      }\n\n      fs.writeFileSync(SUBMISSIONS_FILE, JSON.stringify(filteredSubmissions, null, 2));\n\n      return {\n        success: true,\n        message: 'Submission deleted successfully',\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Failed to delete submission',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  static async bulkUpdate(ids: string[], updates: Partial<ContactSubmission>): Promise<ApiResponse<ContactSubmission[]>> {\n    try {\n      const data = fs.readFileSync(SUBMISSIONS_FILE, 'utf8');\n      const submissions: ContactSubmission[] = JSON.parse(data);\n      const updatedSubmissions: ContactSubmission[] = [];\n\n      submissions.forEach(submission => {\n        if (ids.includes(submission.id)) {\n          Object.assign(submission, updates);\n          updatedSubmissions.push(submission);\n        }\n      });\n\n      fs.writeFileSync(SUBMISSIONS_FILE, JSON.stringify(submissions, null, 2));\n\n      return {\n        success: true,\n        message: `${updatedSubmissions.length} submissions updated successfully`,\n        data: updatedSubmissions,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Failed to update submissions',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  private static applyFilters(submissions: ContactSubmission[], filters: SubmissionFilters): ContactSubmission[] {\n    return submissions.filter(submission => {\n      // Status filter\n      if (filters.status && filters.status.length > 0 && !filters.status.includes(submission.status)) {\n        return false;\n      }\n\n      // Service type filter\n      if (filters.serviceType && filters.serviceType.length > 0 && !filters.serviceType.includes(submission.serviceType)) {\n        return false;\n      }\n\n      // Priority filter\n      if (filters.priority && filters.priority.length > 0 && !filters.priority.includes(submission.priority)) {\n        return false;\n      }\n\n      // Date range filter\n      if (filters.dateRange) {\n        const submissionDate = new Date(submission.submittedAt);\n        const startDate = new Date(filters.dateRange.start);\n        const endDate = new Date(filters.dateRange.end);\n        if (submissionDate < startDate || submissionDate > endDate) {\n          return false;\n        }\n      }\n\n      // Assigned to filter\n      if (filters.assignedTo && submission.assignedTo !== filters.assignedTo) {\n        return false;\n      }\n\n      // Search filter\n      if (filters.search) {\n        const searchTerm = filters.search.toLowerCase();\n        const searchableFields = [\n          submission.companyName,\n          submission.contactPerson,\n          submission.email,\n          submission.phone,\n          submission.message,\n        ];\n        if (!searchableFields.some(field => field?.toLowerCase().includes(searchTerm))) {\n          return false;\n        }\n      }\n\n      // Tags filter\n      if (filters.tags && filters.tags.length > 0) {\n        if (!submission.tags || !filters.tags.some(tag => submission.tags!.includes(tag))) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  }\n\n  private static applySorting(submissions: ContactSubmission[], sortBy: string, sortOrder: 'asc' | 'desc'): ContactSubmission[] {\n    return submissions.sort((a, b) => {\n      let aValue: any = a[sortBy as keyof ContactSubmission];\n      let bValue: any = b[sortBy as keyof ContactSubmission];\n\n      // Handle date fields\n      if (sortBy.includes('At') || sortBy.includes('Date')) {\n        aValue = new Date(aValue || 0).getTime();\n        bValue = new Date(bValue || 0).getTime();\n      }\n\n      // Handle string fields\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (aValue < bValue) {\n        return sortOrder === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return sortOrder === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n  }\n\n  static async getAnalytics(): Promise<ApiResponse<any>> {\n    try {\n      const data = fs.readFileSync(SUBMISSIONS_FILE, 'utf8');\n      const submissions: ContactSubmission[] = JSON.parse(data);\n\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n      const analytics = {\n        totalSubmissions: submissions.length,\n        todaySubmissions: submissions.filter(s => new Date(s.submittedAt) >= today).length,\n        weeklySubmissions: submissions.filter(s => new Date(s.submittedAt) >= weekAgo).length,\n        monthlySubmissions: submissions.filter(s => new Date(s.submittedAt) >= monthAgo).length,\n        statusDistribution: this.getStatusDistribution(submissions),\n        serviceTypeDistribution: this.getServiceTypeDistribution(submissions),\n        submissionTrends: this.getSubmissionTrends(submissions),\n      };\n\n      return {\n        success: true,\n        message: 'Analytics retrieved successfully',\n        data: analytics,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Failed to retrieve analytics',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  private static getStatusDistribution(submissions: ContactSubmission[]) {\n    const statusCounts: Record<string, number> = {};\n    submissions.forEach(s => {\n      statusCounts[s.status] = (statusCounts[s.status] || 0) + 1;\n    });\n\n    return Object.entries(statusCounts).map(([status, count]) => ({\n      status,\n      count,\n      percentage: Math.round((count / submissions.length) * 100),\n    }));\n  }\n\n  private static getServiceTypeDistribution(submissions: ContactSubmission[]) {\n    const typeCounts: Record<string, number> = {};\n    submissions.forEach(s => {\n      typeCounts[s.serviceType] = (typeCounts[s.serviceType] || 0) + 1;\n    });\n\n    return Object.entries(typeCounts).map(([type, count]) => ({\n      type,\n      count,\n      percentage: Math.round((count / submissions.length) * 100),\n    }));\n  }\n\n  private static getSubmissionTrends(submissions: ContactSubmission[]) {\n    const trends: Record<string, number> = {};\n    const last30Days = Array.from({ length: 30 }, (_, i) => {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      return date.toISOString().split('T')[0];\n    }).reverse();\n\n    last30Days.forEach(date => {\n      trends[date] = 0;\n    });\n\n    submissions.forEach(s => {\n      const date = s.submittedAt.split('T')[0];\n      if (trends.hasOwnProperty(date)) {\n        trends[date]++;\n      }\n    });\n\n    return Object.entries(trends).map(([date, count]) => ({\n      date,\n      count,\n    }));\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAC7C,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAExC,+BAA+B;AAC/B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;IAC5B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU;QAAE,WAAW;IAAK;AAC3C;AAEA,uCAAuC;AACvC,MAAM,kBAAkB;IACtB,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,mBAAmB;QACpC,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;IAC9D;IACA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;QAC9B,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;IACxD;IACA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,cAAc;QAC/B,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,GAAG,MAAM;IACzD;AACF;AAEA;AAGO,MAAM;IACX,aAAa,OACX,OAA2B,EAC3B,UAA6B,EACc;QAC3C,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;YAC/C,IAAI,cAAmC,KAAK,KAAK,CAAC;YAElD,gBAAgB;YAChB,IAAI,SAAS;gBACX,cAAc,IAAI,CAAC,YAAY,CAAC,aAAa;YAC/C;YAEA,gBAAgB;YAChB,IAAI,YAAY,QAAQ;gBACtB,cAAc,IAAI,CAAC,YAAY,CAAC,aAAa,WAAW,MAAM,EAAE,WAAW,SAAS,IAAI;YAC1F;YAEA,uBAAuB;YACvB,MAAM,QAAQ,YAAY,MAAM;YAChC,MAAM,OAAO,YAAY,QAAQ;YACjC,MAAM,QAAQ,YAAY,SAAS;YACnC,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;YACrC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAE9B,MAAM,uBAAuB,YAAY,KAAK,CAAC,YAAY;YAE3D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,MAAM;gBACN,YAAY;oBACV;oBACA;oBACA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,QAAQ,EAAU,EAA2C;QACxE,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;YAC/C,MAAM,cAAmC,KAAK,KAAK,CAAC;YACpD,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAElD,IAAI,CAAC,YAAY;gBACf,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,OAAO,UAAoE,EAA2C;QACjI,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;YAC/C,MAAM,cAAmC,KAAK,KAAK,CAAC;YAEpD,MAAM,gBAAmC;gBACvC,GAAG,UAAU;gBACb,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,aAAa,IAAI,OAAO,WAAW;gBACnC,QAAQ;gBACR,UAAU;gBACV,QAAQ;YACV;YAEA,YAAY,IAAI,CAAC;YACjB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;YAErE,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,OAAO,EAAU,EAAE,OAAmC,EAA2C;QAC5G,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;YAC/C,MAAM,cAAmC,KAAK,KAAK,CAAC;YACpD,MAAM,QAAQ,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAElD,IAAI,UAAU,CAAC,GAAG;gBAChB,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,WAAW,CAAC,MAAM,GAAG;gBAAE,GAAG,WAAW,CAAC,MAAM;gBAAE,GAAG,OAAO;YAAC;YACzD,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;YAErE,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,MAAM,WAAW,CAAC,MAAM;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,OAAO,EAAU,EAA8B;QAC1D,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;YAC/C,MAAM,cAAmC,KAAK,KAAK,CAAC;YACpD,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAE7D,IAAI,YAAY,MAAM,KAAK,oBAAoB,MAAM,EAAE;gBACrD,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,SAAS,CAAC,qBAAqB,MAAM;YAE7E,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,WAAW,GAAa,EAAE,OAAmC,EAA6C;QACrH,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;YAC/C,MAAM,cAAmC,KAAK,KAAK,CAAC;YACpD,MAAM,qBAA0C,EAAE;YAElD,YAAY,OAAO,CAAC,CAAA;gBAClB,IAAI,IAAI,QAAQ,CAAC,WAAW,EAAE,GAAG;oBAC/B,OAAO,MAAM,CAAC,YAAY;oBAC1B,mBAAmB,IAAI,CAAC;gBAC1B;YACF;YAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;YAErE,OAAO;gBACL,SAAS;gBACT,SAAS,GAAG,mBAAmB,MAAM,CAAC,iCAAiC,CAAC;gBACxE,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,OAAe,aAAa,WAAgC,EAAE,OAA0B,EAAuB;QAC7G,OAAO,YAAY,MAAM,CAAC,CAAA;YACxB,gBAAgB;YAChB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,MAAM,CAAC,QAAQ,CAAC,WAAW,MAAM,GAAG;gBAC9F,OAAO;YACT;YAEA,sBAAsB;YACtB,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,WAAW,CAAC,QAAQ,CAAC,WAAW,WAAW,GAAG;gBAClH,OAAO;YACT;YAEA,kBAAkB;YAClB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,WAAW,QAAQ,GAAG;gBACtG,OAAO;YACT;YAEA,oBAAoB;YACpB,IAAI,QAAQ,SAAS,EAAE;gBACrB,MAAM,iBAAiB,IAAI,KAAK,WAAW,WAAW;gBACtD,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;gBAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG;gBAC9C,IAAI,iBAAiB,aAAa,iBAAiB,SAAS;oBAC1D,OAAO;gBACT;YACF;YAEA,qBAAqB;YACrB,IAAI,QAAQ,UAAU,IAAI,WAAW,UAAU,KAAK,QAAQ,UAAU,EAAE;gBACtE,OAAO;YACT;YAEA,gBAAgB;YAChB,IAAI,QAAQ,MAAM,EAAE;gBAClB,MAAM,aAAa,QAAQ,MAAM,CAAC,WAAW;gBAC7C,MAAM,mBAAmB;oBACvB,WAAW,WAAW;oBACtB,WAAW,aAAa;oBACxB,WAAW,KAAK;oBAChB,WAAW,KAAK;oBAChB,WAAW,OAAO;iBACnB;gBACD,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAA,QAAS,OAAO,cAAc,SAAS,cAAc;oBAC9E,OAAO;gBACT;YACF;YAEA,cAAc;YACd,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC3C,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,WAAW,IAAI,CAAE,QAAQ,CAAC,OAAO;oBACjF,OAAO;gBACT;YACF;YAEA,OAAO;QACT;IACF;IAEA,OAAe,aAAa,WAAgC,EAAE,MAAc,EAAE,SAAyB,EAAuB;QAC5H,OAAO,YAAY,IAAI,CAAC,CAAC,GAAG;YAC1B,IAAI,SAAc,CAAC,CAAC,OAAkC;YACtD,IAAI,SAAc,CAAC,CAAC,OAAkC;YAEtD,qBAAqB;YACrB,IAAI,OAAO,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,SAAS;gBACpD,SAAS,IAAI,KAAK,UAAU,GAAG,OAAO;gBACtC,SAAS,IAAI,KAAK,UAAU,GAAG,OAAO;YACxC;YAEA,uBAAuB;YACvB,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;gBAC5D,SAAS,OAAO,WAAW;gBAC3B,SAAS,OAAO,WAAW;YAC7B;YAEA,IAAI,SAAS,QAAQ;gBACnB,OAAO,cAAc,QAAQ,CAAC,IAAI;YACpC;YACA,IAAI,SAAS,QAAQ;gBACnB,OAAO,cAAc,QAAQ,IAAI,CAAC;YACpC;YACA,OAAO;QACT;IACF;IAEA,aAAa,eAA0C;QACrD,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,kBAAkB;YAC/C,MAAM,cAAmC,KAAK,KAAK,CAAC;YAEpD,MAAM,MAAM,IAAI;YAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;YACrE,MAAM,UAAU,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;YAC9D,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;YAEhE,MAAM,YAAY;gBAChB,kBAAkB,YAAY,MAAM;gBACpC,kBAAkB,YAAY,MAAM,CAAC,CAAA,IAAK,IAAI,KAAK,EAAE,WAAW,KAAK,OAAO,MAAM;gBAClF,mBAAmB,YAAY,MAAM,CAAC,CAAA,IAAK,IAAI,KAAK,EAAE,WAAW,KAAK,SAAS,MAAM;gBACrF,oBAAoB,YAAY,MAAM,CAAC,CAAA,IAAK,IAAI,KAAK,EAAE,WAAW,KAAK,UAAU,MAAM;gBACvF,oBAAoB,IAAI,CAAC,qBAAqB,CAAC;gBAC/C,yBAAyB,IAAI,CAAC,0BAA0B,CAAC;gBACzD,kBAAkB,IAAI,CAAC,mBAAmB,CAAC;YAC7C;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,OAAe,sBAAsB,WAAgC,EAAE;QACrE,MAAM,eAAuC,CAAC;QAC9C,YAAY,OAAO,CAAC,CAAA;YAClB,YAAY,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI;QAC3D;QAEA,OAAO,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,GAAK,CAAC;gBAC5D;gBACA;gBACA,YAAY,KAAK,KAAK,CAAC,AAAC,QAAQ,YAAY,MAAM,GAAI;YACxD,CAAC;IACH;IAEA,OAAe,2BAA2B,WAAgC,EAAE;QAC1E,MAAM,aAAqC,CAAC;QAC5C,YAAY,OAAO,CAAC,CAAA;YAClB,UAAU,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI;QACjE;QAEA,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;gBACxD;gBACA;gBACA,YAAY,KAAK,KAAK,CAAC,AAAC,QAAQ,YAAY,MAAM,GAAI;YACxD,CAAC;IACH;IAEA,OAAe,oBAAoB,WAAgC,EAAE;QACnE,MAAM,SAAiC,CAAC;QACxC,MAAM,aAAa,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,CAAC,GAAG;YAChD,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAC9B,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACzC,GAAG,OAAO;QAEV,WAAW,OAAO,CAAC,CAAA;YACjB,MAAM,CAAC,KAAK,GAAG;QACjB;QAEA,YAAY,OAAO,CAAC,CAAA;YAClB,MAAM,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACxC,IAAI,OAAO,cAAc,CAAC,OAAO;gBAC/B,MAAM,CAAC,KAAK;YACd;QACF;QAEA,OAAO,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;gBACpD;gBACA;YACF,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/submissions/%5Bid%5D.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\nimport { ContactSubmissionService } from '../../../../lib/dataService';\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Verify admin token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({\n      success: false,\n      message: 'No token provided'\n    });\n  }\n\n  const token = authHeader.substring(7);\n  \n  try {\n    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (jwtError) {\n    return res.status(401).json({\n      success: false,\n      message: 'Invalid token'\n    });\n  }\n\n  const { id } = req.query;\n\n  if (!id || typeof id !== 'string') {\n    return res.status(400).json({\n      success: false,\n      message: 'Invalid submission ID'\n    });\n  }\n\n  if (req.method === 'GET') {\n    try {\n      const result = await ContactSubmissionService.getById(id);\n      return res.status(result.success ? 200 : 404).json(result);\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to retrieve submission',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else if (req.method === 'PUT') {\n    try {\n      const result = await ContactSubmissionService.update(id, req.body);\n      return res.status(result.success ? 200 : 404).json(result);\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to update submission',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else if (req.method === 'DELETE') {\n    try {\n      const result = await ContactSubmissionService.delete(id);\n      return res.status(result.success ? 200 : 404).json(result);\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to delete submission',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEe,eAAe,QAAQ,GAAmB,EAAE,GAAoB;IAC7E,qBAAqB;IACrB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;IAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;IAEnC,IAAI;QACF,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IAC9C,EAAE,OAAO,UAAU;QACjB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,KAAK;IAExB,IAAI,CAAC,MAAM,OAAO,OAAO,UAAU;QACjC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,IAAI;YACF,MAAM,SAAS,MAAM,kHAAA,CAAA,2BAAwB,CAAC,OAAO,CAAC;YACtD,OAAO,IAAI,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM,KAAK,IAAI,CAAC;QACrD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO,IAAI,IAAI,MAAM,KAAK,OAAO;QAC/B,IAAI;YACF,MAAM,SAAS,MAAM,kHAAA,CAAA,2BAAwB,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI;YACjE,OAAO,IAAI,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM,KAAK,IAAI,CAAC;QACrD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO,IAAI,IAAI,MAAM,KAAK,UAAU;QAClC,IAAI;YACF,MAAM,SAAS,MAAM,kHAAA,CAAA,2BAAwB,CAAC,MAAM,CAAC;YACrD,OAAO,IAAI,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM,KAAK,IAAI,CAAC;QACrD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO;QACL,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}