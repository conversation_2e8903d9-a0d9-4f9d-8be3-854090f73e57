{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/verify.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\n\ninterface ApiResponse {\n  success: boolean;\n  message: string;\n  user?: any;\n}\n\nexport default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse<ApiResponse>\n) {\n  if (req.method !== 'GET') {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n\n  try {\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return res.status(401).json({\n        success: false,\n        message: 'No token provided'\n      });\n    }\n\n    const token = authHeader.substring(7);\n    \n    try {\n      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;\n      \n      return res.status(200).json({\n        success: true,\n        message: 'Token valid',\n        user: {\n          username: decoded.username,\n          role: decoded.role\n        }\n      });\n    } catch (jwtError) {\n      return res.status(401).json({\n        success: false,\n        message: 'Invalid token'\n      });\n    }\n\n  } catch (error) {\n    console.error('Token verification error:', error);\n    return res.status(500).json({\n      success: false,\n      message: 'Internal server error'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAQe,eAAe,QAC5B,GAAmB,EACnB,GAAiC;IAEjC,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI;QACF,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QAEnC,IAAI;YACF,MAAM,UAAU,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;YAE5D,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;oBACJ,UAAU,QAAQ,QAAQ;oBAC1B,MAAM,QAAQ,IAAI;gBACpB;YACF;QACF,EAAE,OAAO,UAAU;YACjB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}