module.exports = {

"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsonwebtoken", () => require("jsonwebtoken"));

module.exports = mod;
}}),
"[project]/src/pages/api/admin/system/config.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>handler
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)");
;
// Mock data for system configurations
const mockConfigs = [
    // General Settings
    {
        id: '1',
        category: 'general',
        key: 'company_name',
        value: 'VPL专业网络解决方案',
        type: 'string',
        label: '公司名称',
        description: '网站显示的公司名称',
        required: true,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    {
        id: '2',
        category: 'general',
        key: 'company_name_en',
        value: 'VPL Professional Network Solutions',
        type: 'string',
        label: '公司名称（英文）',
        required: true,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    {
        id: '3',
        category: 'general',
        key: 'company_phone',
        value: '+86-************',
        type: 'string',
        label: '联系电话',
        required: true,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    {
        id: '4',
        category: 'general',
        key: 'company_email',
        value: '<EMAIL>',
        type: 'email',
        label: '联系邮箱',
        required: true,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    // Email Settings
    {
        id: '5',
        category: 'email',
        key: 'smtp_host',
        value: 'smtp.gmail.com',
        type: 'string',
        label: 'SMTP服务器',
        required: true,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    {
        id: '6',
        category: 'email',
        key: 'smtp_port',
        value: 587,
        type: 'number',
        label: 'SMTP端口',
        required: true,
        validation: {
            min: 1,
            max: 65535
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    {
        id: '7',
        category: 'email',
        key: 'smtp_secure',
        value: false,
        type: 'boolean',
        label: '启用SSL/TLS',
        required: false,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    // Form Settings
    {
        id: '8',
        category: 'form',
        key: 'verification_code_expiry',
        value: 10,
        type: 'number',
        label: '验证码有效期（分钟）',
        required: true,
        validation: {
            min: 1,
            max: 60
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    {
        id: '9',
        category: 'form',
        key: 'max_message_length',
        value: 1000,
        type: 'number',
        label: '消息最大长度',
        required: true,
        validation: {
            min: 100,
            max: 5000
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    // Security Settings
    {
        id: '10',
        category: 'security',
        key: 'session_timeout',
        value: 480,
        type: 'number',
        label: '会话超时时间（分钟）',
        required: true,
        validation: {
            min: 30,
            max: 1440
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    },
    {
        id: '11',
        category: 'security',
        key: 'max_login_attempts',
        value: 5,
        type: 'number',
        label: '最大登录尝试次数',
        required: true,
        validation: {
            min: 3,
            max: 10
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
    }
];
function verifyToken(token) {
    try {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__["default"].verify(token, process.env.JWT_SECRET || 'your-secret-key');
    } catch (error) {
        throw new Error('Invalid token');
    }
}
async function handler(req, res) {
    try {
        // Verify authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'No token provided'
            });
        }
        const token = authHeader.substring(7);
        const decoded = verifyToken(token);
        if (req.method === 'GET') {
            const { category } = req.query;
            let configs = mockConfigs;
            if (category && typeof category === 'string') {
                configs = mockConfigs.filter((config)=>config.category === category);
            }
            return res.status(200).json({
                success: true,
                message: 'Configurations retrieved successfully',
                data: configs
            });
        } else if (req.method === 'PUT') {
            const updateRequest = req.body;
            if (!updateRequest.configs || !Array.isArray(updateRequest.configs)) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid request format'
                });
            }
            // Validate and update configurations
            const updatedConfigs = [];
            for (const configUpdate of updateRequest.configs){
                const existingConfig = mockConfigs.find((c)=>c.key === configUpdate.key);
                if (!existingConfig) {
                    return res.status(400).json({
                        success: false,
                        message: `Configuration key '${configUpdate.key}' not found`
                    });
                }
                // Validate value based on type
                if (!validateConfigValue(existingConfig, configUpdate.value)) {
                    return res.status(400).json({
                        success: false,
                        message: `Invalid value for '${configUpdate.key}'`
                    });
                }
                // Update the configuration
                const updatedConfig = {
                    ...existingConfig,
                    value: configUpdate.value,
                    updatedAt: new Date().toISOString(),
                    updatedBy: decoded.username
                };
                // Update in mock data
                const index = mockConfigs.findIndex((c)=>c.key === configUpdate.key);
                if (index !== -1) {
                    mockConfigs[index] = updatedConfig;
                }
                updatedConfigs.push(updatedConfig);
            }
            return res.status(200).json({
                success: true,
                message: 'Configurations updated successfully',
                data: updatedConfigs
            });
        } else {
            return res.status(405).json({
                success: false,
                message: 'Method not allowed'
            });
        }
    } catch (error) {
        console.error('System config error:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
}
function validateConfigValue(config, value) {
    switch(config.type){
        case 'string':
            if (typeof value !== 'string') return false;
            if (config.required && !value.trim()) return false;
            break;
        case 'number':
            if (typeof value !== 'number' || isNaN(value)) return false;
            if (config.validation?.min !== undefined && value < config.validation.min) return false;
            if (config.validation?.max !== undefined && value > config.validation.max) return false;
            break;
        case 'boolean':
            if (typeof value !== 'boolean') return false;
            break;
        case 'email':
            if (typeof value !== 'string') return false;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) return false;
            break;
        case 'url':
            if (typeof value !== 'string') return false;
            try {
                new URL(value);
            } catch  {
                return false;
            }
            break;
        default:
            return true;
    }
    return true;
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__28b4f346._.js.map