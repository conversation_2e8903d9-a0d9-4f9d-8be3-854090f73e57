{"version": 3, "sources": [], "sections": [{"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/login.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\ninterface LoginRequest {\n  username: string;\n  password: string;\n}\n\ninterface ApiResponse {\n  success: boolean;\n  message: string;\n  token?: string;\n}\n\n// In a real application, you would store admin credentials in a database\n// For this demo, we'll use environment variables\nconst ADMIN_CREDENTIALS = {\n  username: process.env.ADMIN_USERNAME || 'admin',\n  // In production, this should be a hashed password stored in the database\n  passwordHash: process.env.ADMIN_PASSWORD_HASH || bcrypt.hashSync(process.env.ADMIN_PASSWORD || 'admin123', 10),\n};\n\nexport default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse<ApiResponse>\n) {\n  if (req.method !== 'POST') {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n\n  try {\n    const { username, password }: LoginRequest = req.body;\n\n    // Validate input\n    if (!username || !password) {\n      return res.status(400).json({\n        success: false,\n        message: 'Username and password are required'\n      });\n    }\n\n    // Check username\n    if (username !== ADMIN_CREDENTIALS.username) {\n      return res.status(401).json({\n        success: false,\n        message: 'Invalid credentials'\n      });\n    }\n\n    // Check password\n    const isPasswordValid = await bcrypt.compare(password, ADMIN_CREDENTIALS.passwordHash);\n    if (!isPasswordValid) {\n      return res.status(401).json({\n        success: false,\n        message: 'Invalid credentials'\n      });\n    }\n\n    // Generate JWT token\n    const token = jwt.sign(\n      { \n        username: username,\n        role: 'admin',\n        iat: Math.floor(Date.now() / 1000)\n      },\n      process.env.JWT_SECRET || 'your-secret-key',\n      { expiresIn: '24h' }\n    );\n\n    // Log successful login\n    console.log('Admin login successful:', {\n      username,\n      timestamp: new Date().toISOString(),\n      ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,\n    });\n\n    return res.status(200).json({\n      success: true,\n      message: 'Login successful',\n      token\n    });\n\n  } catch (error) {\n    console.error('Admin login error:', error);\n    return res.status(500).json({\n      success: false,\n      message: 'Internal server error'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;;;;;AAaA,yEAAyE;AACzE,iDAAiD;AACjD,MAAM,oBAAoB;IACxB,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;IACxC,yEAAyE;IACzE,cAAc,QAAQ,GAAG,CAAC,mBAAmB,IAAI,gHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI,YAAY;AAC7G;AAEe,eAAe,QAC5B,GAAmB,EACnB,GAAiC;IAEjC,IAAI,IAAI,MAAM,KAAK,QAAQ;QACzB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAiB,IAAI,IAAI;QAErD,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,iBAAiB;QACjB,IAAI,aAAa,kBAAkB,QAAQ,EAAE;YAC3C,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,iBAAiB;QACjB,MAAM,kBAAkB,MAAM,gHAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,kBAAkB,YAAY;QACrF,IAAI,CAAC,iBAAiB;YACpB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,qBAAqB;QACrB,MAAM,QAAQ,iHAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,UAAU;YACV,MAAM;YACN,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAC/B,GACA,QAAQ,GAAG,CAAC,UAAU,IAAI,mBAC1B;YAAE,WAAW;QAAM;QAGrB,uBAAuB;QACvB,QAAQ,GAAG,CAAC,2BAA2B;YACrC;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,IAAI,IAAI,OAAO,CAAC,kBAAkB,IAAI,IAAI,UAAU,CAAC,aAAa;QACpE;QAEA,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}