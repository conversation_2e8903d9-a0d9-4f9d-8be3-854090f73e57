module.exports = {

"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/zod [external] (zod, esm_import)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("zod");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/pages/api/admin/services/index.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": ()=>handler
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/zod [external] (zod, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__
]);
[__TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
// Service content schema for validation
const ServiceSchema = __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().min(1, '服务名称不能为空'),
    slug: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().min(1, 'URL标识不能为空').regex(/^[a-z0-9_-]+$/, 'URL标识只能包含小写字母、数字、下划线和连字符'),
    title: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().min(1, '页面标题不能为空'),
    description: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().min(1, '服务描述不能为空'),
    content: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().optional(),
    features: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].array(__TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string()).default([]),
    status: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].enum([
        'active',
        'inactive',
        'draft'
    ]).default('draft'),
    sortOrder: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].number().default(0),
    seoTitle: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().optional(),
    seoDescription: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().optional(),
    seoKeywords: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().optional(),
    images: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].array(__TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string()).default([]),
    tags: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].array(__TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string()).default([])
});
const UpdateServiceSchema = ServiceSchema.partial().extend({
    id: __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].string().min(1, '服务ID不能为空')
});
// Mock database - in production, this would be a real database
let services = [
    {
        id: '1',
        name: 'VPN服务',
        slug: 'vpn_services',
        title: 'VPN服务 - 安全可靠的网络连接',
        description: '军用级加密的VPN连接服务，保护您的数据安全',
        content: '<h2>VPN服务详情</h2><p>我们提供军用级加密的VPN连接服务...</p>',
        features: [
            '军用级加密',
            '零日志政策',
            '全球服务器',
            '无限带宽'
        ],
        status: 'active',
        sortOrder: 1,
        seoTitle: 'VPN服务 - 安全可靠的网络连接 | VPL',
        seoDescription: '军用级加密的VPN连接服务，保护您的数据安全，零日志政策，全球服务器覆盖',
        seoKeywords: 'VPN,网络安全,加密,隐私保护',
        images: [
            '/images/services/vpn-hero.jpg'
        ],
        tags: [
            '网络安全',
            'VPN',
            '加密'
        ],
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        version: 1
    },
    {
        id: '2',
        name: '跨境电商专线',
        slug: 'ecommerce_lines',
        title: '跨境电商专线 - 优化购物体验',
        description: '优化的跨境电商网络连接解决方案，提升用户购物体验',
        content: '<h2>跨境电商专线详情</h2><p>专为跨境电商设计的网络解决方案...</p>',
        features: [
            '多平台支持',
            '高可用性',
            '流量优化',
            '实时监控'
        ],
        status: 'active',
        sortOrder: 2,
        seoTitle: '跨境电商专线 - 优化购物体验 | VPL',
        seoDescription: '优化的跨境电商网络连接解决方案，提升用户购物体验，支持多平台',
        seoKeywords: '跨境电商,网络专线,购物体验,电商优化',
        images: [
            '/images/services/ecommerce-hero.jpg'
        ],
        tags: [
            '跨境电商',
            '网络专线',
            '购物优化'
        ],
        createdAt: '2024-01-14T10:00:00Z',
        updatedAt: '2024-01-14T10:00:00Z',
        version: 1
    },
    {
        id: '3',
        name: '外贸专线',
        slug: 'foreign_trade_lines',
        title: '外贸专线 - 全球贸易网络',
        description: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
        content: '<h2>外贸专线详情</h2><p>专为外贸企业设计的高速稳定网络线路...</p>',
        features: [
            '专用带宽',
            '全球覆盖',
            '低延迟',
            '企业级支持'
        ],
        status: 'active',
        sortOrder: 3,
        seoTitle: '外贸专线 - 全球贸易网络 | VPL',
        seoDescription: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
        seoKeywords: '外贸专线,全球贸易,网络线路,企业网络',
        images: [
            '/images/services/trade-hero.jpg'
        ],
        tags: [
            '外贸',
            '专线',
            '全球网络'
        ],
        createdAt: '2024-01-13T10:00:00Z',
        updatedAt: '2024-01-13T10:00:00Z',
        version: 1
    },
    {
        id: '4',
        name: '定制解决方案',
        slug: 'custom_solution',
        title: '定制解决方案 - 专属网络方案',
        description: '根据您的特殊需求定制专属网络解决方案',
        content: '<h2>定制解决方案详情</h2><p>根据您的特殊需求定制专属网络解决方案...</p>',
        features: [
            '定制设计',
            '专家咨询',
            '可扩展架构',
            '持续支持'
        ],
        status: 'active',
        sortOrder: 4,
        seoTitle: '定制解决方案 - 专属网络方案 | VPL',
        seoDescription: '根据您的特殊需求定制专属网络解决方案，专家咨询，可扩展架构',
        seoKeywords: '定制方案,网络解决方案,专家咨询,企业定制',
        images: [
            '/images/services/custom-hero.jpg'
        ],
        tags: [
            '定制',
            '解决方案',
            '专家咨询'
        ],
        createdAt: '2024-01-12T10:00:00Z',
        updatedAt: '2024-01-12T10:00:00Z',
        version: 1
    }
];
async function handler(req, res) {
    try {
        // Simple auth check - in production, use proper JWT verification
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: '未授权访问'
            });
        }
        switch(req.method){
            case 'GET':
                return handleGet(req, res);
            case 'POST':
                return handlePost(req, res);
            case 'PUT':
                return handlePut(req, res);
            case 'DELETE':
                return handleDelete(req, res);
            default:
                res.setHeader('Allow', [
                    'GET',
                    'POST',
                    'PUT',
                    'DELETE'
                ]);
                return res.status(405).json({
                    error: `方法 ${req.method} 不被允许`
                });
        }
    } catch (error) {
        console.error('Services API error:', error);
        return res.status(500).json({
            error: '服务器内部错误'
        });
    }
}
async function handleGet(req, res) {
    const { page = '1', limit = '10', search = '', status = '', sortBy = 'sortOrder', sortOrder = 'asc' } = req.query;
    let filteredServices = [
        ...services
    ];
    // Search filter
    if (search) {
        const searchTerm = search.toString().toLowerCase();
        filteredServices = filteredServices.filter((service)=>service.name.toLowerCase().includes(searchTerm) || service.description.toLowerCase().includes(searchTerm) || service.tags.some((tag)=>tag.toLowerCase().includes(searchTerm)));
    }
    // Status filter
    if (status && status !== 'all') {
        filteredServices = filteredServices.filter((service)=>service.status === status);
    }
    // Sorting
    filteredServices.sort((a, b)=>{
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        if (sortOrder === 'desc') {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    });
    // Pagination
    const pageNum = parseInt(page.toString());
    const limitNum = parseInt(limit.toString());
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedServices = filteredServices.slice(startIndex, endIndex);
    return res.status(200).json({
        services: paginatedServices,
        pagination: {
            page: pageNum,
            limit: limitNum,
            total: filteredServices.length,
            totalPages: Math.ceil(filteredServices.length / limitNum)
        }
    });
}
async function handlePost(req, res) {
    try {
        const validatedData = ServiceSchema.parse(req.body);
        // Check if slug already exists
        const existingService = services.find((s)=>s.slug === validatedData.slug);
        if (existingService) {
            return res.status(400).json({
                error: 'URL标识已存在，请使用其他标识'
            });
        }
        const newService = {
            id: Date.now().toString(),
            ...validatedData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            version: 1
        };
        services.push(newService);
        return res.status(201).json({
            message: '服务创建成功',
            service: newService
        });
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].ZodError) {
            return res.status(400).json({
                error: '数据验证失败',
                details: error.errors
            });
        }
        throw error;
    }
}
async function handlePut(req, res) {
    try {
        const validatedData = UpdateServiceSchema.parse(req.body);
        const { id, ...updateData } = validatedData;
        const serviceIndex = services.findIndex((s)=>s.id === id);
        if (serviceIndex === -1) {
            return res.status(404).json({
                error: '服务不存在'
            });
        }
        // Check if slug already exists (excluding current service)
        if (updateData.slug) {
            const existingService = services.find((s)=>s.slug === updateData.slug && s.id !== id);
            if (existingService) {
                return res.status(400).json({
                    error: 'URL标识已存在，请使用其他标识'
                });
            }
        }
        const updatedService = {
            ...services[serviceIndex],
            ...updateData,
            updatedAt: new Date().toISOString(),
            version: services[serviceIndex].version + 1
        };
        services[serviceIndex] = updatedService;
        return res.status(200).json({
            message: '服务更新成功',
            service: updatedService
        });
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$zod__$5b$external$5d$__$28$zod$2c$__esm_import$29$__["z"].ZodError) {
            return res.status(400).json({
                error: '数据验证失败',
                details: error.errors
            });
        }
        throw error;
    }
}
async function handleDelete(req, res) {
    const { ids } = req.body;
    if (!ids || !Array.isArray(ids)) {
        return res.status(400).json({
            error: '请提供要删除的服务ID列表'
        });
    }
    const deletedServices = [];
    const notFoundIds = [];
    for (const id of ids){
        const serviceIndex = services.findIndex((s)=>s.id === id);
        if (serviceIndex !== -1) {
            deletedServices.push(services[serviceIndex]);
            services.splice(serviceIndex, 1);
        } else {
            notFoundIds.push(id);
        }
    }
    return res.status(200).json({
        message: `成功删除 ${deletedServices.length} 个服务`,
        deletedServices,
        notFoundIds: notFoundIds.length > 0 ? notFoundIds : undefined
    });
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__16c6ba1c._.js.map