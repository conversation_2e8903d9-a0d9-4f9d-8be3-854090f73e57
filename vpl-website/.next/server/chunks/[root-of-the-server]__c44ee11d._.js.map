{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/analytics/dashboard.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\n\ninterface AnalyticsData {\n  totalSubmissions: number;\n  todaySubmissions: number;\n  weeklySubmissions: number;\n  monthlySubmissions: number;\n  conversionRate: number;\n  averageResponseTime: number;\n  topServiceTypes: Array<{\n    type: string;\n    count: number;\n    percentage: number;\n  }>;\n  submissionTrends: Array<{\n    date: string;\n    count: number;\n  }>;\n  statusDistribution: Array<{\n    status: string;\n    count: number;\n    percentage: number;\n  }>;\n  geographicDistribution: Array<{\n    region: string;\n    count: number;\n    percentage: number;\n  }>;\n  timeDistribution: Array<{\n    hour: number;\n    count: number;\n  }>;\n}\n\ninterface ApiResponse {\n  success: boolean;\n  message: string;\n  data?: AnalyticsData;\n}\n\nfunction verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n}\n\nfunction generateMockAnalytics(): AnalyticsData {\n  const now = new Date();\n  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n  const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n  // Generate submission trends for the last 30 days\n  const submissionTrends = [];\n  for (let i = 29; i >= 0; i--) {\n    const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);\n    const count = Math.floor(Math.random() * 20) + 5; // 5-25 submissions per day\n    submissionTrends.push({\n      date: date.toISOString().split('T')[0],\n      count\n    });\n  }\n\n  const totalSubmissions = submissionTrends.reduce((sum, day) => sum + day.count, 0);\n  const todaySubmissions = submissionTrends[submissionTrends.length - 1].count;\n  const weeklySubmissions = submissionTrends.slice(-7).reduce((sum, day) => sum + day.count, 0);\n\n  return {\n    totalSubmissions: totalSubmissions + 1250, // Add historical data\n    todaySubmissions,\n    weeklySubmissions,\n    monthlySubmissions: totalSubmissions,\n    conversionRate: 68.5,\n    averageResponseTime: 2.3, // hours\n    topServiceTypes: [\n      { type: 'foreign_trade_lines', count: 456, percentage: 38.2 },\n      { type: 'ecommerce_lines', count: 342, percentage: 28.6 },\n      { type: 'vpn_services', count: 278, percentage: 23.3 },\n      { type: 'custom_solution', count: 118, percentage: 9.9 }\n    ],\n    submissionTrends,\n    statusDistribution: [\n      { status: 'pending', count: 89, percentage: 32.1 },\n      { status: 'contacted', count: 156, percentage: 56.3 },\n      { status: 'closed', count: 32, percentage: 11.6 }\n    ],\n    geographicDistribution: [\n      { region: '广东省', count: 234, percentage: 28.5 },\n      { region: '上海市', count: 189, percentage: 23.0 },\n      { region: '北京市', count: 156, percentage: 19.0 },\n      { region: '浙江省', count: 98, percentage: 11.9 },\n      { region: '江苏省', count: 87, percentage: 10.6 },\n      { region: '其他', count: 58, percentage: 7.0 }\n    ],\n    timeDistribution: [\n      { hour: 0, count: 2 }, { hour: 1, count: 1 }, { hour: 2, count: 1 },\n      { hour: 3, count: 0 }, { hour: 4, count: 1 }, { hour: 5, count: 2 },\n      { hour: 6, count: 5 }, { hour: 7, count: 8 }, { hour: 8, count: 15 },\n      { hour: 9, count: 28 }, { hour: 10, count: 35 }, { hour: 11, count: 42 },\n      { hour: 12, count: 38 }, { hour: 13, count: 45 }, { hour: 14, count: 52 },\n      { hour: 15, count: 48 }, { hour: 16, count: 41 }, { hour: 17, count: 35 },\n      { hour: 18, count: 28 }, { hour: 19, count: 22 }, { hour: 20, count: 18 },\n      { hour: 21, count: 12 }, { hour: 22, count: 8 }, { hour: 23, count: 4 }\n    ]\n  };\n}\n\nexport default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse<ApiResponse>\n) {\n  if (req.method !== 'GET') {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n\n  try {\n    // Verify authentication\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return res.status(401).json({\n        success: false,\n        message: 'No token provided'\n      });\n    }\n\n    const token = authHeader.substring(7);\n    const decoded = verifyToken(token);\n\n    // Check permissions (analytics:read)\n    // In a real app, you would check user permissions here\n\n    const { dateRange, serviceType, region } = req.query;\n\n    // Generate analytics data\n    // In a real app, you would query your database with filters\n    const analyticsData = generateMockAnalytics();\n\n    // Apply filters if provided\n    if (serviceType && typeof serviceType === 'string') {\n      // Filter data by service type\n      analyticsData.topServiceTypes = analyticsData.topServiceTypes.filter(\n        service => service.type === serviceType\n      );\n    }\n\n    if (region && typeof region === 'string') {\n      // Filter data by region\n      analyticsData.geographicDistribution = analyticsData.geographicDistribution.filter(\n        geo => geo.region === region\n      );\n    }\n\n    return res.status(200).json({\n      success: true,\n      message: 'Analytics data retrieved successfully',\n      data: analyticsData\n    });\n\n  } catch (error) {\n    console.error('Analytics API error:', error);\n    return res.status(500).json({\n      success: false,\n      message: 'Internal server error'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAwCA,SAAS,YAAY,KAAa;IAChC,IAAI;QACF,OAAO,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IACrD,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,SAAS;IACP,MAAM,MAAM,IAAI;IAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;IACrE,MAAM,UAAU,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;IAC9D,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;IAEhE,kDAAkD;IAClD,MAAM,mBAAmB,EAAE;IAC3B,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAK;QAC5B,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAC3D,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,2BAA2B;QAC7E,iBAAiB,IAAI,CAAC;YACpB,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC;QACF;IACF;IAEA,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,KAAK,EAAE;IAChF,MAAM,mBAAmB,gBAAgB,CAAC,iBAAiB,MAAM,GAAG,EAAE,CAAC,KAAK;IAC5E,MAAM,oBAAoB,iBAAiB,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,KAAK,EAAE;IAE3F,OAAO;QACL,kBAAkB,mBAAmB;QACrC;QACA;QACA,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;QACrB,iBAAiB;YACf;gBAAE,MAAM;gBAAuB,OAAO;gBAAK,YAAY;YAAK;YAC5D;gBAAE,MAAM;gBAAmB,OAAO;gBAAK,YAAY;YAAK;YACxD;gBAAE,MAAM;gBAAgB,OAAO;gBAAK,YAAY;YAAK;YACrD;gBAAE,MAAM;gBAAmB,OAAO;gBAAK,YAAY;YAAI;SACxD;QACD;QACA,oBAAoB;YAClB;gBAAE,QAAQ;gBAAW,OAAO;gBAAI,YAAY;YAAK;YACjD;gBAAE,QAAQ;gBAAa,OAAO;gBAAK,YAAY;YAAK;YACpD;gBAAE,QAAQ;gBAAU,OAAO;gBAAI,YAAY;YAAK;SACjD;QACD,wBAAwB;YACtB;gBAAE,QAAQ;gBAAO,OAAO;gBAAK,YAAY;YAAK;YAC9C;gBAAE,QAAQ;gBAAO,OAAO;gBAAK,YAAY;YAAK;YAC9C;gBAAE,QAAQ;gBAAO,OAAO;gBAAK,YAAY;YAAK;YAC9C;gBAAE,QAAQ;gBAAO,OAAO;gBAAI,YAAY;YAAK;YAC7C;gBAAE,QAAQ;gBAAO,OAAO;gBAAI,YAAY;YAAK;YAC7C;gBAAE,QAAQ;gBAAM,OAAO;gBAAI,YAAY;YAAI;SAC5C;QACD,kBAAkB;YAChB;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAAG;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAAG;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAClE;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAAG;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAAG;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAClE;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAAG;gBAAE,MAAM;gBAAG,OAAO;YAAE;YAAG;gBAAE,MAAM;gBAAG,OAAO;YAAG;YACnE;gBAAE,MAAM;gBAAG,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YACvE;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YACxE;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YACxE;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAG;YACxE;gBAAE,MAAM;gBAAI,OAAO;YAAG;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAE;YAAG;gBAAE,MAAM;gBAAI,OAAO;YAAE;SACvE;IACH;AACF;AAEe,eAAe,QAC5B,GAAmB,EACnB,GAAiC;IAEjC,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI;QACF,wBAAwB;QACxB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAE5B,qCAAqC;QACrC,uDAAuD;QAEvD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK;QAEpD,0BAA0B;QAC1B,4DAA4D;QAC5D,MAAM,gBAAgB;QAEtB,4BAA4B;QAC5B,IAAI,eAAe,OAAO,gBAAgB,UAAU;YAClD,8BAA8B;YAC9B,cAAc,eAAe,GAAG,cAAc,eAAe,CAAC,MAAM,CAClE,CAAA,UAAW,QAAQ,IAAI,KAAK;QAEhC;QAEA,IAAI,UAAU,OAAO,WAAW,UAAU;YACxC,wBAAwB;YACxB,cAAc,sBAAsB,GAAG,cAAc,sBAAsB,CAAC,MAAM,CAChF,CAAA,MAAO,IAAI,MAAM,KAAK;QAE1B;QAEA,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}