module.exports = {

"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsonwebtoken", () => require("jsonwebtoken"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/dataService.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ContactSubmissionService": ()=>ContactSubmissionService
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const SUBMISSIONS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'submissions.json');
const USERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'users.json');
const CONFIG_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'config.json');
// Ensure data directory exists
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
        recursive: true
    });
}
// Initialize files if they don't exist
const initializeFiles = ()=>{
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(SUBMISSIONS_FILE)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(SUBMISSIONS_FILE, JSON.stringify([], null, 2));
    }
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(USERS_FILE)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USERS_FILE, JSON.stringify([], null, 2));
    }
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(CONFIG_FILE)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(CONFIG_FILE, JSON.stringify({}, null, 2));
    }
};
initializeFiles();
class ContactSubmissionService {
    static async getAll(filters, pagination) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SUBMISSIONS_FILE, 'utf8');
            let submissions = JSON.parse(data);
            // Apply filters
            if (filters) {
                submissions = this.applyFilters(submissions, filters);
            }
            // Apply sorting
            if (pagination?.sortBy) {
                submissions = this.applySorting(submissions, pagination.sortBy, pagination.sortOrder || 'desc');
            }
            // Calculate pagination
            const total = submissions.length;
            const page = pagination?.page || 1;
            const limit = pagination?.limit || 10;
            const totalPages = Math.ceil(total / limit);
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedSubmissions = submissions.slice(startIndex, endIndex);
            return {
                success: true,
                message: 'Submissions retrieved successfully',
                data: paginatedSubmissions,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages
                }
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to retrieve submissions',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static async getById(id) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SUBMISSIONS_FILE, 'utf8');
            const submissions = JSON.parse(data);
            const submission = submissions.find((s)=>s.id === id);
            if (!submission) {
                return {
                    success: false,
                    message: 'Submission not found'
                };
            }
            return {
                success: true,
                message: 'Submission retrieved successfully',
                data: submission
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to retrieve submission',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static async create(submission) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SUBMISSIONS_FILE, 'utf8');
            const submissions = JSON.parse(data);
            const newSubmission = {
                ...submission,
                id: Date.now().toString(),
                submittedAt: new Date().toISOString(),
                status: 'pending',
                priority: 'medium',
                source: 'website'
            };
            submissions.push(newSubmission);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(SUBMISSIONS_FILE, JSON.stringify(submissions, null, 2));
            return {
                success: true,
                message: 'Submission created successfully',
                data: newSubmission
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to create submission',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static async update(id, updates) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SUBMISSIONS_FILE, 'utf8');
            const submissions = JSON.parse(data);
            const index = submissions.findIndex((s)=>s.id === id);
            if (index === -1) {
                return {
                    success: false,
                    message: 'Submission not found'
                };
            }
            submissions[index] = {
                ...submissions[index],
                ...updates
            };
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(SUBMISSIONS_FILE, JSON.stringify(submissions, null, 2));
            return {
                success: true,
                message: 'Submission updated successfully',
                data: submissions[index]
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to update submission',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static async delete(id) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SUBMISSIONS_FILE, 'utf8');
            const submissions = JSON.parse(data);
            const filteredSubmissions = submissions.filter((s)=>s.id !== id);
            if (submissions.length === filteredSubmissions.length) {
                return {
                    success: false,
                    message: 'Submission not found'
                };
            }
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(SUBMISSIONS_FILE, JSON.stringify(filteredSubmissions, null, 2));
            return {
                success: true,
                message: 'Submission deleted successfully'
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to delete submission',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static async bulkUpdate(ids, updates) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SUBMISSIONS_FILE, 'utf8');
            const submissions = JSON.parse(data);
            const updatedSubmissions = [];
            submissions.forEach((submission)=>{
                if (ids.includes(submission.id)) {
                    Object.assign(submission, updates);
                    updatedSubmissions.push(submission);
                }
            });
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(SUBMISSIONS_FILE, JSON.stringify(submissions, null, 2));
            return {
                success: true,
                message: `${updatedSubmissions.length} submissions updated successfully`,
                data: updatedSubmissions
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to update submissions',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static applyFilters(submissions, filters) {
        return submissions.filter((submission)=>{
            // Status filter
            if (filters.status && filters.status.length > 0 && !filters.status.includes(submission.status)) {
                return false;
            }
            // Service type filter
            if (filters.serviceType && filters.serviceType.length > 0 && !filters.serviceType.includes(submission.serviceType)) {
                return false;
            }
            // Priority filter
            if (filters.priority && filters.priority.length > 0 && !filters.priority.includes(submission.priority)) {
                return false;
            }
            // Date range filter
            if (filters.dateRange) {
                const submissionDate = new Date(submission.submittedAt);
                const startDate = new Date(filters.dateRange.start);
                const endDate = new Date(filters.dateRange.end);
                if (submissionDate < startDate || submissionDate > endDate) {
                    return false;
                }
            }
            // Assigned to filter
            if (filters.assignedTo && submission.assignedTo !== filters.assignedTo) {
                return false;
            }
            // Search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                const searchableFields = [
                    submission.companyName,
                    submission.contactPerson,
                    submission.email,
                    submission.phone,
                    submission.message
                ];
                if (!searchableFields.some((field)=>field?.toLowerCase().includes(searchTerm))) {
                    return false;
                }
            }
            // Tags filter
            if (filters.tags && filters.tags.length > 0) {
                if (!submission.tags || !filters.tags.some((tag)=>submission.tags.includes(tag))) {
                    return false;
                }
            }
            return true;
        });
    }
    static applySorting(submissions, sortBy, sortOrder) {
        return submissions.sort((a, b)=>{
            let aValue = a[sortBy];
            let bValue = b[sortBy];
            // Handle date fields
            if (sortBy.includes('At') || sortBy.includes('Date')) {
                aValue = new Date(aValue || 0).getTime();
                bValue = new Date(bValue || 0).getTime();
            }
            // Handle string fields
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            if (aValue < bValue) {
                return sortOrder === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return sortOrder === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }
    static async getAnalytics() {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SUBMISSIONS_FILE, 'utf8');
            const submissions = JSON.parse(data);
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            const analytics = {
                totalSubmissions: submissions.length,
                todaySubmissions: submissions.filter((s)=>new Date(s.submittedAt) >= today).length,
                weeklySubmissions: submissions.filter((s)=>new Date(s.submittedAt) >= weekAgo).length,
                monthlySubmissions: submissions.filter((s)=>new Date(s.submittedAt) >= monthAgo).length,
                statusDistribution: this.getStatusDistribution(submissions),
                serviceTypeDistribution: this.getServiceTypeDistribution(submissions),
                submissionTrends: this.getSubmissionTrends(submissions)
            };
            return {
                success: true,
                message: 'Analytics retrieved successfully',
                data: analytics
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to retrieve analytics',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static getStatusDistribution(submissions) {
        const statusCounts = {};
        submissions.forEach((s)=>{
            statusCounts[s.status] = (statusCounts[s.status] || 0) + 1;
        });
        return Object.entries(statusCounts).map(([status, count])=>({
                status,
                count,
                percentage: Math.round(count / submissions.length * 100)
            }));
    }
    static getServiceTypeDistribution(submissions) {
        const typeCounts = {};
        submissions.forEach((s)=>{
            typeCounts[s.serviceType] = (typeCounts[s.serviceType] || 0) + 1;
        });
        return Object.entries(typeCounts).map(([type, count])=>({
                type,
                count,
                percentage: Math.round(count / submissions.length * 100)
            }));
    }
    static getSubmissionTrends(submissions) {
        const trends = {};
        const last30Days = Array.from({
            length: 30
        }, (_, i)=>{
            const date = new Date();
            date.setDate(date.getDate() - i);
            return date.toISOString().split('T')[0];
        }).reverse();
        last30Days.forEach((date)=>{
            trends[date] = 0;
        });
        submissions.forEach((s)=>{
            const date = s.submittedAt.split('T')[0];
            if (trends.hasOwnProperty(date)) {
                trends[date]++;
            }
        });
        return Object.entries(trends).map(([date, count])=>({
                date,
                count
            }));
    }
}
}),
"[project]/src/pages/api/admin/submissions/[id].ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>handler
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dataService$2e$ts__$5b$api$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dataService.ts [api] (ecmascript)");
;
;
async function handler(req, res) {
    // Verify admin token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
            success: false,
            message: 'No token provided'
        });
    }
    const token = authHeader.substring(7);
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__["default"].verify(token, process.env.JWT_SECRET || 'your-secret-key');
    } catch (jwtError) {
        return res.status(401).json({
            success: false,
            message: 'Invalid token'
        });
    }
    const { id } = req.query;
    if (!id || typeof id !== 'string') {
        return res.status(400).json({
            success: false,
            message: 'Invalid submission ID'
        });
    }
    if (req.method === 'GET') {
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dataService$2e$ts__$5b$api$5d$__$28$ecmascript$29$__["ContactSubmissionService"].getById(id);
            return res.status(result.success ? 200 : 404).json(result);
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to retrieve submission',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else if (req.method === 'PUT') {
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dataService$2e$ts__$5b$api$5d$__$28$ecmascript$29$__["ContactSubmissionService"].update(id, req.body);
            return res.status(result.success ? 200 : 404).json(result);
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to update submission',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else if (req.method === 'DELETE') {
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dataService$2e$ts__$5b$api$5d$__$28$ecmascript$29$__["ContactSubmissionService"].delete(id);
            return res.status(result.success ? 200 : 404).json(result);
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to delete submission',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else {
        return res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__fd64e4ad._.js.map