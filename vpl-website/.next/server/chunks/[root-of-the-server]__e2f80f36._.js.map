{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/system-config.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\nimport fs from 'fs';\nimport path from 'path';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst CONFIG_FILE = path.join(DATA_DIR, 'system-config.json');\n\n// Ensure data directory and file exist\nif (!fs.existsSync(DATA_DIR)) {\n  fs.mkdirSync(DATA_DIR, { recursive: true });\n}\n\nif (!fs.existsSync(CONFIG_FILE)) {\n  // Create default system configurations\n  const defaultConfigs = [\n    // General Settings\n    {\n      id: '1',\n      category: 'general',\n      key: 'site_name',\n      value: 'VPL - 专业网络线路服务商',\n      description: '网站名称',\n      type: 'string',\n      isEditable: true,\n    },\n    {\n      id: '2',\n      category: 'general',\n      key: 'company_name',\n      value: 'VPL网络科技有限公司',\n      description: '公司名称',\n      type: 'string',\n      isEditable: true,\n    },\n    {\n      id: '3',\n      category: 'general',\n      key: 'contact_email',\n      value: '<EMAIL>',\n      description: '联系邮箱',\n      type: 'string',\n      isEditable: true,\n    },\n    {\n      id: '4',\n      category: 'general',\n      key: 'contact_phone',\n      value: '+86 ************',\n      description: '联系电话',\n      type: 'string',\n      isEditable: true,\n    },\n    // Email Settings\n    {\n      id: '5',\n      category: 'email',\n      key: 'auto_reply_enabled',\n      value: 'true',\n      description: '启用自动回复邮件',\n      type: 'boolean',\n      isEditable: true,\n    },\n    {\n      id: '6',\n      category: 'email',\n      key: 'email_queue_enabled',\n      value: 'true',\n      description: '启用邮件队列',\n      type: 'boolean',\n      isEditable: true,\n    },\n    // Notification Settings\n    {\n      id: '7',\n      category: 'notifications',\n      key: 'browser_notifications',\n      value: 'true',\n      description: '启用浏览器通知',\n      type: 'boolean',\n      isEditable: true,\n    },\n    {\n      id: '8',\n      category: 'notifications',\n      key: 'email_notifications',\n      value: 'true',\n      description: '启用邮件通知',\n      type: 'boolean',\n      isEditable: true,\n    },\n    // Security Settings\n    {\n      id: '9',\n      category: 'security',\n      key: 'session_timeout',\n      value: '3600',\n      description: '会话超时时间（秒）',\n      type: 'number',\n      isEditable: true,\n    },\n    {\n      id: '10',\n      category: 'security',\n      key: 'max_login_attempts',\n      value: '5',\n      description: '最大登录尝试次数',\n      type: 'number',\n      isEditable: true,\n    },\n    // Maintenance Settings\n    {\n      id: '11',\n      category: 'maintenance',\n      key: 'maintenance_mode',\n      value: 'false',\n      description: '维护模式',\n      type: 'boolean',\n      isEditable: true,\n    },\n    {\n      id: '12',\n      category: 'maintenance',\n      key: 'backup_enabled',\n      value: 'true',\n      description: '启用自动备份',\n      type: 'boolean',\n      isEditable: true,\n    },\n  ];\n\n  fs.writeFileSync(CONFIG_FILE, JSON.stringify(defaultConfigs, null, 2));\n}\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Verify admin token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({\n      success: false,\n      message: 'No token provided'\n    });\n  }\n\n  const token = authHeader.substring(7);\n  \n  try {\n    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (jwtError) {\n    return res.status(401).json({\n      success: false,\n      message: 'Invalid token'\n    });\n  }\n\n  if (req.method === 'GET') {\n    try {\n      const data = fs.readFileSync(CONFIG_FILE, 'utf8');\n      const configs = JSON.parse(data);\n      \n      return res.status(200).json({\n        success: true,\n        message: 'System configurations retrieved successfully',\n        data: configs,\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to retrieve system configurations',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else if (req.method === 'PUT') {\n    try {\n      const data = fs.readFileSync(CONFIG_FILE, 'utf8');\n      const configs = JSON.parse(data);\n      const { configs: updatedConfigs } = req.body;\n\n      // Update configurations\n      configs.forEach((config: any) => {\n        if (updatedConfigs.hasOwnProperty(config.key) && config.isEditable) {\n          let newValue = updatedConfigs[config.key];\n          \n          // Convert value based on type\n          if (config.type === 'boolean') {\n            newValue = newValue.toString();\n          } else if (config.type === 'number') {\n            newValue = newValue.toString();\n          } else if (config.type === 'json') {\n            newValue = typeof newValue === 'object' ? JSON.stringify(newValue) : newValue;\n          }\n          \n          config.value = newValue;\n          config.updatedAt = new Date().toISOString();\n          config.updatedBy = 'admin'; // TODO: Get from JWT token\n        }\n      });\n\n      fs.writeFileSync(CONFIG_FILE, JSON.stringify(configs, null, 2));\n\n      return res.status(200).json({\n        success: true,\n        message: 'System configurations updated successfully',\n        data: configs,\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to update system configurations',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAExC,uCAAuC;AACvC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;IAC5B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU;QAAE,WAAW;IAAK;AAC3C;AAEA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,cAAc;IAC/B,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,mBAAmB;QACnB;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA,wBAAwB;QACxB;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA,oBAAoB;QACpB;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA,uBAAuB;QACvB;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,YAAY;QACd;KACD;IAED,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,aAAa,KAAK,SAAS,CAAC,gBAAgB,MAAM;AACrE;AAEe,eAAe,QAAQ,GAAmB,EAAE,GAAoB;IAC7E,qBAAqB;IACrB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;IAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;IAEnC,IAAI;QACF,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IAC9C,EAAE,OAAO,UAAU;QACjB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,aAAa;YAC1C,MAAM,UAAU,KAAK,KAAK,CAAC;YAE3B,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO,IAAI,IAAI,MAAM,KAAK,OAAO;QAC/B,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,aAAa;YAC1C,MAAM,UAAU,KAAK,KAAK,CAAC;YAC3B,MAAM,EAAE,SAAS,cAAc,EAAE,GAAG,IAAI,IAAI;YAE5C,wBAAwB;YACxB,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,eAAe,cAAc,CAAC,OAAO,GAAG,KAAK,OAAO,UAAU,EAAE;oBAClE,IAAI,WAAW,cAAc,CAAC,OAAO,GAAG,CAAC;oBAEzC,8BAA8B;oBAC9B,IAAI,OAAO,IAAI,KAAK,WAAW;wBAC7B,WAAW,SAAS,QAAQ;oBAC9B,OAAO,IAAI,OAAO,IAAI,KAAK,UAAU;wBACnC,WAAW,SAAS,QAAQ;oBAC9B,OAAO,IAAI,OAAO,IAAI,KAAK,QAAQ;wBACjC,WAAW,OAAO,aAAa,WAAW,KAAK,SAAS,CAAC,YAAY;oBACvE;oBAEA,OAAO,KAAK,GAAG;oBACf,OAAO,SAAS,GAAG,IAAI,OAAO,WAAW;oBACzC,OAAO,SAAS,GAAG,SAAS,2BAA2B;gBACzD;YACF;YAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,MAAM;YAE5D,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO;QACL,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}