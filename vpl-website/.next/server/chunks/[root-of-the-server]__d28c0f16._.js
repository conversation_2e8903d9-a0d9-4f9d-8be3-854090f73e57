module.exports = {

"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsonwebtoken", () => require("jsonwebtoken"));

module.exports = mod;
}}),
"[externals]/bcryptjs [external] (bcryptjs, esm_import)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("bcryptjs");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/pages/api/admin/users/index.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": ()=>handler
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$bcryptjs__$5b$external$5d$__$28$bcryptjs$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/bcryptjs [external] (bcryptjs, esm_import)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$bcryptjs__$5b$external$5d$__$28$bcryptjs$2c$__esm_import$29$__
]);
[__TURBOPACK__imported__module__$5b$externals$5d2f$bcryptjs__$5b$external$5d$__$28$bcryptjs$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
;
;
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const USERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'admin-users.json');
// Ensure data directory and file exist
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
        recursive: true
    });
}
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(USERS_FILE)) {
    // Create default admin user with enhanced structure
    const defaultAdmin = {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        fullName: '系统管理员',
        role: {
            id: '1',
            name: 'super_admin',
            displayName: '超级管理员',
            description: '拥有所有系统权限',
            permissions: [],
            isSystem: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        },
        permissions: [],
        isActive: true,
        loginAttempts: 0,
        twoFactorEnabled: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system',
        // Store hashed password separately (not in the AdminUser type for security)
        password: __TURBOPACK__imported__module__$5b$externals$5d2f$bcryptjs__$5b$external$5d$__$28$bcryptjs$2c$__esm_import$29$__["default"].hashSync('admin123', 10)
    };
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USERS_FILE, JSON.stringify([
        defaultAdmin
    ], null, 2));
}
function verifyToken(token) {
    try {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__["default"].verify(token, process.env.JWT_SECRET || 'your-secret-key');
    } catch (error) {
        throw new Error('Invalid token');
    }
}
function checkPermission(user, permission) {
    // Super admin has all permissions
    if (user.role === 'super_admin') return true;
    // Check specific permissions
    return user.permissions?.includes(permission) || false;
}
async function handler(req, res) {
    try {
        // Verify authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'No token provided'
            });
        }
        const token = authHeader.substring(7);
        const decoded = verifyToken(token);
        if (req.method === 'GET') {
            // Check read permission
            if (!checkPermission(decoded, 'users:read')) {
                return res.status(403).json({
                    success: false,
                    message: 'Insufficient permissions'
                });
            }
            const { page = '1', limit = '10', search, role, status } = req.query;
            const pageNum = parseInt(page);
            const limitNum = parseInt(limit);
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(USERS_FILE, 'utf8');
            let users = JSON.parse(data);
            // Apply filters
            if (search && typeof search === 'string') {
                const searchLower = search.toLowerCase();
                users = users.filter((user)=>user.username.toLowerCase().includes(searchLower) || user.fullName.toLowerCase().includes(searchLower) || user.email.toLowerCase().includes(searchLower));
            }
            if (role && typeof role === 'string') {
                users = users.filter((user)=>user.role?.name === role || user.role === role);
            }
            if (status && typeof status === 'string') {
                const isActive = status === 'active';
                users = users.filter((user)=>user.isActive === isActive);
            }
            // Remove password from response
            const safeUsers = users.map((user)=>{
                const { password, ...safeUser } = user;
                return safeUser;
            });
            // Pagination
            const total = safeUsers.length;
            const totalPages = Math.ceil(total / limitNum);
            const startIndex = (pageNum - 1) * limitNum;
            const endIndex = startIndex + limitNum;
            const paginatedUsers = safeUsers.slice(startIndex, endIndex);
            return res.status(200).json({
                success: true,
                message: 'Users retrieved successfully',
                data: paginatedUsers,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total,
                    totalPages
                }
            });
        } else if (req.method === 'POST') {
            // Check create permission
            if (!checkPermission(decoded, 'users:create')) {
                return res.status(403).json({
                    success: false,
                    message: 'Insufficient permissions'
                });
            }
            const createRequest = req.body;
            // Validate required fields
            if (!createRequest.username || !createRequest.email || !createRequest.fullName || !createRequest.password || !createRequest.role) {
                return res.status(400).json({
                    success: false,
                    message: 'Missing required fields'
                });
            }
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(USERS_FILE, 'utf8');
            const users = JSON.parse(data);
            // Check if username or email already exists
            const existingUser = users.find((user)=>user.username === createRequest.username || user.email === createRequest.email);
            if (existingUser) {
                return res.status(400).json({
                    success: false,
                    message: 'Username or email already exists'
                });
            }
            const hashedPassword = await __TURBOPACK__imported__module__$5b$externals$5d2f$bcryptjs__$5b$external$5d$__$28$bcryptjs$2c$__esm_import$29$__["default"].hash(createRequest.password, 10);
            const newUser = {
                id: Date.now().toString(),
                username: createRequest.username,
                email: createRequest.email,
                fullName: createRequest.fullName,
                role: {
                    id: '2',
                    name: createRequest.role,
                    displayName: createRequest.role === 'admin' ? '管理员' : '用户',
                    description: '',
                    permissions: [],
                    isSystem: false,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                permissions: [],
                isActive: createRequest.isActive ?? true,
                loginAttempts: 0,
                twoFactorEnabled: false,
                phone: createRequest.phone,
                department: createRequest.department,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: decoded.username,
                password: hashedPassword
            };
            users.push(newUser);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
            // Remove password from response
            const { password: _, ...safeUser } = newUser;
            return res.status(201).json({
                success: true,
                message: 'User created successfully',
                data: safeUser
            });
        } else {
            return res.status(405).json({
                success: false,
                message: 'Method not allowed'
            });
        }
    } catch (error) {
        console.error('Users API error:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d28c0f16._.js.map