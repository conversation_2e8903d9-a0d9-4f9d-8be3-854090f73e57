{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/logs.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\nimport fs from 'fs';\nimport path from 'path';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst LOGS_FILE = path.join(DATA_DIR, 'system-logs.json');\n\n// Ensure data directory and file exist\nif (!fs.existsSync(DATA_DIR)) {\n  fs.mkdirSync(DATA_DIR, { recursive: true });\n}\n\nif (!fs.existsSync(LOGS_FILE)) {\n  // Create some sample logs\n  const sampleLogs = [\n    {\n      id: '1',\n      timestamp: new Date().toISOString(),\n      level: 'info',\n      category: 'system',\n      message: '系统启动成功',\n      details: { version: '1.0.0', environment: 'production' },\n    },\n    {\n      id: '2',\n      timestamp: new Date(Date.now() - 60000).toISOString(),\n      level: 'info',\n      category: 'auth',\n      message: '管理员登录成功',\n      details: { username: 'admin' },\n      ipAddress: '127.0.0.1',\n    },\n    {\n      id: '3',\n      timestamp: new Date(Date.now() - 120000).toISOString(),\n      level: 'warning',\n      category: 'email',\n      message: 'SMTP连接超时，正在重试',\n      details: { host: 'smtp.example.com', port: 587 },\n    },\n  ];\n  fs.writeFileSync(LOGS_FILE, JSON.stringify(sampleLogs, null, 2));\n}\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Verify admin token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({\n      success: false,\n      message: 'No token provided'\n    });\n  }\n\n  const token = authHeader.substring(7);\n  \n  try {\n    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (jwtError) {\n    return res.status(401).json({\n      success: false,\n      message: 'Invalid token'\n    });\n  }\n\n  if (req.method === 'GET') {\n    try {\n      const data = fs.readFileSync(LOGS_FILE, 'utf8');\n      let logs = JSON.parse(data);\n      \n      // Sort by timestamp (newest first)\n      logs.sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n\n      // Apply pagination\n      const page = parseInt(req.query.page as string) || 1;\n      const limit = parseInt(req.query.limit as string) || 50;\n      const startIndex = (page - 1) * limit;\n      const endIndex = startIndex + limit;\n      \n      const paginatedLogs = logs.slice(startIndex, endIndex);\n      \n      return res.status(200).json({\n        success: true,\n        message: 'System logs retrieved successfully',\n        data: paginatedLogs,\n        pagination: {\n          page,\n          limit,\n          total: logs.length,\n          totalPages: Math.ceil(logs.length / limit),\n        },\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to retrieve system logs',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else if (req.method === 'POST') {\n    // Add new log entry\n    try {\n      const data = fs.readFileSync(LOGS_FILE, 'utf8');\n      const logs = JSON.parse(data);\n      \n      const newLog = {\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n        ...req.body,\n      };\n\n      logs.push(newLog);\n      \n      // Keep only the last 10000 logs to prevent file from growing too large\n      if (logs.length > 10000) {\n        logs.splice(0, logs.length - 10000);\n      }\n\n      fs.writeFileSync(LOGS_FILE, JSON.stringify(logs, null, 2));\n\n      return res.status(201).json({\n        success: true,\n        message: 'Log entry created successfully',\n        data: newLog,\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to create log entry',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else if (req.method === 'DELETE') {\n    // Clear all logs\n    try {\n      fs.writeFileSync(LOGS_FILE, JSON.stringify([], null, 2));\n\n      // Add a log entry about the clearing\n      const clearLog = {\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n        level: 'info',\n        category: 'system',\n        message: '系统日志已清空',\n        details: { clearedBy: 'admin' },\n      };\n\n      fs.writeFileSync(LOGS_FILE, JSON.stringify([clearLog], null, 2));\n\n      return res.status(200).json({\n        success: true,\n        message: 'All logs cleared successfully',\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to clear logs',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n}\n\n// Helper function to add log entries from other parts of the application\nexport const addLogEntry = (level: string, category: string, message: string, details?: any, userId?: string, ipAddress?: string) => {\n  try {\n    const data = fs.readFileSync(LOGS_FILE, 'utf8');\n    const logs = JSON.parse(data);\n    \n    const newLog = {\n      id: Date.now().toString(),\n      timestamp: new Date().toISOString(),\n      level,\n      category,\n      message,\n      details,\n      userId,\n      ipAddress,\n    };\n\n    logs.push(newLog);\n    \n    // Keep only the last 10000 logs\n    if (logs.length > 10000) {\n      logs.splice(0, logs.length - 10000);\n    }\n\n    fs.writeFileSync(LOGS_FILE, JSON.stringify(logs, null, 2));\n  } catch (error) {\n    console.error('Failed to add log entry:', error);\n  }\n};\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAEtC,uCAAuC;AACvC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;IAC5B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU;QAAE,WAAW;IAAK;AAC3C;AAEA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;IAC7B,0BAA0B;IAC1B,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO;YACP,UAAU;YACV,SAAS;YACT,SAAS;gBAAE,SAAS;gBAAS,aAAa;YAAa;QACzD;QACA;YACE,IAAI;YACJ,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,WAAW;YACnD,OAAO;YACP,UAAU;YACV,SAAS;YACT,SAAS;gBAAE,UAAU;YAAQ;YAC7B,WAAW;QACb;QACA;YACE,IAAI;YACJ,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,QAAQ,WAAW;YACpD,OAAO;YACP,UAAU;YACV,SAAS;YACT,SAAS;gBAAE,MAAM;gBAAoB,MAAM;YAAI;QACjD;KACD;IACD,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC,YAAY,MAAM;AAC/D;AAEe,eAAe,QAAQ,GAAmB,EAAE,GAAoB;IAC7E,qBAAqB;IACrB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;IAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;IAEnC,IAAI;QACF,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IAC9C,EAAE,OAAO,UAAU;QACjB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;YACxC,IAAI,OAAO,KAAK,KAAK,CAAC;YAEtB,mCAAmC;YACnC,KAAK,IAAI,CAAC,CAAC,GAAQ,IAAW,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAE7F,mBAAmB;YACnB,MAAM,OAAO,SAAS,IAAI,KAAK,CAAC,IAAI,KAAe;YACnD,MAAM,QAAQ,SAAS,IAAI,KAAK,CAAC,KAAK,KAAe;YACrD,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAE9B,MAAM,gBAAgB,KAAK,KAAK,CAAC,YAAY;YAE7C,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;gBACN,YAAY;oBACV;oBACA;oBACA,OAAO,KAAK,MAAM;oBAClB,YAAY,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG;gBACtC;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO,IAAI,IAAI,MAAM,KAAK,QAAQ;QAChC,oBAAoB;QACpB,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;YACxC,MAAM,OAAO,KAAK,KAAK,CAAC;YAExB,MAAM,SAAS;gBACb,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW,IAAI,OAAO,WAAW;gBACjC,GAAG,IAAI,IAAI;YACb;YAEA,KAAK,IAAI,CAAC;YAEV,uEAAuE;YACvE,IAAI,KAAK,MAAM,GAAG,OAAO;gBACvB,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,GAAG;YAC/B;YAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM;YAEvD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO,IAAI,IAAI,MAAM,KAAK,UAAU;QAClC,iBAAiB;QACjB,IAAI;YACF,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;YAErD,qCAAqC;YACrC,MAAM,WAAW;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW,IAAI,OAAO,WAAW;gBACjC,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,SAAS;oBAAE,WAAW;gBAAQ;YAChC;YAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC;gBAAC;aAAS,EAAE,MAAM;YAE7D,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO;QACL,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF;AAGO,MAAM,cAAc,CAAC,OAAe,UAAkB,SAAiB,SAAe,QAAiB;IAC5G,IAAI;QACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,WAAW;QACxC,MAAM,OAAO,KAAK,KAAK,CAAC;QAExB,MAAM,SAAS;YACb,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,KAAK,IAAI,CAAC;QAEV,gCAAgC;QAChC,IAAI,KAAK,MAAM,GAAG,OAAO;YACvB,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,GAAG;QAC/B;QAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF", "debugId": null}}]}