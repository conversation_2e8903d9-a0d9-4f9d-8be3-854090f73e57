module.exports = {

"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsonwebtoken", () => require("jsonwebtoken"));

module.exports = mod;
}}),
"[project]/src/pages/api/admin/dashboard.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>handler
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)");
;
// Mock data - in a real application, this would come from a database
const mockSubmissions = [
    {
        id: '1',
        companyName: '深圳贸易有限公司',
        contactPerson: '张经理',
        email: '<EMAIL>',
        phone: '+86 138-0000-0000',
        serviceType: 'foreign_trade_lines',
        message: '我们需要稳定的外贸网络线路，主要连接欧美市场，请提供详细方案和报价。',
        submittedAt: new Date().toISOString(),
        status: 'pending'
    },
    {
        id: '2',
        companyName: '广州电商科技',
        contactPerson: '李总',
        email: '<EMAIL>',
        phone: '+86 139-0000-0000',
        serviceType: 'ecommerce_lines',
        message: '跨境电商平台需要优化网络连接，提升用户访问速度。',
        submittedAt: new Date(Date.now() - 86400000).toISOString(),
        status: 'contacted'
    },
    {
        id: '3',
        companyName: '北京科技集团',
        contactPerson: '王主管',
        email: '<EMAIL>',
        phone: '+86 137-0000-0000',
        serviceType: 'vpn_services',
        message: '企业需要安全的VPN服务，支持多设备接入。',
        submittedAt: new Date(Date.now() - *********).toISOString(),
        status: 'closed'
    }
];
async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
    try {
        // Verify admin token
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'No token provided'
            });
        }
        const token = authHeader.substring(7);
        try {
            __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__["default"].verify(token, process.env.JWT_SECRET || 'your-secret-key');
        } catch (jwtError) {
            return res.status(401).json({
                success: false,
                message: 'Invalid token'
            });
        }
        // In a real application, you would fetch this data from your database
        // For now, we'll return mock data
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todaySubmissions = mockSubmissions.filter((submission)=>new Date(submission.submittedAt) >= today);
        const pendingSubmissions = mockSubmissions.filter((submission)=>submission.status === 'pending');
        const stats = {
            totalInquiries: mockSubmissions.length,
            todayInquiries: todaySubmissions.length,
            pendingInquiries: pendingSubmissions.length,
            totalUsers: 156
        };
        return res.status(200).json({
            success: true,
            message: 'Dashboard data retrieved successfully',
            stats,
            recentSubmissions: mockSubmissions.slice(0, 10) // Return latest 10 submissions
        });
    } catch (error) {
        console.error('Dashboard data error:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c0d3e2ba._.js.map