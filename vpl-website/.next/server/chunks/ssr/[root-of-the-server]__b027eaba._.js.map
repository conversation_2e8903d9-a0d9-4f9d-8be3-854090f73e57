{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/email-settings.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  EnvelopeIcon,\n  CogIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  EyeSlashIcon\n} from '@heroicons/react/24/outline';\n\ninterface EmailConfig {\n  id: string;\n  name: string;\n  smtpHost: string;\n  smtpPort: number;\n  smtpUser: string;\n  smtpPass: string;\n  smtpFrom: string;\n  smtpFromName: string;\n  isDefault: boolean;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport default function EmailSettingsPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);\n  const [showForm, setShowForm] = useState(false);\n  const [editingConfig, setEditingConfig] = useState<EmailConfig | null>(null);\n  const [testEmail, setTestEmail] = useState('');\n  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);\n  const [showPassword, setShowPassword] = useState<{ [key: string]: boolean }>({});\n  const [formData, setFormData] = useState({\n    name: '',\n    smtpHost: '',\n    smtpPort: 587,\n    smtpUser: '',\n    smtpPass: '',\n    smtpFrom: '',\n    smtpFromName: '',\n    isDefault: false,\n    isActive: true,\n  });\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchEmailConfigs();\n    }\n  }, [isAuthenticated]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchEmailConfigs = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/email-config', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setEmailConfigs(data.data || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch email configs:', error);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      const token = localStorage.getItem('adminToken');\n      const url = editingConfig \n        ? `/api/admin/email-config/${editingConfig.id}`\n        : '/api/admin/email-config';\n      const method = editingConfig ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n\n      if (response.ok) {\n        setShowForm(false);\n        setEditingConfig(null);\n        resetForm();\n        fetchEmailConfigs();\n      }\n    } catch (error) {\n      console.error('Failed to save email config:', error);\n    }\n  };\n\n  const handleEdit = (config: EmailConfig) => {\n    setEditingConfig(config);\n    setFormData({\n      name: config.name,\n      smtpHost: config.smtpHost,\n      smtpPort: config.smtpPort,\n      smtpUser: config.smtpUser,\n      smtpPass: config.smtpPass,\n      smtpFrom: config.smtpFrom,\n      smtpFromName: config.smtpFromName,\n      isDefault: config.isDefault,\n      isActive: config.isActive,\n    });\n    setShowForm(true);\n  };\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('确定要删除这个邮件配置吗？')) return;\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/email-config/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        fetchEmailConfigs();\n      }\n    } catch (error) {\n      console.error('Failed to delete email config:', error);\n    }\n  };\n\n  const handleSetDefault = async (id: string) => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/email-config/${id}/set-default`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        fetchEmailConfigs();\n      }\n    } catch (error) {\n      console.error('Failed to set default config:', error);\n    }\n  };\n\n  const handleTestEmail = async (configId: string) => {\n    if (!testEmail) {\n      alert('请输入测试邮箱地址');\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/email-test', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          configId,\n          testEmail\n        })\n      });\n\n      const result = await response.json();\n      setTestResult(result);\n    } catch (error) {\n      setTestResult({\n        success: false,\n        message: '测试失败：网络错误'\n      });\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      smtpHost: '',\n      smtpPort: 587,\n      smtpUser: '',\n      smtpPass: '',\n      smtpFrom: '',\n      smtpFromName: '',\n      isDefault: false,\n      isActive: true,\n    });\n  };\n\n  const togglePasswordVisibility = (configId: string) => {\n    setShowPassword(prev => ({\n      ...prev,\n      [configId]: !prev[configId]\n    }));\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>邮件配置 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">邮件配置</h1>\n                <p className=\"text-gray-600\">管理SMTP邮件服务器配置</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => {\n                    resetForm();\n                    setEditingConfig(null);\n                    setShowForm(true);\n                  }}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  添加配置\n                </button>\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Email Configurations List */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">邮件服务器配置</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      配置名称\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      SMTP服务器\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      发件人\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      操作\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {emailConfigs.map((config) => (\n                    <tr key={config.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {config.name}\n                              {config.isDefault && (\n                                <span className=\"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                                  默认\n                                </span>\n                              )}\n                            </div>\n                            <div className=\"text-sm text-gray-500\">\n                              用户: {config.smtpUser}\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm text-gray-900\">{config.smtpHost}:{config.smtpPort}</div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm text-gray-900\">{config.smtpFromName}</div>\n                        <div className=\"text-sm text-gray-500\">{config.smtpFrom}</div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          config.isActive \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {config.isActive ? '启用' : '禁用'}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-2\">\n                          <button\n                            onClick={() => handleEdit(config)}\n                            className=\"text-blue-600 hover:text-blue-900\"\n                            title=\"编辑\"\n                          >\n                            <PencilIcon className=\"h-4 w-4\" />\n                          </button>\n                          {!config.isDefault && (\n                            <button\n                              onClick={() => handleSetDefault(config.id)}\n                              className=\"text-green-600 hover:text-green-900\"\n                              title=\"设为默认\"\n                            >\n                              <CheckCircleIcon className=\"h-4 w-4\" />\n                            </button>\n                          )}\n                          <button\n                            onClick={() => handleDelete(config.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                            title=\"删除\"\n                          >\n                            <TrashIcon className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Email Test Section */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">邮件测试</h3>\n            </div>\n            <div className=\"px-6 py-4\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex-1\">\n                  <input\n                    type=\"email\"\n                    value={testEmail}\n                    onChange={(e) => setTestEmail(e.target.value)}\n                    placeholder=\"输入测试邮箱地址\"\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <select className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\">\n                  <option value=\"\">选择配置</option>\n                  {emailConfigs.filter(c => c.isActive).map(config => (\n                    <option key={config.id} value={config.id}>{config.name}</option>\n                  ))}\n                </select>\n                <button\n                  onClick={() => {\n                    const select = document.querySelector('select') as HTMLSelectElement;\n                    if (select?.value) {\n                      handleTestEmail(select.value);\n                    }\n                  }}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  <EnvelopeIcon className=\"h-4 w-4 mr-2\" />\n                  发送测试邮件\n                </button>\n              </div>\n              \n              {testResult && (\n                <div className={`mt-4 p-4 rounded-md ${\n                  testResult.success \n                    ? 'bg-green-50 border border-green-200' \n                    : 'bg-red-50 border border-red-200'\n                }`}>\n                  <div className=\"flex\">\n                    {testResult.success ? (\n                      <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                    ) : (\n                      <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                    )}\n                    <div className=\"ml-3\">\n                      <p className={`text-sm ${\n                        testResult.success ? 'text-green-800' : 'text-red-800'\n                      }`}>\n                        {testResult.message}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Configuration Form Modal */}\n          {showForm && (\n            <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n              <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n                <div className=\"mt-3\">\n                  <div className=\"flex justify-between items-center mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">\n                      {editingConfig ? '编辑邮件配置' : '添加邮件配置'}\n                    </h3>\n                    <button\n                      onClick={() => {\n                        setShowForm(false);\n                        setEditingConfig(null);\n                        resetForm();\n                      }}\n                      className=\"text-gray-400 hover:text-gray-600\"\n                    >\n                      ×\n                    </button>\n                  </div>\n                  \n                  <form onSubmit={handleSubmit} className=\"space-y-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          配置名称\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.name}\n                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          SMTP服务器\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.smtpHost}\n                          onChange={(e) => setFormData({ ...formData, smtpHost: e.target.value })}\n                          placeholder=\"smtp.example.com\"\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          端口\n                        </label>\n                        <input\n                          type=\"number\"\n                          required\n                          value={formData.smtpPort}\n                          onChange={(e) => setFormData({ ...formData, smtpPort: parseInt(e.target.value) })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          用户名\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.smtpUser}\n                          onChange={(e) => setFormData({ ...formData, smtpUser: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          密码\n                        </label>\n                        <input\n                          type=\"password\"\n                          required\n                          value={formData.smtpPass}\n                          onChange={(e) => setFormData({ ...formData, smtpPass: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          发件人邮箱\n                        </label>\n                        <input\n                          type=\"email\"\n                          required\n                          value={formData.smtpFrom}\n                          onChange={(e) => setFormData({ ...formData, smtpFrom: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div className=\"md:col-span-2\">\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          发件人名称\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.smtpFromName}\n                          onChange={(e) => setFormData({ ...formData, smtpFromName: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-4\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.isDefault}\n                          onChange={(e) => setFormData({ ...formData, isDefault: e.target.checked })}\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">设为默认配置</span>\n                      </label>\n                      \n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.isActive}\n                          onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">启用配置</span>\n                      </label>\n                    </div>\n                    \n                    <div className=\"flex justify-end space-x-3 pt-4\">\n                      <button\n                        type=\"button\"\n                        onClick={() => {\n                          setShowForm(false);\n                          setEditingConfig(null);\n                          resetForm();\n                        }}\n                        className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                      >\n                        取消\n                      </button>\n                      <button\n                        type=\"submit\"\n                        className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700\"\n                      >\n                        {editingConfig ? '更新' : '保存'}\n                      </button>\n                    </div>\n                  </form>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AA2Be,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAsB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAgD;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,cAAc;QACd,WAAW;QACX,UAAU;IACZ;IACA,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB,KAAK,IAAI,IAAI,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,MAAM,gBACR,CAAC,wBAAwB,EAAE,cAAc,EAAE,EAAE,GAC7C;YACJ,MAAM,SAAS,gBAAgB,QAAQ;YAEvC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY;gBACZ,iBAAiB;gBACjB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB;QACjB,YAAY;YACV,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,cAAc,OAAO,YAAY;YACjC,WAAW,OAAO,SAAS;YAC3B,UAAU,OAAO,QAAQ;QAC3B;QACA,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,kBAAkB;QAE/B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,wBAAwB,EAAE,IAAI,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,wBAAwB,EAAE,GAAG,YAAY,CAAC,EAAE;gBACxE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,WAAW;YACd,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,cAAc;gBACZ,SAAS;gBACT,SAAS;YACX;QACF;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,WAAW;YACX,UAAU;QACZ;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAC7B,CAAC;IACH;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS;oDACP;oDACA,iBAAiB;oDACjB,YAAY;gDACd;gDACA,WAAU;;kEAEV,qKAAC,wMAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAM,WAAU;;8DACf,qKAAC;oDAAM,WAAU;8DACf,cAAA,qKAAC;;0EACC,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,qKAAC;oDAAM,WAAU;8DACd,aAAa,GAAG,CAAC,CAAC,uBACjB,qKAAC;4DAAmB,WAAU;;8EAC5B,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAI,WAAU;kFACb,cAAA,qKAAC;;8FACC,qKAAC;oFAAI,WAAU;;wFACZ,OAAO,IAAI;wFACX,OAAO,SAAS,kBACf,qKAAC;4FAAK,WAAU;sGAA2G;;;;;;;;;;;;8FAK/H,qKAAC;oFAAI,WAAU;;wFAAwB;wFAChC,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8EAK5B,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAI,WAAU;;4EAAyB,OAAO,QAAQ;4EAAC;4EAAE,OAAO,QAAQ;;;;;;;;;;;;8EAE3E,qKAAC;oEAAG,WAAU;;sFACZ,qKAAC;4EAAI,WAAU;sFAAyB,OAAO,YAAY;;;;;;sFAC3D,qKAAC;4EAAI,WAAU;sFAAyB,OAAO,QAAQ;;;;;;;;;;;;8EAEzD,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAK,WAAW,CAAC,wEAAwE,EACxF,OAAO,QAAQ,GACX,gCACA,2BACJ;kFACC,OAAO,QAAQ,GAAG,OAAO;;;;;;;;;;;8EAG9B,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFACC,SAAS,IAAM,WAAW;gFAC1B,WAAU;gFACV,OAAM;0FAEN,cAAA,qKAAC,4MAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;;;;;;4EAEvB,CAAC,OAAO,SAAS,kBAChB,qKAAC;gFACC,SAAS,IAAM,iBAAiB,OAAO,EAAE;gFACzC,WAAU;gFACV,OAAM;0FAEN,cAAA,qKAAC,sNAAA,CAAA,kBAAe;oFAAC,WAAU;;;;;;;;;;;0FAG/B,qKAAC;gFACC,SAAS,IAAM,aAAa,OAAO,EAAE;gFACrC,WAAU;gFACV,OAAM;0FAEN,cAAA,qKAAC,0MAAA,CAAA,YAAS;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2DAzDpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAqE5B,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,aAAY;4DACZ,WAAU;;;;;;;;;;;kEAGd,qKAAC;wDAAO,WAAU;;0EAChB,qKAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA,uBACxC,qKAAC;oEAAuB,OAAO,OAAO,EAAE;8EAAG,OAAO,IAAI;mEAAzC,OAAO,EAAE;;;;;;;;;;;kEAG1B,qKAAC;wDACC,SAAS;4DACP,MAAM,SAAS,SAAS,aAAa,CAAC;4DACtC,IAAI,QAAQ,OAAO;gEACjB,gBAAgB,OAAO,KAAK;4DAC9B;wDACF;wDACA,WAAU;;0EAEV,qKAAC,gNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;4CAK5C,4BACC,qKAAC;gDAAI,WAAW,CAAC,oBAAoB,EACnC,WAAW,OAAO,GACd,wCACA,mCACJ;0DACA,cAAA,qKAAC;oDAAI,WAAU;;wDACZ,WAAW,OAAO,iBACjB,qKAAC,sNAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;iFAE3B,qKAAC,sOAAA,CAAA,0BAAuB;4DAAC,WAAU;;;;;;sEAErC,qKAAC;4DAAI,WAAU;sEACb,cAAA,qKAAC;gEAAE,WAAW,CAAC,QAAQ,EACrB,WAAW,OAAO,GAAG,mBAAmB,gBACxC;0EACC,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUhC,0BACC,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAG,WAAU;kEACX,gBAAgB,WAAW;;;;;;kEAE9B,qKAAC;wDACC,SAAS;4DACP,YAAY;4DACZ,iBAAiB;4DACjB;wDACF;wDACA,WAAU;kEACX;;;;;;;;;;;;0DAKH,qKAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACjE,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,aAAY;wEACZ,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK;4EAAE;wEAC/E,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,qKAAC;gEAAI,WAAU;;kFACb,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,YAAY;wEAC5B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACzE,WAAU;;;;;;;;;;;;;;;;;;kEAKhB,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAM,WAAU;;kFACf,qKAAC;wEACC,MAAK;wEACL,SAAS,SAAS,SAAS;wEAC3B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,WAAW,EAAE,MAAM,CAAC,OAAO;4EAAC;wEACxE,WAAU;;;;;;kFAEZ,qKAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAG/C,qKAAC;gEAAM,WAAU;;kFACf,qKAAC;wEACC,MAAK;wEACL,SAAS,SAAS,QAAQ;wEAC1B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,OAAO;4EAAC;wEACvE,WAAU;;;;;;kFAEZ,qKAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;kEAIjD,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEACC,MAAK;gEACL,SAAS;oEACP,YAAY;oEACZ,iBAAiB;oEACjB;gEACF;gEACA,WAAU;0EACX;;;;;;0EAGD,qKAAC;gEACC,MAAK;gEACL,WAAU;0EAET,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhD", "debugId": null}}]}