{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/users/index.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport Link from 'next/link';\nimport { \n  UsersIcon, \n  PlusIcon, \n  MagnifyingGlassIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  ArrowRightOnRectangleIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  ShieldCheckIcon\n} from '@heroicons/react/24/outline';\nimport { AdminUser } from '@/types/user';\n\ninterface UserListResponse {\n  success: boolean;\n  message: string;\n  data: AdminUser[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\nexport default function UsersManagement() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [users, setUsers] = useState<AdminUser[]>([]);\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    totalPages: 0\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [isDeleting, setIsDeleting] = useState<string | null>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchUsers();\n    }\n  }, [isAuthenticated, pagination.page, searchTerm, roleFilter, statusFilter]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    fetch('/api/admin/verify', {\n      headers: { 'Authorization': `Bearer ${token}` }\n    })\n    .then(response => {\n      if (response.ok) {\n        setIsAuthenticated(true);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const params = new URLSearchParams({\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n        ...(searchTerm && { search: searchTerm }),\n        ...(roleFilter && { role: roleFilter }),\n        ...(statusFilter && { status: statusFilter })\n      });\n\n      const response = await fetch(`/api/admin/users?${params}`, {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (response.ok) {\n        const result: UserListResponse = await response.json();\n        setUsers(result.data);\n        setPagination(result.pagination);\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n    }\n  };\n\n  const handleDeleteUser = async (userId: string) => {\n    if (!confirm('确定要删除这个用户吗？此操作不可撤销。')) {\n      return;\n    }\n\n    setIsDeleting(userId);\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/users/${userId}`, {\n        method: 'DELETE',\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (response.ok) {\n        fetchUsers(); // Refresh the list\n      } else {\n        alert('删除用户失败');\n      }\n    } catch (error) {\n      alert('删除用户时发生错误');\n    } finally {\n      setIsDeleting(null);\n    }\n  };\n\n  const handleToggleStatus = async (userId: string, currentStatus: boolean) => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/users/${userId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ isActive: !currentStatus })\n      });\n\n      if (response.ok) {\n        fetchUsers(); // Refresh the list\n      } else {\n        alert('更新用户状态失败');\n      }\n    } catch (error) {\n      alert('更新用户状态时发生错误');\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const getRoleDisplayName = (role: any) => {\n    if (typeof role === 'string') {\n      switch (role) {\n        case 'super_admin': return '超级管理员';\n        case 'admin': return '管理员';\n        case 'manager': return '业务经理';\n        case 'support': return '技术支持';\n        default: return role;\n      }\n    }\n    return role?.displayName || role?.name || '未知';\n  };\n\n  const getRoleBadgeColor = (role: any) => {\n    const roleName = typeof role === 'string' ? role : role?.name;\n    switch (roleName) {\n      case 'super_admin': return 'bg-red-100 text-red-800';\n      case 'admin': return 'bg-blue-100 text-blue-800';\n      case 'manager': return 'bg-green-100 text-green-800';\n      case 'support': return 'bg-yellow-100 text-yellow-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>用户管理 - VPL管理后台</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3\">\n                  <span className=\"text-white font-bold text-lg\">VPL</span>\n                </div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">用户管理</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-700 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-1\" />\n                  退出登录\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Actions Bar */}\n          <div className=\"mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n            <div className=\"flex items-center space-x-4 mb-4 sm:mb-0\">\n              <Link\n                href=\"/admin/users/create\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                添加用户\n              </Link>\n            </div>\n\n            {/* Search and Filters */}\n            <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2\">\n              <div className=\"relative\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索用户...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n              </div>\n              \n              <select\n                value={roleFilter}\n                onChange={(e) => setRoleFilter(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              >\n                <option value=\"\">所有角色</option>\n                <option value=\"super_admin\">超级管理员</option>\n                <option value=\"admin\">管理员</option>\n                <option value=\"manager\">业务经理</option>\n                <option value=\"support\">技术支持</option>\n              </select>\n\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              >\n                <option value=\"\">所有状态</option>\n                <option value=\"active\">活跃</option>\n                <option value=\"inactive\">禁用</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Users Table */}\n          <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        用户信息\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        角色\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        状态\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        最后登录\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        创建时间\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        操作\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <tr key={user.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                                <UsersIcon className=\"h-6 w-6 text-gray-600\" />\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">\n                                {user.fullName}\n                              </div>\n                              <div className=\"text-sm text-gray-500\">\n                                {user.username} • {user.email}\n                              </div>\n                              {user.phone && (\n                                <div className=\"text-sm text-gray-500\">\n                                  {user.phone}\n                                </div>\n                              )}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(user.role)}`}>\n                            {getRoleDisplayName(user.role)}\n                          </span>\n                          {user.twoFactorEnabled && (\n                            <div className=\"mt-1\">\n                              <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\n                                <ShieldCheckIcon className=\"h-3 w-3 mr-1\" />\n                                2FA\n                              </span>\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <button\n                            onClick={() => handleToggleStatus(user.id, user.isActive)}\n                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                              user.isActive \n                                ? 'bg-green-100 text-green-800 hover:bg-green-200' \n                                : 'bg-red-100 text-red-800 hover:bg-red-200'\n                            }`}\n                          >\n                            {user.isActive ? (\n                              <>\n                                <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\n                                活跃\n                              </>\n                            ) : (\n                              <>\n                                <XCircleIcon className=\"h-3 w-3 mr-1\" />\n                                禁用\n                              </>\n                            )}\n                          </button>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString('zh-CN') : '从未登录'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {new Date(user.createdAt).toLocaleDateString('zh-CN')}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex items-center justify-end space-x-2\">\n                            <Link\n                              href={`/admin/users/${user.id}`}\n                              className=\"text-blue-600 hover:text-blue-900\"\n                            >\n                              <EyeIcon className=\"h-4 w-4\" />\n                            </Link>\n                            <Link\n                              href={`/admin/users/${user.id}/edit`}\n                              className=\"text-indigo-600 hover:text-indigo-900\"\n                            >\n                              <PencilIcon className=\"h-4 w-4\" />\n                            </Link>\n                            {user.role?.name !== 'super_admin' && (\n                              <button\n                                onClick={() => handleDeleteUser(user.id)}\n                                disabled={isDeleting === user.id}\n                                className=\"text-red-600 hover:text-red-900 disabled:opacity-50\"\n                              >\n                                <TrashIcon className=\"h-4 w-4\" />\n                              </button>\n                            )}\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Pagination */}\n              {pagination.totalPages > 1 && (\n                <div className=\"mt-6 flex items-center justify-between\">\n                  <div className=\"text-sm text-gray-700\">\n                    显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}\n                    {Math.min(pagination.page * pagination.limit, pagination.total)} 条，\n                    共 {pagination.total} 条记录\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}\n                      disabled={pagination.page === 1}\n                      className=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n                    >\n                      上一页\n                    </button>\n                    <span className=\"px-3 py-1 text-sm text-gray-700\">\n                      第 {pagination.page} 页，共 {pagination.totalPages} 页\n                    </span>\n                    <button\n                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                      disabled={pagination.page === pagination.totalPages}\n                      className=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n                    >\n                      下一页\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AA0Be,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAiB,WAAW,IAAI;QAAE;QAAY;QAAY;KAAa;IAE3E,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,qBAAqB;YACzB,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD,GACC,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS,EAAE,EAAE;gBACf,mBAAmB;YACrB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAChC,GAAI,cAAc;oBAAE,QAAQ;gBAAW,CAAC;gBACxC,GAAI,cAAc;oBAAE,MAAM;gBAAW,CAAC;gBACtC,GAAI,gBAAgB;oBAAE,QAAQ;gBAAa,CAAC;YAC9C;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBACzD,SAAS;oBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAAC;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAA2B,MAAM,SAAS,IAAI;gBACpD,SAAS,OAAO,IAAI;gBACpB,cAAc,OAAO,UAAU;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,wBAAwB;YACnC;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAAC;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc,mBAAmB;YACnC,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU,CAAC;gBAAc;YAClD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc,mBAAmB;YACnC,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,SAAS,UAAU;YAC5B,OAAQ;gBACN,KAAK;oBAAe,OAAO;gBAC3B,KAAK;oBAAS,OAAO;gBACrB,KAAK;oBAAW,OAAO;gBACvB,KAAK;oBAAW,OAAO;gBACvB;oBAAS,OAAO;YAClB;QACF;QACA,OAAO,MAAM,eAAe,MAAM,QAAQ;IAC5C;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,OAAO,SAAS,WAAW,OAAO,MAAM;QACzD,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAO,WAAU;kCAChB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;0DAGD,qKAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,qKAAC,0OAAA,CAAA,4BAAyB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhE,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,qKAAC,wMAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMzC,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC,8NAAA,CAAA,sBAAmB;wDAAC,WAAU;;;;;;kEAC/B,qKAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;0DAId,qKAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;kEAEV,qKAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,qKAAC;wDAAO,OAAM;kEAAc;;;;;;kEAC5B,qKAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,qKAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,qKAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;0DAG1B,qKAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,qKAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,qKAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,qKAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;0CAM/B,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAM,WAAU;;kEACf,qKAAC;wDAAM,WAAU;kEACf,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,qKAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,qKAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,qKAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,qKAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,qKAAC;oEAAG,WAAU;8EAAkF;;;;;;;;;;;;;;;;;kEAKpG,qKAAC;wDAAM,WAAU;kEACd,MAAM,GAAG,CAAC,CAAC,qBACV,qKAAC;gEAAiB,WAAU;;kFAC1B,qKAAC;wEAAG,WAAU;kFACZ,cAAA,qKAAC;4EAAI,WAAU;;8FACb,qKAAC;oFAAI,WAAU;8FACb,cAAA,qKAAC;wFAAI,WAAU;kGACb,cAAA,qKAAC,0MAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;;;;;;;;;;;8FAGzB,qKAAC;oFAAI,WAAU;;sGACb,qKAAC;4FAAI,WAAU;sGACZ,KAAK,QAAQ;;;;;;sGAEhB,qKAAC;4FAAI,WAAU;;gGACZ,KAAK,QAAQ;gGAAC;gGAAI,KAAK,KAAK;;;;;;;wFAE9B,KAAK,KAAK,kBACT,qKAAC;4FAAI,WAAU;sGACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;kFAMrB,qKAAC;wEAAG,WAAU;;0FACZ,qKAAC;gFAAK,WAAW,CAAC,yDAAyD,EAAE,kBAAkB,KAAK,IAAI,GAAG;0FACxG,mBAAmB,KAAK,IAAI;;;;;;4EAE9B,KAAK,gBAAgB,kBACpB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAK,WAAU;;sGACd,qKAAC,sNAAA,CAAA,kBAAe;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;kFAMpD,qKAAC;wEAAG,WAAU;kFACZ,cAAA,qKAAC;4EACC,SAAS,IAAM,mBAAmB,KAAK,EAAE,EAAE,KAAK,QAAQ;4EACxD,WAAW,CAAC,oEAAoE,EAC9E,KAAK,QAAQ,GACT,mDACA,4CACJ;sFAED,KAAK,QAAQ,iBACZ;;kGACE,qKAAC,sNAAA,CAAA,kBAAe;wFAAC,WAAU;;;;;;oFAAiB;;6GAI9C;;kGACE,qKAAC,8MAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;kFAMhD,qKAAC;wEAAG,WAAU;kFACX,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc,CAAC,WAAW;;;;;;kFAE3E,qKAAC;wEAAG,WAAU;kFACX,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;kFAE/C,qKAAC;wEAAG,WAAU;kFACZ,cAAA,qKAAC;4EAAI,WAAU;;8FACb,qKAAC,qHAAA,CAAA,UAAI;oFACH,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;oFAC/B,WAAU;8FAEV,cAAA,qKAAC,sMAAA,CAAA,UAAO;wFAAC,WAAU;;;;;;;;;;;8FAErB,qKAAC,qHAAA,CAAA,UAAI;oFACH,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;oFACpC,WAAU;8FAEV,cAAA,qKAAC,4MAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;;;;;;gFAEvB,KAAK,IAAI,EAAE,SAAS,+BACnB,qKAAC;oFACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;oFACvC,UAAU,eAAe,KAAK,EAAE;oFAChC,WAAU;8FAEV,cAAA,qKAAC,0MAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+DApFtB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;wCAgGvB,WAAW,UAAU,GAAG,mBACvB,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;wDAAwB;wDAChC,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAI;wDAAE;wDAAG;wDACrD,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;wDAAE;wDAC7D,WAAW,KAAK;wDAAC;;;;;;;8DAEtB,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,KAAK,IAAI,GAAG;oEAAE,CAAC;4DACtE,UAAU,WAAW,IAAI,KAAK;4DAC9B,WAAU;sEACX;;;;;;sEAGD,qKAAC;4DAAK,WAAU;;gEAAkC;gEAC7C,WAAW,IAAI;gEAAC;gEAAM,WAAW,UAAU;gEAAC;;;;;;;sEAEjD,qKAAC;4DACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,KAAK,IAAI,GAAG;oEAAE,CAAC;4DACtE,UAAU,WAAW,IAAI,KAAK,WAAW,UAAU;4DACnD,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}]}