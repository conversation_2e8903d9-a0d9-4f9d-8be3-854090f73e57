{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' },\n  { code: 'ru', name: 'Русский', flag: '🇷🇺' },\n];\n\nexport default function LanguageSwitcher() {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];\n\n  const handleLanguageChange = (langCode: string) => {\n    const { pathname, asPath, query } = router;\n    router.push({ pathname, query }, asPath, { locale: langCode });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n        aria-label={t('navigation.language')}\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.name}</span>\n        <span className=\"sm:hidden\">{currentLanguage.flag}</span>\n        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 ${\n                    language.code === router.locale\n                      ? 'bg-blue-50 text-blue-600'\n                      : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {language.code === router.locale && (\n                    <span className=\"ml-auto text-blue-600\">✓</span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC,EAAE;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QACpC,OAAO,IAAI,CAAC;YAAE;YAAU;QAAM,GAAG,QAAQ;YAAE,QAAQ;QAAS;QAC5D,UAAU;IACZ;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAY,EAAE;;kCAEd,qKAAC,gNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,qKAAC;wBAAK,WAAU;kCAAoB,gBAAgB,IAAI;;;;;;kCACxD,qKAAC;wBAAK,WAAU;kCAAa,gBAAgB,IAAI;;;;;;kCACjD,qKAAC,sNAAA,CAAA,kBAAe;wBAAC,WAAW,CAAC,0CAA0C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAGtG,wBACC;;kCACE,qKAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,qKAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,CAAC,4FAA4F,EACtG,SAAS,IAAI,KAAK,OAAO,MAAM,GAC3B,6BACA,iBACJ;;sDAEF,qKAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,qKAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,SAAS,IAAI,KAAK,OAAO,MAAM,kBAC9B,qKAAC;4CAAK,WAAU;sDAAwB;;;;;;;mCAXrC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport LanguageSwitcher from './LanguageSwitcher';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const navigation = [\n    { name: t('navigation.home'), href: '/' },\n    { name: t('navigation.services'), href: '/services' },\n    { name: t('navigation.features'), href: '/features' },\n    { name: t('navigation.contact'), href: '/contact' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return router.pathname === '/';\n    }\n    return router.pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <span className=\"text-xl font-bold text-gray-900\">{t('brand.name')}</span>\n                <p className=\"text-xs text-gray-500 max-w-xs truncate\">{t('brand.tagline')}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-blue-600 border-b-2 border-blue-600'\n                    : 'text-gray-700 hover:text-blue-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side - Language switcher and CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LanguageSwitcher />\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('buttons.contact_us')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-2\">\n            <LanguageSwitcher />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-2 border-t border-gray-200\">\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  {t('buttons.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAoB,MAAM;QAAI;QACxC;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;KACnD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,OAAO,QAAQ,KAAK;QAC7B;QACA,OAAO,OAAO,QAAQ,CAAC,UAAU,CAAC;IACpC;IAEA,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAK,WAAU;0DAAmC,EAAE;;;;;;0DACrD,qKAAC;gDAAE,WAAU;0DAA2C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,qKAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6DAA6D,EACvE,SAAS,KAAK,IAAI,IACd,6CACA,qCACJ;8CAED,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BACC,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,CAAC,qEAAqE,EAC/E,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;8CAED,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const { t } = useTranslation('common');\n\n  const services = [\n    { name: '外贸网络线路', href: '/services/foreign-trade' },\n    { name: '跨境电商线路', href: '/services/ecommerce' },\n    { name: 'VPN服务', href: '/services/vpn' },\n    { name: '定制解决方案', href: '/services/custom' },\n  ];\n\n  const support = [\n    { name: '技术支持', href: '/support' },\n    { name: '服务条款', href: '/terms' },\n    { name: '隐私政策', href: '/privacy' },\n    { name: '常见问题', href: '/faq' },\n  ];\n\n  const company = [\n    { name: '关于我们', href: '/about' },\n    { name: '新闻动态', href: '/news' },\n    { name: '合作伙伴', href: '/partners' },\n    { name: '招聘信息', href: '/careers' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t('brand.name')}</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 text-sm leading-relaxed\">\n              {t('brand.tagline')}\n            </p>\n            \n            {/* Key features */}\n            <div className=\"space-y-2 mb-6\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ShieldCheckIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>AES/RSA/TLS加密</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <GlobeAltIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>全球网络覆盖</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ServerIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>7x24技术支持</span>\n              </div>\n            </div>\n\n            {/* Contact info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+86 400-xxx-xxxx</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>中国 · 深圳</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.services')}</h3>\n            <ul className=\"space-y-2\">\n              {services.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.support')}</h3>\n            <ul className=\"space-y-2\">\n              {support.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.company')}</h3>\n            <ul className=\"space-y-2\">\n              {company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('footer.copyright')}\n            </p>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                服务条款\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                隐私政策\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                网站地图\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAae,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM;QAA0B;QAClD;YAAE,MAAM;YAAU,MAAM;QAAsB;QAC9C;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAU,MAAM;QAAmB;KAC5C;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAO;KAC9B;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAY;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BAEb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,qKAAC;4CAAK,WAAU;sDAAqB,EAAE;;;;;;;;;;;;8CAEzC,qKAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,sNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,0MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,qBACb,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,qKAAC;gCAAI,WAAU;;kDACb,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwE;;;;;;kDAGtG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;kDAGxG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStH", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function Layout({ children, className = '' }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAe;IACtE,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC,+HAAA,CAAA,UAAM;;;;;0BACP,qKAAC;gBAAK,WAAW,CAAC,OAAO,EAAE,WAAW;0BACnC;;;;;;0BAEH,qKAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useTranslation } from 'next-i18next';\nimport Link from 'next/link';\nimport { ArrowRightIcon, PlayIcon } from '@heroicons/react/24/outline';\nimport { useState, useEffect } from 'react';\n\nexport default function HeroSection() {\n  const { t } = useTranslation(['home', 'common']);\n  const [isVideoPlaying, setIsVideoPlaying] = useState(false);\n  const [isMounted, setIsMounted] = useState(false);\n\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: [0.25, 0.25, 0.25, 0.75],\n      },\n    },\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Video/Image */}\n      <div className=\"absolute inset-0 z-0\">\n        {isVideoPlaying ? (\n          <video\n            autoPlay\n            muted\n            loop\n            className=\"w-full h-full object-cover\"\n            poster=\"/images/network-bg.svg\"\n          >\n            <source src=\"/videos/hero-video.mp4\" type=\"video/mp4\" />\n          </video>\n        ) : (\n          <div \n            className=\"w-full h-full bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900\"\n            style={{\n              backgroundImage: `linear-gradient(rgba(30, 58, 138, 0.8), rgba(67, 56, 202, 0.8)), url('/images/network-bg.svg')`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n            }}\n          />\n        )}\n        \n        {/* Animated overlay pattern */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/50 to-transparent\">\n          <div className=\"absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10\"></div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          variants={containerVariants}\n          initial={isMounted ? \"hidden\" : \"visible\"}\n          animate=\"visible\"\n          className=\"max-w-4xl mx-auto\"\n        >\n          {/* Badge */}\n          <motion.div variants={itemVariants} className=\"mb-8\">\n            <span className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100/20 text-blue-100 border border-blue-300/30 backdrop-blur-sm\">\n              <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n              {t('home:hero.badge')}\n            </span>\n          </motion.div>\n\n          {/* Main Title */}\n          <motion.h1 \n            variants={itemVariants}\n            className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\"\n          >\n            <span className=\"block\">{t('home:hero.title_line1')}</span>\n            <span className=\"block bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent\">\n              {t('home:hero.title_line2')}\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p \n            variants={itemVariants}\n            className=\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed\"\n          >\n            {t('home:hero.subtitle')}\n          </motion.p>\n\n          {/* Description */}\n          <motion.p \n            variants={itemVariants}\n            className=\"text-lg text-blue-200/80 mb-12 max-w-2xl mx-auto\"\n          >\n            {t('home:hero.description')}\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n          >\n            <Link\n              href=\"/contact\"\n              className=\"group inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n            >\n              {t('common:cta.get_started')}\n              <ArrowRightIcon className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n            \n            <button\n              onClick={() => setIsVideoPlaying(!isVideoPlaying)}\n              className=\"group inline-flex items-center px-8 py-4 text-lg font-semibold text-white border-2 border-white/30 rounded-full hover:border-white/60 hover:bg-white/10 transition-all duration-300 backdrop-blur-sm\"\n            >\n              <PlayIcon className=\"mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300\" />\n              {t('common:cta.watch_demo')}\n            </button>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-wrap justify-center items-center gap-8 text-blue-200/60\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n              <span className=\"text-sm font-medium\">{t('home:hero.trust.secure')}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-blue-400 rounded-full\"></div>\n              <span className=\"text-sm font-medium\">{t('home:hero.trust.reliable')}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-purple-400 rounded-full\"></div>\n              <span className=\"text-sm font-medium\">{t('home:hero.trust.professional')}</span>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 1.5, duration: 0.8 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"flex flex-col items-center text-white/60\">\n          <span className=\"text-sm mb-2\">{t('common:scroll_down')}</span>\n          <motion.div\n            animate={{ y: [0, 8, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\"\n          >\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-white/60 rounded-full mt-2\"\n            />\n          </motion.div>\n        </div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;;;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAQ;KAAS;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,qKAAC;QAAQ,WAAU;;0BAEjB,qKAAC;gBAAI,WAAU;;oBACZ,+BACC,qKAAC;wBACC,QAAQ;wBACR,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,QAAO;kCAEP,cAAA,qKAAC;4BAAO,KAAI;4BAAyB,MAAK;;;;;;;;;;6CAG5C,qKAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC,8FAA8F,CAAC;4BACjH,gBAAgB;4BAChB,oBAAoB;wBACtB;;;;;;kCAKJ,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAS,YAAY,WAAW;oBAChC,SAAQ;oBACR,WAAU;;sCAGV,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,qKAAC;gCAAK,WAAU;;kDACd,qKAAC;wCAAK,WAAU;;;;;;oCACf,EAAE;;;;;;;;;;;;sCAKP,qKAAC,gIAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;8CAEV,qKAAC;oCAAK,WAAU;8CAAS,EAAE;;;;;;8CAC3B,qKAAC;oCAAK,WAAU;8CACb,EAAE;;;;;;;;;;;;sCAKP,qKAAC,gIAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE;;;;;;sCAIL,qKAAC,gIAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE;;;;;;sCAIL,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCAET,EAAE;sDACH,qKAAC,oNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;8CAG5B,qKAAC;oCACC,SAAS,IAAM,kBAAkB,CAAC;oCAClC,WAAU;;sDAEV,qKAAC,wMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,EAAE;;;;;;;;;;;;;sCAKP,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;;;;;sDACf,qKAAC;4CAAK,WAAU;sDAAuB,EAAE;;;;;;;;;;;;8CAE3C,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;;;;;sDACf,qKAAC;4CAAK,WAAU;sDAAuB,EAAE;;;;;;;;;;;;8CAE3C,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;;;;;sDACf,qKAAC;4CAAK,WAAU;sDAAuB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjD,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;0BAEV,cAAA,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAK,WAAU;sCAAgB,EAAE;;;;;;sCAClC,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAG;iCAAE;4BAAC;4BACxB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;sCAEV,cAAA,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 1425, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/ui/AnimatedSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { ReactNode } from 'react';\n\ninterface AnimatedSectionProps {\n  children: ReactNode;\n  className?: string;\n  delay?: number;\n  direction?: 'up' | 'down' | 'left' | 'right' | 'fade';\n  duration?: number;\n}\n\nexport default function AnimatedSection({\n  children,\n  className = '',\n  delay = 0,\n  direction = 'up',\n  duration = 0.6\n}: AnimatedSectionProps) {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const variants = {\n    hidden: {\n      opacity: 0,\n      y: direction === 'up' ? 50 : direction === 'down' ? -50 : 0,\n      x: direction === 'left' ? 50 : direction === 'right' ? -50 : 0,\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      x: 0,\n      transition: {\n        duration,\n        delay,\n        ease: \"easeOut\" as const,\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      ref={ref}\n      initial=\"hidden\"\n      animate={inView ? 'visible' : 'hidden'}\n      variants={variants}\n      className={className}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;;;AAHA;;;;AAce,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,YAAY,IAAI,EAChB,WAAW,GAAG,EACO;IACrB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,WAAW;QACf,QAAQ;YACN,SAAS;YACT,GAAG,cAAc,OAAO,KAAK,cAAc,SAAS,CAAC,KAAK;YAC1D,GAAG,cAAc,SAAS,KAAK,cAAc,UAAU,CAAC,KAAK;QAC/D;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,GAAG;YACH,YAAY;gBACV;gBACA;gBACA,MAAM;YACR;QACF;IACF;IAEA,qBACE,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAQ;QACR,SAAS,SAAS,YAAY;QAC9B,UAAU;QACV,WAAW;kBAEV;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useTranslation } from 'next-i18next';\nimport Link from 'next/link';\nimport { useState, useEffect } from 'react';\nimport {\n  GlobeAltIcon,\n  ShoppingCartIcon,\n  ShieldCheckIcon,\n  CogIcon,\n  ArrowRightIcon,\n  CheckIcon\n} from '@heroicons/react/24/outline';\nimport AnimatedSection from '../ui/AnimatedSection';\n\nconst services = [\n  {\n    id: 'foreign_trade_lines',\n    icon: GlobeAltIcon,\n    color: 'from-blue-500 to-cyan-500',\n    bgColor: 'bg-blue-50',\n    iconColor: 'text-blue-600',\n    features: [\n      'dedicated_bandwidth',\n      'global_coverage',\n      'low_latency',\n      'enterprise_support'\n    ]\n  },\n  {\n    id: 'ecommerce_lines',\n    icon: ShoppingCartIcon,\n    color: 'from-green-500 to-emerald-500',\n    bgColor: 'bg-green-50',\n    iconColor: 'text-green-600',\n    features: [\n      'multi_platform',\n      'high_availability',\n      'traffic_optimization',\n      'real_time_monitoring'\n    ]\n  },\n  {\n    id: 'vpn_services',\n    icon: ShieldCheckIcon,\n    color: 'from-purple-500 to-indigo-500',\n    bgColor: 'bg-purple-50',\n    iconColor: 'text-purple-600',\n    features: [\n      'military_encryption',\n      'zero_logs',\n      'global_servers',\n      'unlimited_bandwidth'\n    ]\n  },\n  {\n    id: 'custom_solution',\n    icon: CogIcon,\n    color: 'from-orange-500 to-red-500',\n    bgColor: 'bg-orange-50',\n    iconColor: 'text-orange-600',\n    features: [\n      'tailored_design',\n      'expert_consultation',\n      'scalable_architecture',\n      'ongoing_support'\n    ]\n  }\n];\n\nexport default function ServicesSection() {\n  const { t } = useTranslation(['home', 'common']);\n  const [isMounted, setIsMounted] = useState(false);\n\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\" as const,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 bg-gradient-to-b from-gray-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4\">\n            {t('home:services.badge')}\n          </span>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            {t('home:services.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {t('home:services.subtitle')}\n          </p>\n        </AnimatedSection>\n\n        {/* Services Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\"\n        >\n          {services.map((service, index) => {\n            const Icon = service.icon;\n            return (\n              <motion.div\n                key={service.id}\n                variants={cardVariants}\n                className=\"group relative\"\n              >\n                <div className=\"relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden\">\n                  {/* Background Gradient */}\n                  <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />\n                  \n                  {/* Icon */}\n                  <div className={`inline-flex items-center justify-center w-16 h-16 ${service.bgColor} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                    <Icon className={`h-8 w-8 ${service.iconColor}`} />\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                    {t(`home:services.items.${service.id}.title`)}\n                  </h3>\n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {t(`home:services.items.${service.id}.description`)}\n                  </p>\n\n                  {/* Features */}\n                  <ul className=\"space-y-2 mb-6\">\n                    {service.features.map((feature) => (\n                      <li key={feature} className=\"flex items-center text-sm text-gray-600\">\n                        <CheckIcon className=\"h-4 w-4 text-green-500 mr-2 flex-shrink-0\" />\n                        {t(`home:services.features.${feature}`)}\n                      </li>\n                    ))}\n                  </ul>\n\n                  {/* CTA */}\n                  <Link\n                    href={`/services/${service.id}`}\n                    className={`inline-flex items-center text-sm font-semibold bg-gradient-to-r ${service.color} bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300`}\n                  >\n                    {t('common:buttons.learn_more')}\n                    <ArrowRightIcon className=\"ml-1 h-4 w-4\" />\n                  </Link>\n\n                  {/* Hover Effect */}\n                  <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${service.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left`} />\n                </div>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        {/* Bottom CTA */}\n        <AnimatedSection delay={0.3} className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 mb-8\">\n            {t('home:services.cta_text')}\n          </p>\n          <Link\n            href=\"/contact\"\n            className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n          >\n            {t('common:cta.contact_us')}\n            <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n          </Link>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;;;;AAdA;;;;;;;;AAgBA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM,gNAAA,CAAA,eAAY;QAClB,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM,wNAAA,CAAA,mBAAgB;QACtB,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM,sNAAA,CAAA,kBAAe;QACrB,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM,sMAAA,CAAA,UAAO;QACb,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAQ;KAAS;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BAEb,qKAAC,oIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,qKAAC;4BAAK,WAAU;sCACb,EAAE;;;;;;sCAEL,qKAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,qKAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;oBACzC,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,qKAAC,gIAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;sCAEV,cAAA,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,QAAQ,KAAK,CAAC,gEAAgE,CAAC;;;;;;kDAGrI,qKAAC;wCAAI,WAAW,CAAC,kDAAkD,EAAE,QAAQ,OAAO,CAAC,wEAAwE,CAAC;kDAC5J,cAAA,qKAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,EAAE;;;;;;;;;;;kDAIjD,qKAAC;wCAAG,WAAU;kDACX,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC;;;;;;kDAE9C,qKAAC;wCAAE,WAAU;kDACV,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,YAAY,CAAC;;;;;;kDAIpD,qKAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,qKAAC;gDAAiB,WAAU;;kEAC1B,qKAAC,0MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpB,EAAE,CAAC,uBAAuB,EAAE,SAAS;;+CAF/B;;;;;;;;;;kDAQb,qKAAC,qHAAA,CAAA,UAAI;wCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wCAC/B,WAAW,CAAC,gEAAgE,EAAE,QAAQ,KAAK,CAAC,0FAA0F,CAAC;;4CAEtL,EAAE;0DACH,qKAAC,oNAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;kDAI5B,qKAAC;wCAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,KAAK,CAAC,0FAA0F,CAAC;;;;;;;;;;;;2BAzC/K,QAAQ,EAAE;;;;;oBA6CrB;;;;;;8BAIF,qKAAC,oIAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;;sCACrC,qKAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,qKAAC,qHAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCAET,EAAE;8CACH,qKAAC,oNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1811, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/ui/CountUpNumber.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useInView } from 'react-intersection-observer';\n\ninterface CountUpNumberProps {\n  end: number;\n  duration?: number;\n  suffix?: string;\n  prefix?: string;\n  className?: string;\n}\n\nexport default function CountUpNumber({\n  end,\n  duration = 2000,\n  suffix = '',\n  prefix = '',\n  className = ''\n}: CountUpNumberProps) {\n  const [count, setCount] = useState(0);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  useEffect(() => {\n    if (!inView) return;\n\n    let startTime: number;\n    let animationFrame: number;\n\n    const animate = (currentTime: number) => {\n      if (!startTime) startTime = currentTime;\n      const progress = Math.min((currentTime - startTime) / duration, 1);\n      \n      // Easing function for smooth animation\n      const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n      setCount(Math.floor(easeOutQuart * end));\n\n      if (progress < 1) {\n        animationFrame = requestAnimationFrame(animate);\n      }\n    };\n\n    animationFrame = requestAnimationFrame(animate);\n\n    return () => {\n      if (animationFrame) {\n        cancelAnimationFrame(animationFrame);\n      }\n    };\n  }, [inView, end, duration]);\n\n  return (\n    <span ref={ref} className={className}>\n      {prefix}{count.toLocaleString()}{suffix}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;;AAHA;;;;AAae,SAAS,cAAc,EACpC,GAAG,EACH,WAAW,IAAI,EACf,SAAS,EAAE,EACX,SAAS,EAAE,EACX,YAAY,EAAE,EACK;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,IAAI;QACJ,IAAI;QAEJ,MAAM,UAAU,CAAC;YACf,IAAI,CAAC,WAAW,YAAY;YAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;YAEhE,uCAAuC;YACvC,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;YAChD,SAAS,KAAK,KAAK,CAAC,eAAe;YAEnC,IAAI,WAAW,GAAG;gBAChB,iBAAiB,sBAAsB;YACzC;QACF;QAEA,iBAAiB,sBAAsB;QAEvC,OAAO;YACL,IAAI,gBAAgB;gBAClB,qBAAqB;YACvB;QACF;IACF,GAAG;QAAC;QAAQ;QAAK;KAAS;IAE1B,qBACE,qKAAC;QAAK,KAAK;QAAK,WAAW;;YACxB;YAAQ,MAAM,cAAc;YAAI;;;;;;;AAGvC", "debugId": null}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useTranslation } from 'next-i18next';\nimport CountUpNumber from '../ui/CountUpNumber';\nimport AnimatedSection from '../ui/AnimatedSection';\nimport { \n  UsersIcon, \n  GlobeAltIcon, \n  ClockIcon, \n  ShieldCheckIcon,\n  TrophyIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nconst stats = [\n  {\n    id: 'clients',\n    icon: UsersIcon,\n    value: 5000,\n    suffix: '+',\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-100'\n  },\n  {\n    id: 'countries',\n    icon: GlobeAltIcon,\n    value: 120,\n    suffix: '+',\n    color: 'text-green-600',\n    bgColor: 'bg-green-100'\n  },\n  {\n    id: 'uptime',\n    icon: ClockIcon,\n    value: 99.9,\n    suffix: '%',\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-100'\n  },\n  {\n    id: 'experience',\n    icon: TrophyIcon,\n    value: 15,\n    suffix: '+',\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-100'\n  },\n  {\n    id: 'servers',\n    icon: ServerIcon,\n    value: 200,\n    suffix: '+',\n    color: 'text-indigo-600',\n    bgColor: 'bg-indigo-100'\n  },\n  {\n    id: 'security',\n    icon: ShieldCheckIcon,\n    value: 100,\n    suffix: '%',\n    color: 'text-red-600',\n    bgColor: 'bg-red-100'\n  }\n];\n\nexport default function StatsSection() {\n  const { t } = useTranslation(['home']);\n\n  return (\n    <section className=\"py-24 bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/50 to-transparent\"></div>\n      \n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <span className=\"inline-block px-4 py-2 bg-blue-100/20 text-blue-100 rounded-full text-sm font-semibold mb-4 backdrop-blur-sm border border-blue-300/30\">\n            {t('home:stats.badge')}\n          </span>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            {t('home:stats.title')}\n          </h2>\n          <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n            {t('home:stats.subtitle')}\n          </p>\n        </AnimatedSection>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8\">\n          {stats.map((stat, index) => {\n            const Icon = stat.icon;\n            return (\n              <AnimatedSection\n                key={stat.id}\n                delay={index * 0.1}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-2xl\">\n                  {/* Icon */}\n                  <div className={`inline-flex items-center justify-center w-16 h-16 ${stat.bgColor} rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                    <Icon className={`h-8 w-8 ${stat.color}`} />\n                  </div>\n\n                  {/* Number */}\n                  <div className=\"text-3xl md:text-4xl font-bold text-white mb-2\">\n                    <CountUpNumber\n                      end={stat.value}\n                      suffix={stat.suffix}\n                      duration={2500}\n                    />\n                  </div>\n\n                  {/* Label */}\n                  <p className=\"text-blue-100 font-medium\">\n                    {t(`home:stats.items.${stat.id}.label`)}\n                  </p>\n\n                  {/* Description */}\n                  <p className=\"text-blue-200/70 text-sm mt-2\">\n                    {t(`home:stats.items.${stat.id}.description`)}\n                  </p>\n                </div>\n              </AnimatedSection>\n            );\n          })}\n        </div>\n\n        {/* Bottom Text */}\n        <AnimatedSection delay={0.6} className=\"text-center mt-16\">\n          <p className=\"text-lg text-blue-100 max-w-4xl mx-auto leading-relaxed\">\n            {t('home:stats.bottom_text')}\n          </p>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AALA;;;;;;AAcA,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM,0MAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,gNAAA,CAAA,eAAY;QAClB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,0MAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,4MAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,4MAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,sNAAA,CAAA,kBAAe;QACrB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;KAAO;IAErC,qBACE,qKAAC;QAAQ,WAAU;;0BAEjB,qKAAC;gBAAI,WAAU;;;;;;0BACf,qKAAC;gBAAI,WAAU;;;;;;0BAEf,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC,oIAAA,CAAA,UAAe;wBAAC,WAAU;;0CACzB,qKAAC;gCAAK,WAAU;0CACb,EAAE;;;;;;0CAEL,qKAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,qKAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAKP,qKAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;4BAChB,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,qKAAC,oIAAA,CAAA,UAAe;gCAEd,OAAO,QAAQ;gCACf,WAAU;0CAEV,cAAA,qKAAC;oCAAI,WAAU;;sDAEb,qKAAC;4CAAI,WAAW,CAAC,kDAAkD,EAAE,KAAK,OAAO,CAAC,wEAAwE,CAAC;sDACzJ,cAAA,qKAAC;gDAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;sDAI1C,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC,kIAAA,CAAA,UAAa;gDACZ,KAAK,KAAK,KAAK;gDACf,QAAQ,KAAK,MAAM;gDACnB,UAAU;;;;;;;;;;;sDAKd,qKAAC;4CAAE,WAAU;sDACV,EAAE,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC;;;;;;sDAIxC,qKAAC;4CAAE,WAAU;sDACV,EAAE,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC;;;;;;;;;;;;+BA1B3C,KAAK,EAAE;;;;;wBA+BlB;;;;;;kCAIF,qKAAC,oIAAA,CAAA,UAAe;wBAAC,OAAO;wBAAK,WAAU;kCACrC,cAAA,qKAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 2117, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/index.tsx"], "sourcesContent": ["import { GetStaticProps } from 'next';\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\nimport { useTranslation } from 'next-i18next';\nimport Head from 'next/head';\nimport Layout from '../components/layout/Layout';\nimport HeroSection from '../components/home/<USER>';\nimport ServicesSection from '../components/home/<USER>';\nimport StatsSection from '../components/home/<USER>';\n\nexport default function Home() {\n  const { t } = useTranslation(['common', 'home']);\n\n  return (\n    <>\n      <Head>\n        <title>{`VPL - ${t('brand.tagline')}`}</title>\n        <meta name=\"description\" content={t('home:hero.subtitle')} />\n        <meta name=\"keywords\" content=\"外贸网络线路,跨境电商,VPN服务,网络加密,AES加密,RSA加密,TLS加密\" />\n      </Head>\n\n      <Layout>\n        <HeroSection />\n        <ServicesSection />\n        <StatsSection />\n      </Layout>\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async ({ locale }) => {\n  return {\n    props: {\n      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'home'])),\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAO;IAE/C,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAO,CAAC,MAAM,EAAE,EAAE,kBAAkB;;;;;;kCACrC,qKAAC;wBAAK,MAAK;wBAAc,SAAS,EAAE;;;;;;kCACpC,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAGhC,qKAAC,+HAAA,CAAA,UAAM;;kCACL,qKAAC,kIAAA,CAAA,UAAW;;;;;kCACZ,qKAAC,sIAAA,CAAA,UAAe;;;;;kCAChB,qKAAC,mIAAA,CAAA,UAAY;;;;;;;;;;;;;AAIrB;AAEO,MAAM,iBAAiC,OAAO,EAAE,MAAM,EAAE;IAC7D,OAAO;QACL,OAAO;YACL,GAAI,MAAM,CAAA,GAAA,uLAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,MAAM;gBAAC;gBAAU;aAAO,CAAC;QACtE;IACF;AACF", "debugId": null}}]}