{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' },\n  { code: 'ru', name: 'Русский', flag: '🇷🇺' },\n];\n\nexport default function LanguageSwitcher() {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];\n\n  const handleLanguageChange = (langCode: string) => {\n    const { pathname, asPath, query } = router;\n    router.push({ pathname, query }, asPath, { locale: langCode });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n        aria-label={t('navigation.language')}\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.name}</span>\n        <span className=\"sm:hidden\">{currentLanguage.flag}</span>\n        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 ${\n                    language.code === router.locale\n                      ? 'bg-blue-50 text-blue-600'\n                      : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {language.code === router.locale && (\n                    <span className=\"ml-auto text-blue-600\">✓</span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC,EAAE;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QACpC,OAAO,IAAI,CAAC;YAAE;YAAU;QAAM,GAAG,QAAQ;YAAE,QAAQ;QAAS;QAC5D,UAAU;IACZ;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAY,EAAE;;kCAEd,qKAAC,gNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,qKAAC;wBAAK,WAAU;kCAAoB,gBAAgB,IAAI;;;;;;kCACxD,qKAAC;wBAAK,WAAU;kCAAa,gBAAgB,IAAI;;;;;;kCACjD,qKAAC,sNAAA,CAAA,kBAAe;wBAAC,WAAW,CAAC,0CAA0C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAGtG,wBACC;;kCACE,qKAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,qKAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,CAAC,4FAA4F,EACtG,SAAS,IAAI,KAAK,OAAO,MAAM,GAC3B,6BACA,iBACJ;;sDAEF,qKAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,qKAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,SAAS,IAAI,KAAK,OAAO,MAAM,kBAC9B,qKAAC;4CAAK,WAAU;sDAAwB;;;;;;;mCAXrC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport LanguageSwitcher from './LanguageSwitcher';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const navigation = [\n    { name: t('navigation.home'), href: '/' },\n    { name: t('navigation.services'), href: '/services' },\n    { name: t('navigation.features'), href: '/features' },\n    { name: t('navigation.contact'), href: '/contact' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return router.pathname === '/';\n    }\n    return router.pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <span className=\"text-xl font-bold text-gray-900\">{t('brand.name')}</span>\n                <p className=\"text-xs text-gray-500 max-w-xs truncate\">{t('brand.tagline')}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-blue-600 border-b-2 border-blue-600'\n                    : 'text-gray-700 hover:text-blue-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side - Language switcher and CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LanguageSwitcher />\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('buttons.contact_us')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-2\">\n            <LanguageSwitcher />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-2 border-t border-gray-200\">\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  {t('buttons.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAoB,MAAM;QAAI;QACxC;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;KACnD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,OAAO,QAAQ,KAAK;QAC7B;QACA,OAAO,OAAO,QAAQ,CAAC,UAAU,CAAC;IACpC;IAEA,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAK,WAAU;0DAAmC,EAAE;;;;;;0DACrD,qKAAC;gDAAE,WAAU;0DAA2C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,qKAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6DAA6D,EACvE,SAAS,KAAK,IAAI,IACd,6CACA,qCACJ;8CAED,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BACC,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,CAAC,qEAAqE,EAC/E,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;8CAED,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const { t } = useTranslation('common');\n\n  const services = [\n    { name: '外贸网络线路', href: '/services/foreign-trade' },\n    { name: '跨境电商线路', href: '/services/ecommerce' },\n    { name: 'VPN服务', href: '/services/vpn' },\n    { name: '定制解决方案', href: '/services/custom' },\n  ];\n\n  const support = [\n    { name: '技术支持', href: '/support' },\n    { name: '服务条款', href: '/terms' },\n    { name: '隐私政策', href: '/privacy' },\n    { name: '常见问题', href: '/faq' },\n  ];\n\n  const company = [\n    { name: '关于我们', href: '/about' },\n    { name: '新闻动态', href: '/news' },\n    { name: '合作伙伴', href: '/partners' },\n    { name: '招聘信息', href: '/careers' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t('brand.name')}</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 text-sm leading-relaxed\">\n              {t('brand.tagline')}\n            </p>\n            \n            {/* Key features */}\n            <div className=\"space-y-2 mb-6\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ShieldCheckIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>AES/RSA/TLS加密</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <GlobeAltIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>全球网络覆盖</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ServerIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>7x24技术支持</span>\n              </div>\n            </div>\n\n            {/* Contact info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+86 400-xxx-xxxx</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>中国 · 深圳</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.services')}</h3>\n            <ul className=\"space-y-2\">\n              {services.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.support')}</h3>\n            <ul className=\"space-y-2\">\n              {support.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.company')}</h3>\n            <ul className=\"space-y-2\">\n              {company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('footer.copyright')}\n            </p>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                服务条款\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                隐私政策\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                网站地图\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAae,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM;QAA0B;QAClD;YAAE,MAAM;YAAU,MAAM;QAAsB;QAC9C;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAU,MAAM;QAAmB;KAC5C;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAO;KAC9B;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAY;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BAEb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,qKAAC;4CAAK,WAAU;sDAAqB,EAAE;;;;;;;;;;;;8CAEzC,qKAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,sNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,0MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,qBACb,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,qKAAC;gCAAI,WAAU;;kDACb,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwE;;;;;;kDAGtG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;kDAGxG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStH", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function Layout({ children, className = '' }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAe;IACtE,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC,+HAAA,CAAA,UAAM;;;;;0BACP,qKAAC;gBAAK,WAAW,CAAC,OAAO,EAAE,WAAW;0BACnC;;;;;;0BAEH,qKAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/forms/ContactForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  ChatBubbleLeftRightIcon,\n  UserIcon\n} from '@heroicons/react/24/outline';\n\n// Form validation schema\nconst contactSchema = z.object({\n  companyName: z.string().min(1, 'Company name is required'),\n  contactPerson: z.string().min(1, 'Contact person is required'),\n  phone: z.string().min(1, 'Phone number is required'),\n  email: z.string().email('Invalid email address'),\n  wechat: z.string().optional(),\n  qq: z.string().optional(),\n  serviceType: z.string().min(1, 'Service type is required'),\n  message: z.string().min(10, 'Message must be at least 10 characters'),\n  verificationCode: z.string().min(1, 'Verification code is required'),\n});\n\ntype ContactFormData = z.infer<typeof contactSchema>;\n\nexport default function ContactForm() {\n  const { t } = useTranslation(['contact', 'common']);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [verificationCode, setVerificationCode] = useState('');\n  const [generatedCode, setGeneratedCode] = useState('');\n  const [showSuccess, setShowSuccess] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ContactFormData>({\n    resolver: zodResolver(contactSchema),\n  });\n\n  // Generate alphanumeric verification code\n  const generateVerificationCode = () => {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = '';\n    for (let i = 0; i < 6; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    setGeneratedCode(result);\n    return result;\n  };\n\n  const onSubmit = async (data: ContactFormData) => {\n    if (data.verificationCode !== generatedCode) {\n      alert(t('contact:error.verification_failed'));\n      return;\n    }\n\n    setIsSubmitting(true);\n    \n    try {\n      const response = await fetch('/api/contact', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (response.ok) {\n        setShowSuccess(true);\n        reset();\n        setGeneratedCode('');\n      } else {\n        throw new Error('Failed to submit form');\n      }\n    } catch (error) {\n      alert(t('contact:error.message'));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const serviceTypes = [\n    { value: 'foreign_trade_lines', label: t('contact:services.foreign_trade_lines') },\n    { value: 'ecommerce_lines', label: t('contact:services.ecommerce_lines') },\n    { value: 'vpn_services', label: t('contact:services.vpn_services') },\n    { value: 'custom_solution', label: t('contact:services.custom_solution') },\n  ];\n\n  if (showSuccess) {\n    return (\n      <div className=\"bg-green-50 border border-green-200 rounded-lg p-8 text-center\">\n        <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 mb-4\">\n          <svg className=\"h-8 w-8 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 12.75l6 6 9-13.5\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-semibold text-green-900 mb-2\">\n          {t('contact:success.title')}\n        </h3>\n        <p className=\"text-green-700 mb-6\">\n          {t('contact:success.message')}\n        </p>\n        <button\n          onClick={() => setShowSuccess(false)}\n          className=\"bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors duration-200\"\n        >\n          {t('common:buttons.back')}\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n      {/* Company Information */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div>\n          <label htmlFor=\"companyName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.company_name')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            {...register('companyName')}\n            type=\"text\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            placeholder={t('contact:form.company_name_placeholder')}\n          />\n          {errors.companyName && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.companyName.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"contactPerson\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.contact_person')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            {...register('contactPerson')}\n            type=\"text\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            placeholder={t('contact:form.contact_person_placeholder')}\n          />\n          {errors.contactPerson && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.contactPerson.message}</p>\n          )}\n        </div>\n      </div>\n\n      {/* Contact Information */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.phone')} <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <PhoneIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('phone')}\n              type=\"tel\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.phone_placeholder')}\n            />\n          </div>\n          {errors.phone && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.email')} <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <EnvelopeIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('email')}\n              type=\"email\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.email_placeholder')}\n            />\n          </div>\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n          )}\n        </div>\n      </div>\n\n      {/* Additional Contact Methods */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div>\n          <label htmlFor=\"wechat\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.wechat')}\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <ChatBubbleLeftRightIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('wechat')}\n              type=\"text\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.wechat_placeholder')}\n            />\n          </div>\n        </div>\n\n        <div>\n          <label htmlFor=\"qq\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.qq')}\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <UserIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('qq')}\n              type=\"text\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.qq_placeholder')}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Service Type */}\n      <div>\n        <label htmlFor=\"serviceType\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('contact:form.service_type')} <span className=\"text-red-500\">*</span>\n        </label>\n        <select\n          {...register('serviceType')}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n        >\n          <option value=\"\">{t('contact:form.service_type_placeholder')}</option>\n          {serviceTypes.map((type) => (\n            <option key={type.value} value={type.value}>\n              {type.label}\n            </option>\n          ))}\n        </select>\n        {errors.serviceType && (\n          <p className=\"mt-1 text-sm text-red-600\">{errors.serviceType.message}</p>\n        )}\n      </div>\n\n      {/* Message */}\n      <div>\n        <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('contact:form.message')} <span className=\"text-red-500\">*</span>\n        </label>\n        <textarea\n          {...register('message')}\n          rows={4}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n          placeholder={t('contact:form.message_placeholder')}\n        />\n        {errors.message && (\n          <p className=\"mt-1 text-sm text-red-600\">{errors.message.message}</p>\n        )}\n      </div>\n\n      {/* Verification Code */}\n      <div>\n        <label htmlFor=\"verificationCode\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('contact:form.verification_code')} <span className=\"text-red-500\">*</span>\n        </label>\n        <div className=\"flex space-x-3\">\n          <input\n            {...register('verificationCode')}\n            type=\"text\"\n            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            placeholder={t('contact:form.verification_code_placeholder')}\n          />\n          <button\n            type=\"button\"\n            onClick={generateVerificationCode}\n            className=\"px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors duration-200\"\n          >\n            {t('contact:form.get_verification_code')}\n          </button>\n        </div>\n        {generatedCode && (\n          <div className=\"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md\">\n            <p className=\"text-sm text-blue-800\">\n              验证码: <span className=\"font-mono font-bold text-lg\">{generatedCode}</span>\n            </p>\n          </div>\n        )}\n        {errors.verificationCode && (\n          <p className=\"mt-1 text-sm text-red-600\">{errors.verificationCode.message}</p>\n        )}\n      </div>\n\n      {/* Submit Button */}\n      <div>\n        <button\n          type=\"submit\"\n          disabled={isSubmitting}\n          className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n        >\n          {isSubmitting ? t('contact:form.submitting') : t('contact:form.submit')}\n        </button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;AAPA;;;;;;;;AAcA,yBAAyB;AACzB,MAAM,gBAAgB,sGAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,aAAa,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,eAAe,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,OAAO,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,OAAO,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,QAAQ,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,IAAI,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,aAAa,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,SAAS,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC5B,kBAAkB,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACtC;AAIe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAW;KAAS;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,0CAA0C;IAC1C,MAAM,2BAA2B;QAC/B,MAAM,QAAQ;QACd,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;QAChE;QACA,iBAAiB;QACjB,OAAO;IACT;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,KAAK,gBAAgB,KAAK,eAAe;YAC3C,MAAM,EAAE;YACR;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf;gBACA,iBAAiB;YACnB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,EAAE;QACV,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB;YAAE,OAAO;YAAuB,OAAO,EAAE;QAAwC;QACjF;YAAE,OAAO;YAAmB,OAAO,EAAE;QAAoC;QACzE;YAAE,OAAO;YAAgB,OAAO,EAAE;QAAiC;QACnE;YAAE,OAAO;YAAmB,OAAO,EAAE;QAAoC;KAC1E;IAED,IAAI,aAAa;QACf,qBACE,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;wBAAyB,MAAK;wBAAO,SAAQ;wBAAY,aAAY;wBAAM,QAAO;kCAC/F,cAAA,qKAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,GAAE;;;;;;;;;;;;;;;;8BAGzD,qKAAC;oBAAG,WAAU;8BACX,EAAE;;;;;;8BAEL,qKAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAEL,qKAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;8BAET,EAAE;;;;;;;;;;;;IAIX;IAEA,qBACE,qKAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAEhD,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;;0CACC,qKAAC;gCAAM,SAAQ;gCAAc,WAAU;;oCACpC,EAAE;oCAA6B;kDAAC,qKAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAElE,qKAAC;gCACE,GAAG,SAAS,cAAc;gCAC3B,MAAK;gCACL,WAAU;gCACV,aAAa,EAAE;;;;;;4BAEhB,OAAO,WAAW,kBACjB,qKAAC;gCAAE,WAAU;0CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAIxE,qKAAC;;0CACC,qKAAC;gCAAM,SAAQ;gCAAgB,WAAU;;oCACtC,EAAE;oCAA+B;kDAAC,qKAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEpE,qKAAC;gCACE,GAAG,SAAS,gBAAgB;gCAC7B,MAAK;gCACL,WAAU;gCACV,aAAa,EAAE;;;;;;4BAEhB,OAAO,aAAa,kBACnB,qKAAC;gCAAE,WAAU;0CAA6B,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0BAM5E,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;;0CACC,qKAAC;gCAAM,SAAQ;gCAAQ,WAAU;;oCAC9B,EAAE;oCAAsB;kDAAC,qKAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE3D,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,0MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,qKAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;4BAGlB,OAAO,KAAK,kBACX,qKAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAIlE,qKAAC;;0CACC,qKAAC;gCAAM,SAAQ;gCAAQ,WAAU;;oCAC9B,EAAE;oCAAsB;kDAAC,qKAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE3D,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,qKAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;4BAGlB,OAAO,KAAK,kBACX,qKAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;0BAMpE,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;;0CACC,qKAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAC/B,EAAE;;;;;;0CAEL,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,sOAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;;;;;;kDAErC,qKAAC;wCACE,GAAG,SAAS,SAAS;wCACtB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;kCAKrB,qKAAC;;0CACC,qKAAC;gCAAM,SAAQ;gCAAK,WAAU;0CAC3B,EAAE;;;;;;0CAEL,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,wMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,qKAAC;wCACE,GAAG,SAAS,KAAK;wCAClB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,qKAAC;;kCACC,qKAAC;wBAAM,SAAQ;wBAAc,WAAU;;4BACpC,EAAE;4BAA6B;0CAAC,qKAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAElE,qKAAC;wBACE,GAAG,SAAS,cAAc;wBAC3B,WAAU;;0CAEV,qKAAC;gCAAO,OAAM;0CAAI,EAAE;;;;;;4BACnB,aAAa,GAAG,CAAC,CAAC,qBACjB,qKAAC;oCAAwB,OAAO,KAAK,KAAK;8CACvC,KAAK,KAAK;mCADA,KAAK,KAAK;;;;;;;;;;;oBAK1B,OAAO,WAAW,kBACjB,qKAAC;wBAAE,WAAU;kCAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0BAKxE,qKAAC;;kCACC,qKAAC;wBAAM,SAAQ;wBAAU,WAAU;;4BAChC,EAAE;4BAAwB;0CAAC,qKAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAE7D,qKAAC;wBACE,GAAG,SAAS,UAAU;wBACvB,MAAM;wBACN,WAAU;wBACV,aAAa,EAAE;;;;;;oBAEhB,OAAO,OAAO,kBACb,qKAAC;wBAAE,WAAU;kCAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0BAKpE,qKAAC;;kCACC,qKAAC;wBAAM,SAAQ;wBAAmB,WAAU;;4BACzC,EAAE;4BAAkC;0CAAC,qKAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAEvE,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCACE,GAAG,SAAS,mBAAmB;gCAChC,MAAK;gCACL,WAAU;gCACV,aAAa,EAAE;;;;;;0CAEjB,qKAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,EAAE;;;;;;;;;;;;oBAGN,+BACC,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAE,WAAU;;gCAAwB;8CAC9B,qKAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;;oBAIzD,OAAO,gBAAgB,kBACtB,qKAAC;wBAAE,WAAU;kCAA6B,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;0BAK7E,qKAAC;0BACC,cAAA,qKAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,eAAe,EAAE,6BAA6B,EAAE;;;;;;;;;;;;;;;;;AAK3D", "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/contact.tsx"], "sourcesContent": ["import { GetStaticProps } from 'next';\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\nimport { useTranslation } from 'next-i18next';\nimport Head from 'next/head';\nimport Layout from '../components/layout/Layout';\nimport ContactForm from '../components/forms/ContactForm';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  ChatBubbleLeftRightIcon,\n  UserIcon,\n  MapPinIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Contact() {\n  const { t } = useTranslation(['contact', 'common']);\n\n  const contactMethods = [\n    {\n      icon: PhoneIcon,\n      title: t('contact:contact_methods.phone'),\n      value: '+86 400-xxx-xxxx',\n      description: '工作日 9:00-18:00'\n    },\n    {\n      icon: EnvelopeIcon,\n      title: t('contact:contact_methods.email'),\n      value: '<EMAIL>',\n      description: '24小时内回复'\n    },\n    {\n      icon: ChatBubbleLeftRightIcon,\n      title: t('contact:contact_methods.wechat'),\n      value: 'VPL-Service',\n      description: '扫码添加客服微信'\n    },\n    {\n      icon: UserIcon,\n      title: t('contact:contact_methods.qq'),\n      value: '12345678',\n      description: 'QQ在线咨询'\n    }\n  ];\n\n  return (\n    <>\n      <Head>\n        <title>联系我们 - VPL专业网络解决方案</title>\n        <meta name=\"description\" content=\"联系VPL获取专业的B2B网络解决方案咨询，我们提供外贸网络线路、跨境电商外网线路、VPN服务等多种联系方式。\" />\n      </Head>\n      \n      <Layout>\n        {/* Hero Section */}\n        <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl\">\n                {t('contact:title')}\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\">\n                {t('contact:subtitle')}\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Methods */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                {t('contact:contact_methods.title')}\n              </h2>\n            </div>\n            \n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n              {contactMethods.map((method, index) => {\n                const IconComponent = method.icon;\n                return (\n                  <div key={index} className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                    <div className=\"mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 mb-4\">\n                      <IconComponent className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {method.title}\n                    </h3>\n                    <p className=\"text-blue-600 font-medium mb-1\">\n                      {method.value}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      {method.description}\n                    </p>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Form and Info */}\n        <section className=\"py-20 bg-gray-50\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"lg:grid lg:grid-cols-3 lg:gap-16\">\n              {/* Contact Form */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"bg-white rounded-lg shadow-sm p-8\">\n                  <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                    在线咨询表单\n                  </h2>\n                  <ContactForm />\n                </div>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"mt-12 lg:mt-0\">\n                <div className=\"bg-white rounded-lg shadow-sm p-8\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                    联系信息\n                  </h3>\n                  \n                  <div className=\"space-y-6\">\n                    <div className=\"flex items-start\">\n                      <MapPinIcon className=\"flex-shrink-0 h-6 w-6 text-blue-600 mt-1\" />\n                      <div className=\"ml-3\">\n                        <h4 className=\"text-base font-medium text-gray-900\">公司地址</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          中国广东省深圳市南山区<br />\n                          科技园南区软件产业基地\n                        </p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-start\">\n                      <ClockIcon className=\"flex-shrink-0 h-6 w-6 text-blue-600 mt-1\" />\n                      <div className=\"ml-3\">\n                        <h4 className=\"text-base font-medium text-gray-900\">服务时间</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          周一至周五：9:00 - 18:00<br />\n                          周末及节假日：10:00 - 16:00<br />\n                          紧急技术支持：7x24小时\n                        </p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-start\">\n                      <PhoneIcon className=\"flex-shrink-0 h-6 w-6 text-blue-600 mt-1\" />\n                      <div className=\"ml-3\">\n                        <h4 className=\"text-base font-medium text-gray-900\">联系电话</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          销售咨询：+86 400-xxx-xxxx<br />\n                          技术支持：+86 400-xxx-xxxx<br />\n                          投诉建议：+86 400-xxx-xxxx\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-8 p-4 bg-blue-50 rounded-lg\">\n                    <h4 className=\"text-base font-medium text-blue-900 mb-2\">\n                      为什么选择我们？\n                    </h4>\n                    <ul className=\"text-sm text-blue-800 space-y-1\">\n                      <li>• 专业的技术团队支持</li>\n                      <li>• 7x24小时服务保障</li>\n                      <li>• 定制化解决方案</li>\n                      <li>• 银行级安全保护</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-20 bg-white\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                常见问题\n              </h2>\n              <p className=\"text-lg text-gray-600\">\n                以下是客户经常询问的问题，如有其他疑问请联系我们\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-2\">\n              <div className=\"space-y-6\">\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    你们的服务覆盖哪些地区？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    我们的服务覆盖全球主要贸易区域，包括北美、欧洲、东南亚等地区，可以为您提供稳定的国际网络连接。\n                  </p>\n                </div>\n                \n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    如何保证网络连接的稳定性？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    我们采用多线路冗余设计，配备专业的监控系统，确保99.9%的服务可用性，并提供7x24小时技术支持。\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"space-y-6\">\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    你们的加密技术安全吗？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    我们采用军用级AES-256加密、RSA非对称加密、TLS协议等多重安全保障，确保您的数据传输绝对安全。\n                  </p>\n                </div>\n                \n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    如何开始使用你们的服务？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    您可以通过填写上方的咨询表单或直接联系我们的销售团队，我们会根据您的需求提供最适合的解决方案。\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n      </Layout>\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async ({ locale }) => {\n  return {\n    props: {\n      ...(await serverSideTranslations(locale ?? 'zh', ['contact', 'common'])),\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAW;KAAS;IAElD,MAAM,iBAAiB;QACrB;YACE,MAAM,0MAAA,CAAA,YAAS;YACf,OAAO,EAAE;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gNAAA,CAAA,eAAY;YAClB,OAAO,EAAE;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,sOAAA,CAAA,0BAAuB;YAC7B,OAAO,EAAE;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,wMAAA,CAAA,WAAQ;YACd,OAAO,EAAE;YACT,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAGnC,qKAAC,+HAAA,CAAA,UAAM;;kCAEL,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,qKAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAOX,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;;;;;;8CAIP,qKAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ;wCAC3B,MAAM,gBAAgB,OAAO,IAAI;wCACjC,qBACE,qKAAC;4CAAgB,WAAU;;8DACzB,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAc,WAAU;;;;;;;;;;;8DAE3B,qKAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;8DAEf,qKAAC;oDAAE,WAAU;8DACV,OAAO,KAAK;;;;;;8DAEf,qKAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;2CAXb;;;;;oCAed;;;;;;;;;;;;;;;;;kCAMN,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAG,WAAU;8DAAwC;;;;;;8DAGtD,qKAAC,mIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;kDAKhB,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAIrD,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;;8EACb,qKAAC,4MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAG,WAAU;sFAAsC;;;;;;sFACpD,qKAAC;4EAAE,WAAU;;gFAAwB;8FACxB,qKAAC;;;;;gFAAK;;;;;;;;;;;;;;;;;;;sEAMvB,qKAAC;4DAAI,WAAU;;8EACb,qKAAC,0MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAG,WAAU;sFAAsC;;;;;;sFACpD,qKAAC;4EAAE,WAAU;;gFAAwB;8FACjB,qKAAC;;;;;gFAAK;8FACJ,qKAAC;;;;;gFAAK;;;;;;;;;;;;;;;;;;;sEAMhC,qKAAC;4DAAI,WAAU;;8EACb,qKAAC,0MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAG,WAAU;sFAAsC;;;;;;sFACpD,qKAAC;4EAAE,WAAU;;gFAAwB;8FACd,qKAAC;;;;;gFAAK;8FACN,qKAAC;;;;;gFAAK;;;;;;;;;;;;;;;;;;;;;;;;;8DAOnC,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,qKAAC;4DAAG,WAAU;;8EACZ,qKAAC;8EAAG;;;;;;8EACJ,qKAAC;8EAAG;;;;;;8EACJ,qKAAC;8EAAG;;;;;;8EACJ,qKAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUlB,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,qKAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,qKAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAK/B,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,qKAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,qKAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAK/B,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,qKAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/C;AAEO,MAAM,iBAAiC,OAAO,EAAE,MAAM,EAAE;IAC7D,OAAO;QACL,OAAO;YACL,GAAI,MAAM,CAAA,GAAA,uLAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,MAAM;gBAAC;gBAAW;aAAS,CAAC;QACzE;IACF;AACF", "debugId": null}}]}