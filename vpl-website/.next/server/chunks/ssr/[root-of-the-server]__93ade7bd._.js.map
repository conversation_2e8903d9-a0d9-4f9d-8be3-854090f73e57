{"version": 3, "sources": [], "sections": [{"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/_document.tsx"], "sourcesContent": ["import { Html, Head, Main, NextScript } from 'next/document';\n\nexport default function Document() {\n  return (\n    <Html>\n      <Head>\n        <meta charSet=\"utf-8\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n      </Head>\n      <body>\n        <Main />\n        <NextScript />\n      </body>\n    </Html>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,qKAAC,yHAAA,CAAA,OAAI;;0BACH,qKAAC,yHAAA,CAAA,OAAI;;kCACH,qKAAC;wBAAK,SAAQ;;;;;;kCACd,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,qKAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;;;;;;;0BAExB,qKAAC;;kCACC,qKAAC,yHAAA,CAAA,OAAI;;;;;kCACL,qKAAC,yHAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;AAInB", "debugId": null}}]}