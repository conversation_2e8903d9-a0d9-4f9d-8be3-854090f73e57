{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' },\n  { code: 'ru', name: 'Русский', flag: '🇷🇺' },\n];\n\nexport default function LanguageSwitcher() {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];\n\n  const handleLanguageChange = (langCode: string) => {\n    const { pathname, asPath, query } = router;\n    router.push({ pathname, query }, asPath, { locale: langCode });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n        aria-label={t('navigation.language')}\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.name}</span>\n        <span className=\"sm:hidden\">{currentLanguage.flag}</span>\n        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 ${\n                    language.code === router.locale\n                      ? 'bg-blue-50 text-blue-600'\n                      : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {language.code === router.locale && (\n                    <span className=\"ml-auto text-blue-600\">✓</span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC,EAAE;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QACpC,OAAO,IAAI,CAAC;YAAE;YAAU;QAAM,GAAG,QAAQ;YAAE,QAAQ;QAAS;QAC5D,UAAU;IACZ;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAY,EAAE;;kCAEd,qKAAC,gNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,qKAAC;wBAAK,WAAU;kCAAoB,gBAAgB,IAAI;;;;;;kCACxD,qKAAC;wBAAK,WAAU;kCAAa,gBAAgB,IAAI;;;;;;kCACjD,qKAAC,sNAAA,CAAA,kBAAe;wBAAC,WAAW,CAAC,0CAA0C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAGtG,wBACC;;kCACE,qKAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,qKAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,CAAC,4FAA4F,EACtG,SAAS,IAAI,KAAK,OAAO,MAAM,GAC3B,6BACA,iBACJ;;sDAEF,qKAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,qKAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,SAAS,IAAI,KAAK,OAAO,MAAM,kBAC9B,qKAAC;4CAAK,WAAU;sDAAwB;;;;;;;mCAXrC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport LanguageSwitcher from './LanguageSwitcher';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const navigation = [\n    { name: t('navigation.home'), href: '/' },\n    { name: t('navigation.services'), href: '/services' },\n    { name: t('navigation.features'), href: '/features' },\n    { name: t('navigation.contact'), href: '/contact' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return router.pathname === '/';\n    }\n    return router.pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <span className=\"text-xl font-bold text-gray-900\">{t('brand.name')}</span>\n                <p className=\"text-xs text-gray-500 max-w-xs truncate\">{t('brand.tagline')}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-blue-600 border-b-2 border-blue-600'\n                    : 'text-gray-700 hover:text-blue-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side - Language switcher and CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LanguageSwitcher />\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('buttons.contact_us')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-2\">\n            <LanguageSwitcher />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-2 border-t border-gray-200\">\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  {t('buttons.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAoB,MAAM;QAAI;QACxC;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;KACnD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,OAAO,QAAQ,KAAK;QAC7B;QACA,OAAO,OAAO,QAAQ,CAAC,UAAU,CAAC;IACpC;IAEA,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAK,WAAU;0DAAmC,EAAE;;;;;;0DACrD,qKAAC;gDAAE,WAAU;0DAA2C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,qKAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6DAA6D,EACvE,SAAS,KAAK,IAAI,IACd,6CACA,qCACJ;8CAED,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BACC,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,CAAC,qEAAqE,EAC/E,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;8CAED,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const { t } = useTranslation('common');\n\n  const services = [\n    { name: '外贸网络线路', href: '/services/foreign-trade' },\n    { name: '跨境电商线路', href: '/services/ecommerce' },\n    { name: 'VPN服务', href: '/services/vpn' },\n    { name: '定制解决方案', href: '/services/custom' },\n  ];\n\n  const support = [\n    { name: '技术支持', href: '/support' },\n    { name: '服务条款', href: '/terms' },\n    { name: '隐私政策', href: '/privacy' },\n    { name: '常见问题', href: '/faq' },\n  ];\n\n  const company = [\n    { name: '关于我们', href: '/about' },\n    { name: '新闻动态', href: '/news' },\n    { name: '合作伙伴', href: '/partners' },\n    { name: '招聘信息', href: '/careers' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t('brand.name')}</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 text-sm leading-relaxed\">\n              {t('brand.tagline')}\n            </p>\n            \n            {/* Key features */}\n            <div className=\"space-y-2 mb-6\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ShieldCheckIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>AES/RSA/TLS加密</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <GlobeAltIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>全球网络覆盖</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ServerIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>7x24技术支持</span>\n              </div>\n            </div>\n\n            {/* Contact info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+86 400-xxx-xxxx</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>中国 · 深圳</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.services')}</h3>\n            <ul className=\"space-y-2\">\n              {services.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.support')}</h3>\n            <ul className=\"space-y-2\">\n              {support.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.company')}</h3>\n            <ul className=\"space-y-2\">\n              {company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('footer.copyright')}\n            </p>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                服务条款\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                隐私政策\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                网站地图\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAae,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM;QAA0B;QAClD;YAAE,MAAM;YAAU,MAAM;QAAsB;QAC9C;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAU,MAAM;QAAmB;KAC5C;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAO;KAC9B;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAY;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BAEb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,qKAAC;4CAAK,WAAU;sDAAqB,EAAE;;;;;;;;;;;;8CAEzC,qKAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,sNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,0MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,qBACb,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,qKAAC;gCAAI,WAAU;;kDACb,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwE;;;;;;kDAGtG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;kDAGxG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStH", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function Layout({ children, className = '' }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAe;IACtE,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC,+HAAA,CAAA,UAAM;;;;;0BACP,qKAAC;gBAAK,WAAW,CAAC,OAAO,EAAE,WAAW;0BACnC;;;;;;0BAEH,qKAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/index.tsx"], "sourcesContent": ["import { GetStaticProps } from 'next';\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\nimport { useTranslation } from 'next-i18next';\nimport Head from 'next/head';\nimport Layout from '../components/layout/Layout';\nimport HeroSection from '../components/home/<USER>';\nimport ServicesSection from '../components/home/<USER>';\nimport StatsSection from '../components/home/<USER>';\n\nexport default function Home() {\n  const { t } = useTranslation(['common', 'home']);\n\n  return (\n    <>\n      <Head>\n        <title>{`VPL - ${t('brand.tagline')}`}</title>\n        <meta name=\"description\" content={t('home:hero.subtitle')} />\n        <meta name=\"keywords\" content=\"外贸网络线路,跨境电商,VPN服务,网络加密,AES加密,RSA加密,TLS加密\" />\n      </Head>\n\n      <Layout>\n        {/* Hero Section */}\n        <section className=\"relative bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 min-h-screen flex items-center justify-center\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/50 to-transparent\">\n            <div className=\"absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10\"></div>\n          </div>\n\n          <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <div className=\"max-w-4xl mx-auto\">\n              <span className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100/20 text-blue-100 border border-blue-300/30 backdrop-blur-sm mb-8\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n                {t('home:hero.badge')}\n              </span>\n\n              <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\">\n                <span className=\"block\">{t('home:hero.title_line1')}</span>\n                <span className=\"block bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent\">\n                  {t('home:hero.title_line2')}\n                </span>\n              </h1>\n\n              <p className=\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed\">\n                {t('home:hero.subtitle')}\n              </p>\n\n              <p className=\"text-lg text-blue-200/80 mb-12 max-w-2xl mx-auto\">\n                {t('home:hero.description')}\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\">\n                <Link\n                  href=\"/contact\"\n                  className=\"group inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n                >\n                  {t('cta.get_started')}\n                  <svg className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n                  </svg>\n                </Link>\n\n                <button className=\"group inline-flex items-center px-8 py-4 text-lg font-semibold text-white border-2 border-white/30 rounded-full hover:border-white/60 hover:bg-white/10 transition-all duration-300 backdrop-blur-sm\">\n                  <svg className=\"mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {t('cta.watch_demo')}\n                </button>\n              </div>\n\n              <div className=\"flex flex-wrap justify-center items-center gap-8 text-blue-200/60\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n                  <span className=\"text-sm font-medium\">{t('home:hero.trust.secure')}</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-3 h-3 bg-blue-400 rounded-full\"></div>\n                  <span className=\"text-sm font-medium\">{t('home:hero.trust.reliable')}</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-3 h-3 bg-purple-400 rounded-full\"></div>\n                  <span className=\"text-sm font-medium\">{t('home:hero.trust.professional')}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Services Section */}\n        <section className=\"py-24 bg-gradient-to-b from-gray-50 to-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-16\">\n              <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4\">\n                {t('home:services.badge')}\n              </span>\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n                {t('home:services.title')}\n              </h2>\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                {t('home:services.subtitle')}\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {[\n                {\n                  title: t('home:services.items.foreign_trade_lines.title'),\n                  description: t('home:services.items.foreign_trade_lines.description'),\n                  color: \"from-blue-500 to-cyan-500\",\n                  bgColor: \"bg-blue-50\",\n                  iconColor: \"text-blue-600\"\n                },\n                {\n                  title: t('home:services.items.ecommerce_lines.title'),\n                  description: t('home:services.items.ecommerce_lines.description'),\n                  color: \"from-green-500 to-emerald-500\",\n                  bgColor: \"bg-green-50\",\n                  iconColor: \"text-green-600\"\n                },\n                {\n                  title: t('home:services.items.vpn_services.title'),\n                  description: t('home:services.items.vpn_services.description'),\n                  color: \"from-purple-500 to-indigo-500\",\n                  bgColor: \"bg-purple-50\",\n                  iconColor: \"text-purple-600\"\n                },\n                {\n                  title: t('home:services.items.custom_solution.title'),\n                  description: t('home:services.items.custom_solution.description'),\n                  color: \"from-orange-500 to-red-500\",\n                  bgColor: \"bg-orange-50\",\n                  iconColor: \"text-orange-600\"\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"group relative\">\n                  <div className=\"relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden\">\n                    <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />\n                    \n                    <div className={`inline-flex items-center justify-center w-16 h-16 ${service.bgColor} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                      <div className={`h-8 w-8 ${service.iconColor} rounded`}></div>\n                    </div>\n\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                      {service.title}\n                    </h3>\n                    <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                      {service.description}\n                    </p>\n\n                    <Link\n                      href=\"/contact\"\n                      className={`inline-flex items-center text-sm font-semibold bg-gradient-to-r ${service.color} bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300`}\n                    >\n                      {t('cta.learn_more')}\n                      <svg className=\"ml-1 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n                      </svg>\n                    </Link>\n\n                    <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${service.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left`} />\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"text-center mt-16\">\n              <p className=\"text-lg text-gray-600 mb-8\">\n                {t('home:services.cta_text')}\n              </p>\n              <Link\n                href=\"/contact\"\n                className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n              >\n                {t('cta.contact_us')}\n                <svg className=\"ml-2 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n                </svg>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 bg-blue-600\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n                {t('home:cta.title')}\n              </h2>\n              <p className=\"mt-4 text-lg text-blue-100\">\n                {t('home:cta.subtitle')}\n              </p>\n              <p className=\"mt-2 text-base text-blue-200\">\n                {t('home:cta.description')}\n              </p>\n              <div className=\"mt-8\">\n                <Link\n                  href=\"/contact\"\n                  className=\"inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-blue-600 shadow-sm hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors duration-200\"\n                >\n                  {t('cta.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        </section>\n      </Layout>\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async ({ locale }) => {\n  return {\n    props: {\n      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'home'])),\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;;;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAO;IAE/C,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAO,CAAC,MAAM,EAAE,EAAE,kBAAkB;;;;;;kCACrC,qKAAC;wBAAK,MAAK;wBAAc,SAAS,EAAE;;;;;;kCACpC,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAGhC,qKAAC,+HAAA,CAAA,UAAM;;kCAEL,qKAAC;wBAAQ,WAAU;;0CACjB,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;;;;;;;;;;;0CAGjB,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAK,WAAU;;8DACd,qKAAC;oDAAK,WAAU;;;;;;gDACf,EAAE;;;;;;;sDAGL,qKAAC;4CAAG,WAAU;;8DACZ,qKAAC;oDAAK,WAAU;8DAAS,EAAE;;;;;;8DAC3B,qKAAC;oDAAK,WAAU;8DACb,EAAE;;;;;;;;;;;;sDAIP,qKAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;sDAGL,qKAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;sDAGL,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDACC,MAAK;oDACL,WAAU;;wDAET,EAAE;sEACH,qKAAC;4DAAI,WAAU;4DAA2E,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC/H,cAAA,qKAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAIzE,qKAAC;oDAAO,WAAU;;sEAChB,qKAAC;4DAAI,WAAU;4DAAuE,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC3H,cAAA,qKAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,EAAE;;;;;;;;;;;;;sDAIP,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;;;;;;sEACf,qKAAC;4DAAK,WAAU;sEAAuB,EAAE;;;;;;;;;;;;8DAE3C,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;;;;;;sEACf,qKAAC;4DAAK,WAAU;sEAAuB,EAAE;;;;;;;;;;;;8DAE3C,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;;;;;;sEACf,qKAAC;4DAAK,WAAU;sEAAuB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAK,WAAU;sDACb,EAAE;;;;;;sDAEL,qKAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,qKAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;8CAIP,qKAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,OAAO,EAAE;4CACT,aAAa,EAAE;4CACf,OAAO;4CACP,SAAS;4CACT,WAAW;wCACb;wCACA;4CACE,OAAO,EAAE;4CACT,aAAa,EAAE;4CACf,OAAO;4CACP,SAAS;4CACT,WAAW;wCACb;wCACA;4CACE,OAAO,EAAE;4CACT,aAAa,EAAE;4CACf,OAAO;4CACP,SAAS;4CACT,WAAW;wCACb;wCACA;4CACE,OAAO,EAAE;4CACT,aAAa,EAAE;4CACf,OAAO;4CACP,SAAS;4CACT,WAAW;wCACb;qCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,qKAAC;4CAAgB,WAAU;sDACzB,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAW,CAAC,mCAAmC,EAAE,QAAQ,KAAK,CAAC,gEAAgE,CAAC;;;;;;kEAErI,qKAAC;wDAAI,WAAW,CAAC,kDAAkD,EAAE,QAAQ,OAAO,CAAC,wEAAwE,CAAC;kEAC5J,cAAA,qKAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,CAAC,QAAQ,CAAC;;;;;;;;;;;kEAGxD,qKAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,qKAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAGtB,qKAAC;wDACC,MAAK;wDACL,WAAW,CAAC,gEAAgE,EAAE,QAAQ,KAAK,CAAC,0FAA0F,CAAC;;4DAEtL,EAAE;0EACH,qKAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACnE,cAAA,qKAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAIzE,qKAAC;wDAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,KAAK,CAAC,0FAA0F,CAAC;;;;;;;;;;;;2CAzB5K;;;;;;;;;;8CA+Bd,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;sDAEL,qKAAC;4CACC,MAAK;4CACL,WAAU;;gDAET,EAAE;8DACH,qKAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DACnE,cAAA,qKAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,qKAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;kDAEL,qKAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;kDAEL,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CACC,MAAK;4CACL,WAAU;sDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;AAEO,MAAM,iBAAiC,OAAO,EAAE,MAAM,EAAE;IAC7D,OAAO;QACL,OAAO;YACL,GAAI,MAAM,CAAA,GAAA,uLAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,MAAM;gBAAC;gBAAU;aAAO,CAAC;QACtE;IACF;AACF", "debugId": null}}]}