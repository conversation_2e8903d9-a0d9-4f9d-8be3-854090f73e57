{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/_app.tsx"], "sourcesContent": ["import type { AppProps } from 'next/app';\nimport { appWithTranslation } from 'next-i18next';\nimport '../styles/globals.css';\n\nfunction App({ Component, pageProps }: AppProps) {\n  return (\n    <div className=\"font-sans antialiased\">\n      <Component {...pageProps} />\n    </div>\n  );\n}\n\nexport default appWithTranslation(App);\n"], "names": [], "mappings": ";;;;AACA;;;;AAGA,SAAS,IAAI,EAAE,SAAS,EAAE,SAAS,EAAY;IAC7C,qBACE,qKAAC;QAAI,WAAU;kBACb,cAAA,qKAAC;YAAW,GAAG,SAAS;;;;;;;;;;;AAG9B;uCAEe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}]}