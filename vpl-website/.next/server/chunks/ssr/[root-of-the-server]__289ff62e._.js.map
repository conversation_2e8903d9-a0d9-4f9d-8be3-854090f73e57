{"version": 3, "sources": [], "sections": [{"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/lib/notifications.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nclass NotificationService {\n  private socket: Socket | null = null;\n  private isConnected = false;\n  private connectionAttempts = 0;\n  private maxRetries = 3;\n\n  connect() {\n    if (typeof window !== 'undefined' && !this.socket && this.connectionAttempts < this.maxRetries) {\n      try {\n        this.connectionAttempts++;\n        console.log(`Attempting to connect to Socket.IO server (attempt ${this.connectionAttempts})`);\n\n        this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {\n          path: '/api/socket',\n          transports: ['polling', 'websocket'],\n          timeout: 5000,\n          forceNew: true,\n        });\n\n        this.socket.on('connect', () => {\n          console.log('Connected to notification service');\n          this.isConnected = true;\n          this.connectionAttempts = 0; // Reset on successful connection\n        });\n\n        this.socket.on('disconnect', () => {\n          console.log('Disconnected from notification service');\n          this.isConnected = false;\n        });\n\n        this.socket.on('connect_error', (error) => {\n          console.warn('Socket.IO connection error:', error.message);\n          this.isConnected = false;\n\n          if (this.connectionAttempts >= this.maxRetries) {\n            console.warn('Max connection attempts reached. Socket.IO features will be disabled.');\n          }\n        });\n\n        this.socket.on('admin-joined', (data) => {\n          console.log('Successfully joined admin room:', data);\n        });\n\n      } catch (error) {\n        console.error('Failed to initialize Socket.IO:', error);\n        this.isConnected = false;\n      }\n    }\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n    }\n  }\n\n  joinAdminRoom() {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('join-admin');\n    } else {\n      console.warn('Cannot join admin room: Socket.IO not connected');\n    }\n  }\n\n  onNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.on('new-submission', callback);\n    } else {\n      console.warn('Cannot listen for new submissions: Socket.IO not available');\n    }\n  }\n\n  offNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.off('new-submission', callback);\n    }\n  }\n\n  emitNewSubmission(data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('new-submission', data);\n    } else {\n      console.warn('Cannot emit new submission: Socket.IO not connected');\n    }\n  }\n\n  // Admin notification methods\n  notifyAdmins(type: string, data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.to('admin').emit('admin-notification', {\n        type,\n        data,\n        timestamp: new Date().toISOString(),\n      });\n    } else {\n      console.warn('Cannot notify admins: Socket.IO not connected');\n    }\n  }\n\n  onAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.on('admin-notification', callback);\n    } else {\n      console.warn('Cannot listen for admin notifications: Socket.IO not available');\n    }\n  }\n\n  offAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.off('admin-notification', callback);\n    }\n  }\n\n  // Check if service is available\n  isAvailable(): boolean {\n    return this.socket !== null && this.isConnected;\n  }\n\n  // Get connection status\n  getStatus(): string {\n    if (!this.socket) return 'not-initialized';\n    if (this.isConnected) return 'connected';\n    return 'disconnected';\n  }\n}\n\nexport const notificationService = new NotificationService();\n\n// Browser notification utilities\nexport const requestNotificationPermission = async (): Promise<boolean> => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return false;\n  }\n\n  if (Notification.permission === 'granted') {\n    return true;\n  }\n\n  if (Notification.permission === 'denied') {\n    return false;\n  }\n\n  const permission = await Notification.requestPermission();\n  return permission === 'granted';\n};\n\nexport const showBrowserNotification = (title: string, options?: NotificationOptions) => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return;\n  }\n\n  if (Notification.permission === 'granted') {\n    new Notification(title, {\n      icon: '/favicon.ico',\n      badge: '/favicon.ico',\n      ...options,\n    });\n  }\n};\n\nexport default NotificationService;\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;AAEA,MAAM;IACI,SAAwB,KAAK;IAC7B,cAAc,MAAM;IACpB,qBAAqB,EAAE;IACvB,aAAa,EAAE;IAEvB,UAAU;QACR,IAAI,gBAAkB,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU;;IAyChG;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,gBAAgB;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,gBAAgB,QAA6B,EAAE;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB;QACnC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,iBAAiB,QAA6B,EAAE;QAC9C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB;QACpC;IACF;IAEA,kBAAkB,IAAS,EAAE;QAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;QACrC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,6BAA6B;IAC7B,aAAa,IAAY,EAAE,IAAS,EAAE;QACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,sBAAsB;gBACjD;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,oBAAoB,QAAqC,EAAE;QACzD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,sBAAsB;QACvC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,qBAAqB,QAAqC,EAAE;QAC1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB;QACxC;IACF;IAEA,gCAAgC;IAChC,cAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,WAAW;IACjD;IAEA,wBAAwB;IACxB,YAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO;QAC7B,OAAO;IACT;AACF;AAEO,MAAM,sBAAsB,IAAI;AAGhC,MAAM,gCAAgC;IAC3C,IAAI,gBAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE,OAAO;IACT;;;IAUA,MAAM;AAER;AAEO,MAAM,0BAA0B,CAAC,OAAe;IACrD,IAAI,gBAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE;IACF;;;AASF;uCAEe", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/NotificationCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { BellIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { notificationService, showBrowserNotification, requestNotificationPermission } from '../../lib/notifications';\n\ninterface Notification {\n  id: string;\n  type: 'new-submission' | 'system' | 'warning';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n\nexport default function NotificationCenter() {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [connectionStatus, setConnectionStatus] = useState<string>('not-initialized');\n\n  useEffect(() => {\n    // Initialize notification service with error handling\n    try {\n      notificationService.connect();\n\n      // Check connection status periodically\n      const statusInterval = setInterval(() => {\n        setConnectionStatus(notificationService.getStatus());\n      }, 2000);\n\n      // Try to join admin room after a short delay\n      setTimeout(() => {\n        notificationService.joinAdminRoom();\n      }, 1000);\n\n      // Request browser notification permission\n      requestNotificationPermission();\n\n      return () => {\n        clearInterval(statusInterval);\n      };\n    } catch (error) {\n      console.error('Failed to initialize notification service:', error);\n      setConnectionStatus('error');\n    }\n\n    // Listen for new submissions\n    const handleNewSubmission = (data: any) => {\n      const notification: Notification = {\n        id: Date.now().toString(),\n        type: 'new-submission',\n        title: '新的客户咨询',\n        message: `${data.companyName} 提交了新的咨询`,\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n\n      setNotifications(prev => [notification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(notification.title, {\n        body: notification.message,\n        tag: 'new-submission',\n      });\n    };\n\n    // Listen for admin notifications\n    const handleAdminNotification = (notification: any) => {\n      const newNotification: Notification = {\n        id: Date.now().toString(),\n        type: notification.type,\n        title: notification.title || '系统通知',\n        message: notification.message,\n        timestamp: notification.timestamp,\n        read: false,\n      };\n\n      setNotifications(prev => [newNotification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(newNotification.title, {\n        body: newNotification.message,\n        tag: notification.type,\n      });\n    };\n\n    notificationService.onNewSubmission(handleNewSubmission);\n    notificationService.onAdminNotification(handleAdminNotification);\n\n    return () => {\n      notificationService.offNewSubmission(handleNewSubmission);\n      notificationService.offAdminNotification(handleAdminNotification);\n      notificationService.disconnect();\n    };\n  }, []);\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id\n          ? { ...notification, read: true }\n          : notification\n      )\n    );\n    setUnreadCount(prev => Math.max(0, prev - 1));\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n    setUnreadCount(0);\n  };\n\n  const removeNotification = (id: string) => {\n    const notification = notifications.find(n => n.id === id);\n    if (notification && !notification.read) {\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    }\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'new-submission':\n        return '📧';\n      case 'system':\n        return '⚙️';\n      case 'warning':\n        return '⚠️';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) {\n      return '刚刚';\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes}分钟前`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)}小时前`;\n    } else {\n      return date.toLocaleDateString('zh-CN');\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\"\n      >\n        <BellIcon className=\"h-6 w-6\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n        {/* Connection status indicator */}\n        <span className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full ${\n          connectionStatus === 'connected' ? 'bg-green-500' :\n          connectionStatus === 'disconnected' ? 'bg-yellow-500' :\n          'bg-gray-400'\n        }`} title={`Socket.IO: ${connectionStatus}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 max-h-96 overflow-hidden\">\n            <div className=\"p-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-medium text-gray-900\">通知</h3>\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-500\"\n                  >\n                    全部标记为已读\n                  </button>\n                )}\n              </div>\n              {/* Connection status */}\n              <div className=\"mt-2 text-xs text-gray-500\">\n                实时通知: {\n                  connectionStatus === 'connected' ? '🟢 已连接' :\n                  connectionStatus === 'disconnected' ? '🟡 连接中断' :\n                  connectionStatus === 'error' ? '🔴 连接错误' :\n                  '⚪ 未连接'\n                }\n              </div>\n            </div>\n            \n            <div className=\"max-h-64 overflow-y-auto\">\n              {notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  暂无通知\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${\n                      !notification.read ? 'bg-blue-50' : ''\n                    }`}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-start space-x-3 flex-1\">\n                        <span className=\"text-lg\">\n                          {getNotificationIcon(notification.type)}\n                        </span>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {notification.title}\n                          </p>\n                          <p className=\"text-sm text-gray-600 mt-1\">\n                            {notification.message}\n                          </p>\n                          <p className=\"text-xs text-gray-400 mt-1\">\n                            {formatTimestamp(notification.timestamp)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {!notification.read && (\n                          <button\n                            onClick={() => markAsRead(notification.id)}\n                            className=\"w-2 h-2 bg-blue-500 rounded-full\"\n                            title=\"标记为已读\"\n                          />\n                        )}\n                        <button\n                          onClick={() => removeNotification(notification.id)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;;;AAJA;;;;;AAee,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,sDAAsD;QACtD,IAAI;YACF,oHAAA,CAAA,sBAAmB,CAAC,OAAO;YAE3B,uCAAuC;YACvC,MAAM,iBAAiB,YAAY;gBACjC,oBAAoB,oHAAA,CAAA,sBAAmB,CAAC,SAAS;YACnD,GAAG;YAEH,6CAA6C;YAC7C,WAAW;gBACT,oHAAA,CAAA,sBAAmB,CAAC,aAAa;YACnC,GAAG;YAEH,0CAA0C;YAC1C,CAAA,GAAA,oHAAA,CAAA,gCAA6B,AAAD;YAE5B,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,oBAAoB;QACtB;QAEA,6BAA6B;QAC7B,MAAM,sBAAsB,CAAC;YAC3B,MAAM,eAA6B;gBACjC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,OAAO;gBACP,SAAS,GAAG,KAAK,WAAW,CAAC,QAAQ,CAAC;gBACtC,WAAW,IAAI,OAAO,WAAW;gBACjC,MAAM;YACR;YAEA,iBAAiB,CAAA,OAAQ;oBAAC;uBAAiB;iBAAK;YAChD,eAAe,CAAA,OAAQ,OAAO;YAE9B,4BAA4B;YAC5B,CAAA,GAAA,oHAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa,KAAK,EAAE;gBAC1C,MAAM,aAAa,OAAO;gBAC1B,KAAK;YACP;QACF;QAEA,iCAAiC;QACjC,MAAM,0BAA0B,CAAC;YAC/B,MAAM,kBAAgC;gBACpC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,aAAa,IAAI;gBACvB,OAAO,aAAa,KAAK,IAAI;gBAC7B,SAAS,aAAa,OAAO;gBAC7B,WAAW,aAAa,SAAS;gBACjC,MAAM;YACR;YAEA,iBAAiB,CAAA,OAAQ;oBAAC;uBAAoB;iBAAK;YACnD,eAAe,CAAA,OAAQ,OAAO;YAE9B,4BAA4B;YAC5B,CAAA,GAAA,oHAAA,CAAA,0BAAuB,AAAD,EAAE,gBAAgB,KAAK,EAAE;gBAC7C,MAAM,gBAAgB,OAAO;gBAC7B,KAAK,aAAa,IAAI;YACxB;QACF;QAEA,oHAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QACpC,oHAAA,CAAA,sBAAmB,CAAC,mBAAmB,CAAC;QAExC,OAAO;YACL,oHAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC;YACrC,oHAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;YACzC,oHAAA,CAAA,sBAAmB,CAAC,UAAU;QAChC;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAC9B;QAGR,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IAC5C;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;QAE3D,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,gBAAgB,CAAC,aAAa,IAAI,EAAE;YACtC,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C;QACA,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,GAAG,cAAc,GAAG,CAAC;QAC9B,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;QAC/C,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,qKAAC,wMAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBACnB,cAAc,mBACb,qKAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;kCAIhC,qKAAC;wBAAK,WAAW,CAAC,iDAAiD,EACjE,qBAAqB,cAAc,iBACnC,qBAAqB,iBAAiB,kBACtC,eACA;wBAAE,OAAO,CAAC,WAAW,EAAE,kBAAkB;;;;;;;;;;;;YAG5C,wBACC;;kCACE,qKAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,cAAc,mBACb,qKAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAML,qKAAC;wCAAI,WAAU;;4CAA6B;4CAExC,qBAAqB,cAAc,WACnC,qBAAqB,iBAAiB,YACtC,qBAAqB,UAAU,YAC/B;;;;;;;;;;;;;0CAKN,qKAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,qKAAC;oCAAI,WAAU;8CAAgC;;;;;2CAI/C,cAAc,GAAG,CAAC,CAAC,6BACjB,qKAAC;wCAEC,WAAW,CAAC,8CAA8C,EACxD,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;kDAEF,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAK,WAAU;sEACb,oBAAoB,aAAa,IAAI;;;;;;sEAExC,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEAAE,WAAU;8EACV,aAAa,KAAK;;;;;;8EAErB,qKAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,qKAAC;oEAAE,WAAU;8EACV,gBAAgB,aAAa,SAAS;;;;;;;;;;;;;;;;;;8DAI7C,qKAAC;oDAAI,WAAU;;wDACZ,CAAC,aAAa,IAAI,kBACjB,qKAAC;4DACC,SAAS,IAAM,WAAW,aAAa,EAAE;4DACzC,WAAU;4DACV,OAAM;;;;;;sEAGV,qKAAC;4DACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,WAAU;sEAEV,cAAA,qKAAC,0MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAlCtB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AA+CxC", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Link from 'next/link';\nimport { \n  HomeIcon,\n  UsersIcon,\n  EnvelopeIcon,\n  ChartBarIcon,\n  CogIcon,\n  DocumentTextIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport NotificationCenter from './NotificationCenter';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: '仪表板', href: '/admin/dashboard', icon: HomeIcon },\n  { name: '咨询管理', href: '/admin/inquiries', icon: EnvelopeIcon },\n  { name: '用户管理', href: '/admin/users', icon: UsersIcon },\n  { name: '数据分析', href: '/admin/analytics', icon: ChartBarIcon },\n  { \n    name: '系统设置', \n    icon: CogIcon,\n    children: [\n      { name: '基本配置', href: '/admin/system/config', icon: CogIcon },\n      { name: '邮件设置', href: '/admin/email-settings', icon: EnvelopeIcon },\n      { name: '多语言管理', href: '/admin/system/localization', icon: GlobeAltIcon },\n      { name: '安全设置', href: '/admin/system/security', icon: ShieldCheckIcon },\n    ]\n  },\n  { \n    name: '内容管理', \n    icon: DocumentTextIcon,\n    children: [\n      { name: '页面内容', href: '/admin/content/pages', icon: DocumentTextIcon },\n      { name: '服务类型', href: '/admin/content/services', icon: ServerIcon },\n    ]\n  },\n];\n\nexport default function AdminLayout({ children, title }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedItems, setExpandedItems] = useState<string[]>([]);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [userInfo, setUserInfo] = useState<any>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    fetch('/api/admin/verify', {\n      headers: { 'Authorization': `Bearer ${token}` }\n    })\n    .then(response => response.json())\n    .then(result => {\n      if (result.success) {\n        setIsAuthenticated(true);\n        setUserInfo(result.user);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const toggleExpanded = (itemName: string) => {\n    setExpandedItems(prev => \n      prev.includes(itemName) \n        ? prev.filter(name => name !== itemName)\n        : [...prev, itemName]\n    );\n  };\n\n  const isCurrentPath = (href: string) => {\n    return router.pathname === href || router.pathname.startsWith(href + '/');\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XMarkIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <SidebarContent \n            navigation={navigation} \n            expandedItems={expandedItems}\n            toggleExpanded={toggleExpanded}\n            isCurrentPath={isCurrentPath}\n          />\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <SidebarContent \n            navigation={navigation} \n            expandedItems={expandedItems}\n            toggleExpanded={toggleExpanded}\n            isCurrentPath={isCurrentPath}\n          />\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Top bar */}\n        <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Bars3Icon className=\"h-6 w-6\" />\n          </button>\n          \n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex items-center\">\n              {title && (\n                <h1 className=\"text-2xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            \n            <div className=\"ml-4 flex items-center md:ml-6 space-x-4\">\n              <NotificationCenter />\n              \n              {/* User menu */}\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-700\">{userInfo?.fullName || userInfo?.username}</div>\n                  <div className=\"text-gray-500\">{userInfo?.role}</div>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-400 hover:text-gray-600\"\n                  title=\"退出登录\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n\nfunction SidebarContent({ \n  navigation, \n  expandedItems, \n  toggleExpanded, \n  isCurrentPath \n}: {\n  navigation: any[];\n  expandedItems: string[];\n  toggleExpanded: (name: string) => void;\n  isCurrentPath: (href: string) => boolean;\n}) {\n  return (\n    <div className=\"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\">\n      {/* Logo */}\n      <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n        <div className=\"flex items-center flex-shrink-0 px-4\">\n          <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg mr-3\">\n            <span className=\"text-white font-bold\">VPL</span>\n          </div>\n          <span className=\"text-xl font-semibold text-gray-900\">管理后台</span>\n        </div>\n        \n        {/* Navigation */}\n        <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n          {navigation.map((item) => (\n            <div key={item.name}>\n              {item.children ? (\n                <div>\n                  <button\n                    onClick={() => toggleExpanded(item.name)}\n                    className={`group w-full flex items-center pl-2 pr-1 py-2 text-left text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      expandedItems.includes(item.name)\n                        ? 'bg-gray-100 text-gray-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                    {item.name}\n                    <svg\n                      className={`ml-auto h-5 w-5 transform transition-colors duration-150 ${\n                        expandedItems.includes(item.name) ? 'rotate-90 text-gray-400' : 'text-gray-300'\n                      }`}\n                      viewBox=\"0 0 20 20\"\n                      fill=\"currentColor\"\n                    >\n                      <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </button>\n                  {expandedItems.includes(item.name) && (\n                    <div className=\"mt-1 space-y-1\">\n                      {item.children.map((subItem: any) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          className={`group w-full flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md ${\n                            isCurrentPath(subItem.href)\n                              ? 'bg-blue-100 text-blue-700'\n                              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                          }`}\n                        >\n                          <subItem.icon className=\"mr-3 h-4 w-4\" />\n                          {subItem.name}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <Link\n                  href={item.href}\n                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                    isCurrentPath(item.href)\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                >\n                  <item.icon className={`mr-3 h-5 w-5 ${\n                    isCurrentPath(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'\n                  }`} />\n                  {item.name}\n                </Link>\n              )}\n            </div>\n          ))}\n        </nav>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAoB,MAAM,wMAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,gNAAA,CAAA,eAAY;IAAC;IAC7D;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,0MAAA,CAAA,YAAS;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,gNAAA,CAAA,eAAY;IAAC;IAC7D;QACE,MAAM;QACN,MAAM,sMAAA,CAAA,UAAO;QACb,UAAU;YACR;gBAAE,MAAM;gBAAQ,MAAM;gBAAwB,MAAM,sMAAA,CAAA,UAAO;YAAC;YAC5D;gBAAE,MAAM;gBAAQ,MAAM;gBAAyB,MAAM,gNAAA,CAAA,eAAY;YAAC;YAClE;gBAAE,MAAM;gBAAS,MAAM;gBAA8B,MAAM,gNAAA,CAAA,eAAY;YAAC;YACxE;gBAAE,MAAM;gBAAQ,MAAM;gBAA0B,MAAM,sNAAA,CAAA,kBAAe;YAAC;SACvE;IACH;IACA;QACE,MAAM;QACN,MAAM,wNAAA,CAAA,mBAAgB;QACtB,UAAU;YACR;gBAAE,MAAM;gBAAQ,MAAM;gBAAwB,MAAM,wNAAA,CAAA,mBAAgB;YAAC;YACrE;gBAAE,MAAM;gBAAQ,MAAM;gBAA2B,MAAM,4MAAA,CAAA,aAAU;YAAC;SACnE;IACH;CACD;AAEc,SAAS,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAoB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,qBAAqB;YACzB,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD,GACC,IAAI,CAAC,CAAA,WAAY,SAAS,IAAI,IAC9B,IAAI,CAAC,CAAA;YACJ,IAAI,OAAO,OAAO,EAAE;gBAClB,mBAAmB;gBACnB,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO;IACvE;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BAEb,qKAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,qKAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,qKAAC;gCACC,YAAY;gCACZ,eAAe;gCACf,gBAAgB;gCAChB,eAAe;;;;;;;;;;;;;;;;;;0BAMrB,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBACC,YAAY;wBACZ,eAAe;wBACf,gBAAgB;wBAChB,eAAe;;;;;;;;;;;;;;;;0BAMrB,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,qKAAC,0MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAGvB,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACZ,uBACC,qKAAC;4CAAG,WAAU;sDAAwC;;;;;;;;;;;kDAI1D,qKAAC;wCAAI,WAAU;;0DACb,qKAAC,0IAAA,CAAA,UAAkB;;;;;0DAGnB,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAA6B,UAAU,YAAY,UAAU;;;;;;0EAC5E,qKAAC;gEAAI,WAAU;0EAAiB,UAAU;;;;;;;;;;;;kEAE5C,qKAAC;wDACC,SAAS;wDACT,WAAU;wDACV,OAAM;kEAEN,cAAA,qKAAC,0OAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,qKAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,UAAU,EACV,aAAa,EACb,cAAc,EACd,aAAa,EAMd;IACC,qBACE,qKAAC;QAAI,WAAU;kBAEb,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;sCAEzC,qKAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;8BAIxD,qKAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC;sCACE,KAAK,QAAQ,iBACZ,qKAAC;;kDACC,qKAAC;wCACC,SAAS,IAAM,eAAe,KAAK,IAAI;wCACvC,WAAW,CAAC,2IAA2I,EACrJ,cAAc,QAAQ,CAAC,KAAK,IAAI,IAC5B,8BACA,sDACJ;;0DAEF,qKAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;0DACV,qKAAC;gDACC,WAAW,CAAC,yDAAyD,EACnE,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,4BAA4B,iBAChE;gDACF,SAAQ;gDACR,MAAK;0DAEL,cAAA,qKAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;oCAG5J,cAAc,QAAQ,CAAC,KAAK,IAAI,mBAC/B,qKAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,WAAW,CAAC,8EAA8E,EACxF,cAAc,QAAQ,IAAI,IACtB,8BACA,sDACJ;;kEAEF,qKAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;oDACvB,QAAQ,IAAI;;+CATR,QAAQ,IAAI;;;;;;;;;;;;;;;qDAgB3B,qKAAC,qHAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,iEAAiE,EAC3E,cAAc,KAAK,IAAI,IACnB,8BACA,sDACJ;;kDAEF,qKAAC,KAAK,IAAI;wCAAC,WAAW,CAAC,aAAa,EAClC,cAAc,KAAK,IAAI,IAAI,kBAAkB,2CAC7C;;;;;;oCACD,KAAK,IAAI;;;;;;;2BAtDN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AA+D/B", "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/content/services.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport Head from 'next/head';\nimport AdminLayout from '../../../components/admin/AdminLayout';\nimport { \n  CogIcon,\n  PencilIcon,\n  EyeIcon,\n  PlusIcon,\n  TrashIcon\n} from '@heroicons/react/24/outline';\n\ninterface ServiceContent {\n  id: string;\n  name: string;\n  slug: string;\n  title: string;\n  description: string;\n  features: string[];\n  status: 'active' | 'inactive';\n  lastModified: string;\n}\n\nexport default function ServicesContent() {\n  const [services, setServices] = useState<ServiceContent[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedService, setSelectedService] = useState<ServiceContent | null>(null);\n  const [isEditing, setIsEditing] = useState(false);\n\n  // Mock data - in real app, this would come from API\n  useEffect(() => {\n    const mockServices: ServiceContent[] = [\n      {\n        id: '1',\n        name: 'VPN服务',\n        slug: 'vpn_services',\n        title: 'VPN服务 - 安全可靠的网络连接',\n        description: '军用级加密的VPN连接服务，保护您的数据安全',\n        features: ['军用级加密', '零日志政策', '全球服务器', '无限带宽'],\n        status: 'active',\n        lastModified: '2024-01-15'\n      },\n      {\n        id: '2',\n        name: '跨境电商专线',\n        slug: 'ecommerce_lines',\n        title: '跨境电商专线 - 优化购物体验',\n        description: '优化的跨境电商网络连接解决方案，提升用户购物体验',\n        features: ['多平台支持', '高可用性', '流量优化', '实时监控'],\n        status: 'active',\n        lastModified: '2024-01-14'\n      },\n      {\n        id: '3',\n        name: '外贸专线',\n        slug: 'foreign_trade_lines',\n        title: '外贸专线 - 全球贸易网络',\n        description: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',\n        features: ['专用带宽', '全球覆盖', '低延迟', '企业级支持'],\n        status: 'active',\n        lastModified: '2024-01-13'\n      },\n      {\n        id: '4',\n        name: '定制解决方案',\n        slug: 'custom_solution',\n        title: '定制解决方案 - 专属网络方案',\n        description: '根据您的特殊需求定制专属网络解决方案',\n        features: ['定制设计', '专家咨询', '可扩展架构', '持续支持'],\n        status: 'active',\n        lastModified: '2024-01-12'\n      }\n    ];\n\n    setTimeout(() => {\n      setServices(mockServices);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const handleEdit = (service: ServiceContent) => {\n    setSelectedService(service);\n    setIsEditing(true);\n  };\n\n  const handleSave = async (updatedService: ServiceContent) => {\n    // In real app, this would make API call\n    setServices(prev => \n      prev.map(s => s.id === updatedService.id ? updatedService : s)\n    );\n    setIsEditing(false);\n    setSelectedService(null);\n  };\n\n  const handleDelete = async (serviceId: string) => {\n    if (confirm('确定要删除这个服务吗？')) {\n      // In real app, this would make API call\n      setServices(prev => prev.filter(s => s.id !== serviceId));\n    }\n  };\n\n  const toggleStatus = async (serviceId: string) => {\n    // In real app, this would make API call\n    setServices(prev => \n      prev.map(s => \n        s.id === serviceId \n          ? { ...s, status: s.status === 'active' ? 'inactive' : 'active' }\n          : s\n      )\n    );\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <>\n      <Head>\n        <title>服务内容管理 - VPL后台管理系统</title>\n        <meta name=\"description\" content=\"管理网站服务内容\" />\n      </Head>\n      \n      <AdminLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <CogIcon className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">服务内容管理</h1>\n                <p className=\"text-gray-600\">管理网站上的服务内容和描述</p>\n              </div>\n            </div>\n            <button\n              onClick={() => setIsEditing(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <PlusIcon className=\"h-4 w-4 mr-2\" />\n              添加服务\n            </button>\n          </div>\n\n          {/* Services Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {services.map((service) => (\n              <div key={service.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{service.name}</h3>\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      service.status === 'active' \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-gray-100 text-gray-800'\n                    }`}>\n                      {service.status === 'active' ? '已启用' : '已禁用'}\n                    </span>\n                  </div>\n                  \n                  <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">{service.description}</p>\n                  \n                  <div className=\"mb-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-2\">特色功能</h4>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {service.features.slice(0, 3).map((feature, index) => (\n                        <span key={index} className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800\">\n                          {feature}\n                        </span>\n                      ))}\n                      {service.features.length > 3 && (\n                        <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600\">\n                          +{service.features.length - 3}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"text-xs text-gray-500 mb-4\">\n                    最后修改: {service.lastModified}\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => handleEdit(service)}\n                        className=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                      >\n                        <PencilIcon className=\"h-3 w-3 mr-1\" />\n                        编辑\n                      </button>\n                      <button\n                        onClick={() => window.open(`/services/${service.slug}`, '_blank')}\n                        className=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                      >\n                        <EyeIcon className=\"h-3 w-3 mr-1\" />\n                        预览\n                      </button>\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => toggleStatus(service.id)}\n                        className={`inline-flex items-center px-3 py-1.5 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 ${\n                          service.status === 'active'\n                            ? 'border-red-300 text-red-700 bg-white hover:bg-red-50 focus:ring-red-500'\n                            : 'border-green-300 text-green-700 bg-white hover:bg-green-50 focus:ring-green-500'\n                        }`}\n                      >\n                        {service.status === 'active' ? '禁用' : '启用'}\n                      </button>\n                      <button\n                        onClick={() => handleDelete(service.id)}\n                        className=\"inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n                      >\n                        <TrashIcon className=\"h-3 w-3 mr-1\" />\n                        删除\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Empty State */}\n          {services.length === 0 && (\n            <div className=\"text-center py-12\">\n              <CogIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无服务</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">开始添加您的第一个服务内容</p>\n              <div className=\"mt-6\">\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  添加服务\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Edit Modal - Placeholder */}\n        {isEditing && (\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n            <div className=\"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\">\n              <div className=\"mt-3\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                  {selectedService ? '编辑服务' : '添加服务'}\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  服务编辑功能正在开发中...\n                </p>\n                <div className=\"flex justify-end space-x-3\">\n                  <button\n                    onClick={() => {\n                      setIsEditing(false);\n                      setSelectedService(null);\n                    }}\n                    className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                  >\n                    取消\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oDAAoD;IACpD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAiC;YACrC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAS;oBAAS;oBAAS;iBAAO;gBAC7C,QAAQ;gBACR,cAAc;YAChB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAS;oBAAQ;oBAAQ;iBAAO;gBAC3C,QAAQ;gBACR,cAAc;YAChB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAQ;oBAAQ;oBAAO;iBAAQ;gBAC1C,QAAQ;gBACR,cAAc;YAChB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAQ;oBAAQ;oBAAS;iBAAO;gBAC3C,QAAQ;gBACR,cAAc;YAChB;SACD;QAED,WAAW;YACT,YAAY;YACZ,WAAW;QACb,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,aAAa,OAAO;QACxB,wCAAwC;QACxC,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;QAE9D,aAAa;QACb,mBAAmB;IACrB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,gBAAgB;YAC1B,wCAAwC;YACxC,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,wCAAwC;QACxC,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,YACL;oBAAE,GAAG,CAAC;oBAAE,QAAQ,EAAE,MAAM,KAAK,WAAW,aAAa;gBAAS,IAC9D;IAGV;IAEA,IAAI,SAAS;QACX,qBACE,qKAAC,mIAAA,CAAA,UAAW;sBACV,cAAA,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAGnC,qKAAC,mIAAA,CAAA,UAAW;;kCACV,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC,sMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,qKAAC;;kEACC,qKAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,qKAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAGjC,qKAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,qKAAC,wMAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAMzC,qKAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,qKAAC;wCAAqB,WAAU;kDAC9B,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAG,WAAU;sEAAuC,QAAQ,IAAI;;;;;;sEACjE,qKAAC;4DAAK,WAAW,CAAC,wEAAwE,EACxF,QAAQ,MAAM,KAAK,WACf,gCACA,6BACJ;sEACC,QAAQ,MAAM,KAAK,WAAW,QAAQ;;;;;;;;;;;;8DAI3C,qKAAC;oDAAE,WAAU;8DAA2C,QAAQ,WAAW;;;;;;8DAE3E,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAG,WAAU;sEAAyC;;;;;;sEACvD,qKAAC;4DAAI,WAAU;;gEACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,qKAAC;wEAAiB,WAAU;kFACzB;uEADQ;;;;;gEAIZ,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,qKAAC;oEAAK,WAAU;;wEAA2F;wEACvG,QAAQ,QAAQ,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;8DAMpC,qKAAC;oDAAI,WAAU;;wDAA6B;wDACnC,QAAQ,YAAY;;;;;;;8DAG7B,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEACC,SAAS,IAAM,WAAW;oEAC1B,WAAU;;sFAEV,qKAAC,4MAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGzC,qKAAC;oEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE,EAAE;oEACxD,WAAU;;sFAEV,qKAAC,sMAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;sEAKxC,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEACC,SAAS,IAAM,aAAa,QAAQ,EAAE;oEACtC,WAAW,CAAC,4HAA4H,EACtI,QAAQ,MAAM,KAAK,WACf,4EACA,mFACJ;8EAED,QAAQ,MAAM,KAAK,WAAW,OAAO;;;;;;8EAExC,qKAAC;oEACC,SAAS,IAAM,aAAa,QAAQ,EAAE;oEACtC,WAAU;;sFAEV,qKAAC,0MAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;uCApEtC,QAAQ,EAAE;;;;;;;;;;4BA+EvB,SAAS,MAAM,KAAK,mBACnB,qKAAC;gCAAI,WAAU;;kDACb,qKAAC,sMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,qKAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,qKAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;;8DAEV,qKAAC,wMAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;oBAS9C,2BACC,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDACX,kBAAkB,SAAS;;;;;;kDAE9B,qKAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CACC,SAAS;gDACP,aAAa;gDACb,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}