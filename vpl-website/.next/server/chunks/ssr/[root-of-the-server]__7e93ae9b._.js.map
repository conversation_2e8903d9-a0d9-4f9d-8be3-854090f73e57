{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/inquiries.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport { \n  MagnifyingGlassIcon,\n  FunnelIcon,\n  EllipsisVerticalIcon,\n  EyeIcon,\n  PencilIcon,\n  TrashIcon,\n  ArrowDownTrayIcon,\n  CheckIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { ContactSubmission, SubmissionFilters, PaginationParams } from '../../types/admin';\n\nexport default function InquiriesPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [submissions, setSubmissions] = useState<ContactSubmission[]>([]);\n  const [selectedSubmissions, setSelectedSubmissions] = useState<string[]>([]);\n  const [filters, setFilters] = useState<SubmissionFilters>({});\n  const [pagination, setPagination] = useState<PaginationParams>({\n    page: 1,\n    limit: 10,\n    sortBy: 'submittedAt',\n    sortOrder: 'desc'\n  });\n  const [totalPages, setTotalPages] = useState(1);\n  const [showFilters, setShowFilters] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchSubmissions();\n    }\n  }, [isAuthenticated, filters, pagination]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchSubmissions = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const queryParams = new URLSearchParams({\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n        sortBy: pagination.sortBy || 'submittedAt',\n        sortOrder: pagination.sortOrder || 'desc',\n      });\n\n      // Add filters to query params\n      if (filters.status?.length) {\n        filters.status.forEach(status => queryParams.append('status', status));\n      }\n      if (filters.serviceType?.length) {\n        filters.serviceType.forEach(type => queryParams.append('serviceType', type));\n      }\n      if (filters.priority?.length) {\n        filters.priority.forEach(priority => queryParams.append('priority', priority));\n      }\n      if (filters.search) {\n        queryParams.append('search', filters.search);\n      }\n      if (filters.dateRange) {\n        queryParams.append('dateStart', filters.dateRange.start);\n        queryParams.append('dateEnd', filters.dateRange.end);\n      }\n\n      const response = await fetch(`/api/admin/submissions?${queryParams}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSubmissions(data.data || []);\n        setTotalPages(data.pagination?.totalPages || 1);\n      }\n    } catch (error) {\n      console.error('Failed to fetch submissions:', error);\n    }\n  };\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setFilters({ ...filters, search: searchTerm });\n    setPagination({ ...pagination, page: 1 });\n  };\n\n  const handleStatusChange = async (id: string, status: string) => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/submissions/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ status })\n      });\n\n      if (response.ok) {\n        fetchSubmissions();\n      }\n    } catch (error) {\n      console.error('Failed to update status:', error);\n    }\n  };\n\n  const handleBulkAction = async (action: string) => {\n    if (selectedSubmissions.length === 0) return;\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      let updates = {};\n\n      switch (action) {\n        case 'mark-contacted':\n          updates = { status: 'contacted' };\n          break;\n        case 'mark-completed':\n          updates = { status: 'completed' };\n          break;\n        case 'delete':\n          // Handle bulk delete\n          break;\n        default:\n          return;\n      }\n\n      const response = await fetch('/api/admin/submissions', {\n        method: 'PATCH',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          ids: selectedSubmissions,\n          updates\n        })\n      });\n\n      if (response.ok) {\n        setSelectedSubmissions([]);\n        fetchSubmissions();\n      }\n    } catch (error) {\n      console.error('Failed to perform bulk action:', error);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },\n      contacted: { color: 'bg-blue-100 text-blue-800', text: '已联系' },\n      'in-progress': { color: 'bg-purple-100 text-purple-800', text: '处理中' },\n      completed: { color: 'bg-green-100 text-green-800', text: '已完成' },\n      closed: { color: 'bg-gray-100 text-gray-800', text: '已关闭' },\n    };\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      low: { color: 'bg-gray-100 text-gray-800', text: '低' },\n      medium: { color: 'bg-blue-100 text-blue-800', text: '中' },\n      high: { color: 'bg-orange-100 text-orange-800', text: '高' },\n      urgent: { color: 'bg-red-100 text-red-800', text: '紧急' },\n    };\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getServiceTypeName = (serviceType: string) => {\n    const types: { [key: string]: string } = {\n      'foreign_trade_lines': '外贸网络线路',\n      'ecommerce_lines': '跨境电商线路',\n      'vpn_services': 'VPN服务',\n      'custom_solution': '定制解决方案',\n    };\n    return types[serviceType] || serviceType;\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>客户咨询管理 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">客户咨询管理</h1>\n                <p className=\"text-gray-600\">管理和跟踪所有客户咨询</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Search and Filters */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"p-6\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n                {/* Search */}\n                <form onSubmit={handleSearch} className=\"flex-1 max-w-lg\">\n                  <div className=\"relative\">\n                    <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      placeholder=\"搜索公司名称、联系人、邮箱...\"\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                </form>\n\n                {/* Action Buttons */}\n                <div className=\"flex items-center space-x-3\">\n                  <button\n                    onClick={() => setShowFilters(!showFilters)}\n                    className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                  >\n                    <FunnelIcon className=\"h-4 w-4 mr-2\" />\n                    筛选\n                  </button>\n                  \n                  {selectedSubmissions.length > 0 && (\n                    <div className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={() => handleBulkAction('mark-contacted')}\n                        className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n                      >\n                        标记已联系\n                      </button>\n                      <button\n                        onClick={() => handleBulkAction('mark-completed')}\n                        className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700\"\n                      >\n                        标记完成\n                      </button>\n                    </div>\n                  )}\n                  \n                  <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\">\n                    <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                    导出\n                  </button>\n                </div>\n              </div>\n\n              {/* Filters Panel */}\n              {showFilters && (\n                <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                    {/* Status Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">状态</label>\n                      <select\n                        multiple\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                        onChange={(e) => {\n                          const values = Array.from(e.target.selectedOptions, option => option.value);\n                          setFilters({ ...filters, status: values });\n                        }}\n                      >\n                        <option value=\"pending\">待处理</option>\n                        <option value=\"contacted\">已联系</option>\n                        <option value=\"in-progress\">处理中</option>\n                        <option value=\"completed\">已完成</option>\n                        <option value=\"closed\">已关闭</option>\n                      </select>\n                    </div>\n\n                    {/* Service Type Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">服务类型</label>\n                      <select\n                        multiple\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                        onChange={(e) => {\n                          const values = Array.from(e.target.selectedOptions, option => option.value);\n                          setFilters({ ...filters, serviceType: values });\n                        }}\n                      >\n                        <option value=\"foreign_trade_lines\">外贸网络线路</option>\n                        <option value=\"ecommerce_lines\">跨境电商线路</option>\n                        <option value=\"vpn_services\">VPN服务</option>\n                        <option value=\"custom_solution\">定制解决方案</option>\n                      </select>\n                    </div>\n\n                    {/* Priority Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">优先级</label>\n                      <select\n                        multiple\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                        onChange={(e) => {\n                          const values = Array.from(e.target.selectedOptions, option => option.value);\n                          setFilters({ ...filters, priority: values });\n                        }}\n                      >\n                        <option value=\"low\">低</option>\n                        <option value=\"medium\">中</option>\n                        <option value=\"high\">高</option>\n                        <option value=\"urgent\">紧急</option>\n                      </select>\n                    </div>\n\n                    {/* Date Range Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">日期范围</label>\n                      <div className=\"space-y-2\">\n                        <input\n                          type=\"date\"\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                          onChange={(e) => {\n                            setFilters({\n                              ...filters,\n                              dateRange: {\n                                start: e.target.value,\n                                end: filters.dateRange?.end || ''\n                              }\n                            });\n                          }}\n                        />\n                        <input\n                          type=\"date\"\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                          onChange={(e) => {\n                            setFilters({\n                              ...filters,\n                              dateRange: {\n                                start: filters.dateRange?.start || '',\n                                end: e.target.value\n                              }\n                            });\n                          }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Submissions Table */}\n          <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedSubmissions.length === submissions.length && submissions.length > 0}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setSelectedSubmissions(submissions.map(s => s.id));\n                          } else {\n                            setSelectedSubmissions([]);\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                      />\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      公司信息\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      服务类型\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      优先级\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      提交时间\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      操作\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {submissions.map((submission) => (\n                    <tr key={submission.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedSubmissions.includes(submission.id)}\n                          onChange={(e) => {\n                            if (e.target.checked) {\n                              setSelectedSubmissions([...selectedSubmissions, submission.id]);\n                            } else {\n                              setSelectedSubmissions(selectedSubmissions.filter(id => id !== submission.id));\n                            }\n                          }}\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">{submission.companyName}</div>\n                          <div className=\"text-sm text-gray-500\">{submission.contactPerson}</div>\n                          <div className=\"text-sm text-gray-500\">{submission.email}</div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span className=\"text-sm text-gray-900\">\n                          {getServiceTypeName(submission.serviceType)}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {getStatusBadge(submission.status)}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {getPriorityBadge(submission.priority)}\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\n                        {new Date(submission.submittedAt).toLocaleDateString('zh-CN')}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-2\">\n                          <button\n                            onClick={() => router.push(`/admin/inquiries/${submission.id}`)}\n                            className=\"text-blue-600 hover:text-blue-900\"\n                            title=\"查看详情\"\n                          >\n                            <EyeIcon className=\"h-4 w-4\" />\n                          </button>\n                          <button\n                            onClick={() => router.push(`/admin/inquiries/${submission.id}/edit`)}\n                            className=\"text-green-600 hover:text-green-900\"\n                            title=\"编辑\"\n                          >\n                            <PencilIcon className=\"h-4 w-4\" />\n                          </button>\n                          <div className=\"relative\">\n                            <select\n                              value={submission.status}\n                              onChange={(e) => handleStatusChange(submission.id, e.target.value)}\n                              className=\"text-xs border border-gray-300 rounded px-2 py-1\"\n                            >\n                              <option value=\"pending\">待处理</option>\n                              <option value=\"contacted\">已联系</option>\n                              <option value=\"in-progress\">处理中</option>\n                              <option value=\"completed\">已完成</option>\n                              <option value=\"closed\">已关闭</option>\n                            </select>\n                          </div>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Pagination */}\n            <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n              <div className=\"flex-1 flex justify-between sm:hidden\">\n                <button\n                  onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}\n                  disabled={pagination.page === 1}\n                  className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  上一页\n                </button>\n                <button\n                  onClick={() => setPagination({ ...pagination, page: Math.min(totalPages, pagination.page + 1) })}\n                  disabled={pagination.page === totalPages}\n                  className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  下一页\n                </button>\n              </div>\n              <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                <div>\n                  <p className=\"text-sm text-gray-700\">\n                    显示第 <span className=\"font-medium\">{(pagination.page - 1) * pagination.limit + 1}</span> 到{' '}\n                    <span className=\"font-medium\">\n                      {Math.min(pagination.page * pagination.limit, submissions.length)}\n                    </span>{' '}\n                    条记录\n                  </p>\n                </div>\n                <div>\n                  <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                    <button\n                      onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}\n                      disabled={pagination.page === 1}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      上一页\n                    </button>\n                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (\n                      <button\n                        key={page}\n                        onClick={() => setPagination({ ...pagination, page })}\n                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                          page === pagination.page\n                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\n                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                        }`}\n                      >\n                        {page}\n                      </button>\n                    ))}\n                    <button\n                      onClick={() => setPagination({ ...pagination, page: Math.min(totalPages, pagination.page + 1) })}\n                      disabled={pagination.page === totalPages}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      下一页\n                    </button>\n                  </nav>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACtE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAoB;QAC7D,MAAM;QACN,OAAO;QACP,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAiB;QAAS;KAAW;IAEzC,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,cAAc,IAAI,gBAAgB;gBACtC,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAChC,QAAQ,WAAW,MAAM,IAAI;gBAC7B,WAAW,WAAW,SAAS,IAAI;YACrC;YAEA,8BAA8B;YAC9B,IAAI,QAAQ,MAAM,EAAE,QAAQ;gBAC1B,QAAQ,MAAM,CAAC,OAAO,CAAC,CAAA,SAAU,YAAY,MAAM,CAAC,UAAU;YAChE;YACA,IAAI,QAAQ,WAAW,EAAE,QAAQ;gBAC/B,QAAQ,WAAW,CAAC,OAAO,CAAC,CAAA,OAAQ,YAAY,MAAM,CAAC,eAAe;YACxE;YACA,IAAI,QAAQ,QAAQ,EAAE,QAAQ;gBAC5B,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,WAAY,YAAY,MAAM,CAAC,YAAY;YACtE;YACA,IAAI,QAAQ,MAAM,EAAE;gBAClB,YAAY,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC7C;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,YAAY,MAAM,CAAC,aAAa,QAAQ,SAAS,CAAC,KAAK;gBACvD,YAAY,MAAM,CAAC,WAAW,QAAQ,SAAS,CAAC,GAAG;YACrD;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,aAAa,EAAE;gBACpE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,IAAI,IAAI,EAAE;gBAC9B,cAAc,KAAK,UAAU,EAAE,cAAc;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAW;QAC5C,cAAc;YAAE,GAAG,UAAU;YAAE,MAAM;QAAE;IACzC;IAEA,MAAM,qBAAqB,OAAO,IAAY;QAC5C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,IAAI,EAAE;gBAC3D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,oBAAoB,MAAM,KAAK,GAAG;QAEtC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,UAAU,CAAC;YAEf,OAAQ;gBACN,KAAK;oBACH,UAAU;wBAAE,QAAQ;oBAAY;oBAChC;gBACF,KAAK;oBACH,UAAU;wBAAE,QAAQ;oBAAY;oBAChC;gBACF,KAAK;oBAEH;gBACF;oBACE;YACJ;YAEA,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK;oBACL;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,uBAAuB,EAAE;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YAC/D,WAAW;gBAAE,OAAO;gBAA6B,MAAM;YAAM;YAC7D,eAAe;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YACrE,WAAW;gBAAE,OAAO;gBAA+B,MAAM;YAAM;YAC/D,QAAQ;gBAAE,OAAO;gBAA6B,MAAM;YAAM;QAC5D;QACA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBACE,qKAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,KAAK,EAAE;sBACvG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAAiB;YACrB,KAAK;gBAAE,OAAO;gBAA6B,MAAM;YAAI;YACrD,QAAQ;gBAAE,OAAO;gBAA6B,MAAM;YAAI;YACxD,MAAM;gBAAE,OAAO;gBAAiC,MAAM;YAAI;YAC1D,QAAQ;gBAAE,OAAO;gBAA2B,MAAM;YAAK;QACzD;QACA,MAAM,SAAS,cAAc,CAAC,SAAwC,IAAI,eAAe,MAAM;QAC/F,qBACE,qKAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,KAAK,EAAE;sBACvG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAmC;YACvC,uBAAuB;YACvB,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;QACrB;QACA,OAAO,KAAK,CAAC,YAAY,IAAI;IAC/B;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DAEb,qKAAC;oDAAK,UAAU;oDAAc,WAAU;8DACtC,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC,8NAAA,CAAA,sBAAmB;gEAAC,WAAU;;;;;;0EAC/B,qKAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;8DAMhB,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DACC,SAAS,IAAM,eAAe,CAAC;4DAC/B,WAAU;;8EAEV,qKAAC,4MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAIxC,oBAAoB,MAAM,GAAG,mBAC5B,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EACX;;;;;;8EAGD,qKAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EACX;;;;;;;;;;;;sEAML,qKAAC;4DAAO,WAAU;;8EAChB,qKAAC,0NAAA,CAAA,oBAAiB;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;wCAOnD,6BACC,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEAEb,qKAAC;;0EACC,qKAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,qKAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,UAAU,CAAC;oEACT,MAAM,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;oEAC1E,WAAW;wEAAE,GAAG,OAAO;wEAAE,QAAQ;oEAAO;gEAC1C;;kFAEA,qKAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,qKAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,qKAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,qKAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,qKAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAK3B,qKAAC;;0EACC,qKAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,qKAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,UAAU,CAAC;oEACT,MAAM,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;oEAC1E,WAAW;wEAAE,GAAG,OAAO;wEAAE,aAAa;oEAAO;gEAC/C;;kFAEA,qKAAC;wEAAO,OAAM;kFAAsB;;;;;;kFACpC,qKAAC;wEAAO,OAAM;kFAAkB;;;;;;kFAChC,qKAAC;wEAAO,OAAM;kFAAe;;;;;;kFAC7B,qKAAC;wEAAO,OAAM;kFAAkB;;;;;;;;;;;;;;;;;;kEAKpC,qKAAC;;0EACC,qKAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,qKAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,UAAU,CAAC;oEACT,MAAM,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;oEAC1E,WAAW;wEAAE,GAAG,OAAO;wEAAE,UAAU;oEAAO;gEAC5C;;kFAEA,qKAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,qKAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,qKAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,qKAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAK3B,qKAAC;;0EACC,qKAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,qKAAC;gEAAI,WAAU;;kFACb,qKAAC;wEACC,MAAK;wEACL,WAAU;wEACV,UAAU,CAAC;4EACT,WAAW;gFACT,GAAG,OAAO;gFACV,WAAW;oFACT,OAAO,EAAE,MAAM,CAAC,KAAK;oFACrB,KAAK,QAAQ,SAAS,EAAE,OAAO;gFACjC;4EACF;wEACF;;;;;;kFAEF,qKAAC;wEACC,MAAK;wEACL,WAAU;wEACV,UAAU,CAAC;4EACT,WAAW;gFACT,GAAG,OAAO;gFACV,WAAW;oFACT,OAAO,QAAQ,SAAS,EAAE,SAAS;oFACnC,KAAK,EAAE,MAAM,CAAC,KAAK;gFACrB;4EACF;wEACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWhB,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAM,WAAU;;8DACf,qKAAC;oDAAM,WAAU;8DACf,cAAA,qKAAC;;0EACC,qKAAC;gEAAG,WAAU;0EACZ,cAAA,qKAAC;oEACC,MAAK;oEACL,SAAS,oBAAoB,MAAM,KAAK,YAAY,MAAM,IAAI,YAAY,MAAM,GAAG;oEACnF,UAAU,CAAC;wEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4EACpB,uBAAuB,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;wEAClD,OAAO;4EACL,uBAAuB,EAAE;wEAC3B;oEACF;oEACA,WAAU;;;;;;;;;;;0EAGd,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,qKAAC;oDAAM,WAAU;8DACd,YAAY,GAAG,CAAC,CAAC,2BAChB,qKAAC;4DAAuB,WAAU;;8EAChC,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEACC,MAAK;wEACL,SAAS,oBAAoB,QAAQ,CAAC,WAAW,EAAE;wEACnD,UAAU,CAAC;4EACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gFACpB,uBAAuB;uFAAI;oFAAqB,WAAW,EAAE;iFAAC;4EAChE,OAAO;gFACL,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,KAAM,OAAO,WAAW,EAAE;4EAC9E;wEACF;wEACA,WAAU;;;;;;;;;;;8EAGd,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;;0FACC,qKAAC;gFAAI,WAAU;0FAAqC,WAAW,WAAW;;;;;;0FAC1E,qKAAC;gFAAI,WAAU;0FAAyB,WAAW,aAAa;;;;;;0FAChE,qKAAC;gFAAI,WAAU;0FAAyB,WAAW,KAAK;;;;;;;;;;;;;;;;;8EAG5D,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAK,WAAU;kFACb,mBAAmB,WAAW,WAAW;;;;;;;;;;;8EAG9C,qKAAC;oEAAG,WAAU;8EACX,eAAe,WAAW,MAAM;;;;;;8EAEnC,qKAAC;oEAAG,WAAU;8EACX,iBAAiB,WAAW,QAAQ;;;;;;8EAEvC,qKAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,WAAW,WAAW,EAAE,kBAAkB,CAAC;;;;;;8EAEvD,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,WAAW,EAAE,EAAE;gFAC9D,WAAU;gFACV,OAAM;0FAEN,cAAA,qKAAC,sMAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;;;;;;0FAErB,qKAAC;gFACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC;gFACnE,WAAU;gFACV,OAAM;0FAEN,cAAA,qKAAC,4MAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;;;;;;0FAExB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFACC,OAAO,WAAW,MAAM;oFACxB,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oFACjE,WAAU;;sGAEV,qKAAC;4FAAO,OAAM;sGAAU;;;;;;sGACxB,qKAAC;4FAAO,OAAM;sGAAY;;;;;;sGAC1B,qKAAC;4FAAO,OAAM;sGAAc;;;;;;sGAC5B,qKAAC;4FAAO,OAAM;sGAAY;;;;;;sGAC1B,qKAAC;4FAAO,OAAM;sGAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2DA9DxB,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;kDA0E9B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDACC,SAAS,IAAM,cAAc;gEAAE,GAAG,UAAU;gEAAE,MAAM,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,GAAG;4DAAG;wDACrF,UAAU,WAAW,IAAI,KAAK;wDAC9B,WAAU;kEACX;;;;;;kEAGD,qKAAC;wDACC,SAAS,IAAM,cAAc;gEAAE,GAAG,UAAU;gEAAE,MAAM,KAAK,GAAG,CAAC,YAAY,WAAW,IAAI,GAAG;4DAAG;wDAC9F,UAAU,WAAW,IAAI,KAAK;wDAC9B,WAAU;kEACX;;;;;;;;;;;;0DAIH,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;kEACC,cAAA,qKAAC;4DAAE,WAAU;;gEAAwB;8EAC/B,qKAAC;oEAAK,WAAU;8EAAe,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAG;;;;;;gEAAS;gEAAG;8EAC1F,qKAAC;oEAAK,WAAU;8EACb,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,YAAY,MAAM;;;;;;gEAC1D;gEAAI;;;;;;;;;;;;kEAIhB,qKAAC;kEACC,cAAA,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEACC,SAAS,IAAM,cAAc;4EAAE,GAAG,UAAU;4EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,GAAG;wEAAG;oEACrF,UAAU,WAAW,IAAI,KAAK;oEAC9B,WAAU;8EACX;;;;;;gEAGA,MAAM,IAAI,CAAC;oEAAE,QAAQ;gEAAW,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,qBACxD,qKAAC;wEAEC,SAAS,IAAM,cAAc;gFAAE,GAAG,UAAU;gFAAE;4EAAK;wEACnD,WAAW,CAAC,uEAAuE,EACjF,SAAS,WAAW,IAAI,GACpB,kDACA,2DACJ;kFAED;uEARI;;;;;8EAWT,qKAAC;oEACC,SAAS,IAAM,cAAc;4EAAE,GAAG,UAAU;4EAAE,MAAM,KAAK,GAAG,CAAC,YAAY,WAAW,IAAI,GAAG;wEAAG;oEAC9F,UAAU,WAAW,IAAI,KAAK;oEAC9B,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}]}