{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/index.tsx"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useRouter } from 'next/router';\n\nexport default function AdminIndex() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // 重定向到管理后台登录页面\n    router.replace('/admin/login');\n  }, [router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n        <p className=\"mt-4 text-gray-600\">正在跳转到管理后台...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QACf,OAAO,OAAO,CAAC;IACjB,GAAG;QAAC;KAAO;IAEX,qBACE,qKAAC;QAAI,WAAU;kBACb,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;;;;;8BACf,qKAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}]}