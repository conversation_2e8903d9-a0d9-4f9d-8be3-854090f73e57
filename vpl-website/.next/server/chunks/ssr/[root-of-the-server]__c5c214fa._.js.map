{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/users.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  UserPlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  ShieldCheckIcon,\n  UserIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\n\ninterface AdminUser {\n  id: string;\n  username: string;\n  email: string;\n  fullName: string;\n  role: 'super_admin' | 'admin' | 'manager' | 'viewer';\n  isActive: boolean;\n  lastLoginAt?: string;\n  createdAt: string;\n  createdBy: string;\n}\n\nexport default function UsersPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [users, setUsers] = useState<AdminUser[]>([]);\n  const [showForm, setShowForm] = useState(false);\n  const [editingUser, setEditingUser] = useState<AdminUser | null>(null);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    fullName: '',\n    role: 'viewer' as Admin<PERSON><PERSON>['role'],\n    password: '',\n    confirmPassword: '',\n    isActive: true,\n  });\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchUsers();\n    }\n  }, [isAuthenticated]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/users', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setUsers(data.data || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (formData.password !== formData.confirmPassword) {\n      alert('密码确认不匹配');\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const url = editingUser \n        ? `/api/admin/users/${editingUser.id}`\n        : '/api/admin/users';\n      const method = editingUser ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n\n      if (response.ok) {\n        setShowForm(false);\n        setEditingUser(null);\n        resetForm();\n        fetchUsers();\n      }\n    } catch (error) {\n      console.error('Failed to save user:', error);\n    }\n  };\n\n  const handleEdit = (user: AdminUser) => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      email: user.email,\n      fullName: user.fullName,\n      role: user.role,\n      password: '',\n      confirmPassword: '',\n      isActive: user.isActive,\n    });\n    setShowForm(true);\n  };\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('确定要删除这个用户吗？此操作不可撤销。')) return;\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/users/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        fetchUsers();\n      }\n    } catch (error) {\n      console.error('Failed to delete user:', error);\n    }\n  };\n\n  const handleToggleStatus = async (id: string, isActive: boolean) => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/users/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ isActive })\n      });\n\n      if (response.ok) {\n        fetchUsers();\n      }\n    } catch (error) {\n      console.error('Failed to update user status:', error);\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      username: '',\n      email: '',\n      fullName: '',\n      role: 'viewer',\n      password: '',\n      confirmPassword: '',\n      isActive: true,\n    });\n  };\n\n  const getRoleName = (role: string) => {\n    const roles: { [key: string]: string } = {\n      'super_admin': '超级管理员',\n      'admin': '管理员',\n      'manager': '经理',\n      'viewer': '查看者',\n    };\n    return roles[role] || role;\n  };\n\n  const getRoleBadge = (role: string) => {\n    const roleConfig = {\n      'super_admin': { color: 'bg-red-100 text-red-800', text: '超级管理员' },\n      'admin': { color: 'bg-blue-100 text-blue-800', text: '管理员' },\n      'manager': { color: 'bg-purple-100 text-purple-800', text: '经理' },\n      'viewer': { color: 'bg-gray-100 text-gray-800', text: '查看者' },\n    };\n    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.viewer;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>用户管理 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">用户管理</h1>\n                <p className=\"text-gray-600\">管理管理员账户和权限</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => {\n                    resetForm();\n                    setEditingUser(null);\n                    setShowForm(true);\n                  }}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  <UserPlusIcon className=\"h-4 w-4 mr-2\" />\n                  添加用户\n                </button>\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Users Table */}\n          <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      用户信息\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      角色\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      最后登录\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      创建时间\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      操作\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {users.map((user) => (\n                    <tr key={user.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0 h-10 w-10\">\n                            <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                              <UserIcon className=\"h-6 w-6 text-gray-600\" />\n                            </div>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{user.fullName}</div>\n                            <div className=\"text-sm text-gray-500\">{user.username}</div>\n                            <div className=\"text-sm text-gray-500\">{user.email}</div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {getRoleBadge(user.role)}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          {user.isActive ? (\n                            <CheckCircleIcon className=\"h-5 w-5 text-green-500 mr-2\" />\n                          ) : (\n                            <XCircleIcon className=\"h-5 w-5 text-red-500 mr-2\" />\n                          )}\n                          <span className={`text-sm ${user.isActive ? 'text-green-800' : 'text-red-800'}`}>\n                            {user.isActive ? '活跃' : '禁用'}\n                          </span>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\n                        {user.lastLoginAt \n                          ? new Date(user.lastLoginAt).toLocaleString('zh-CN')\n                          : '从未登录'\n                        }\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\n                        {new Date(user.createdAt).toLocaleDateString('zh-CN')}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-2\">\n                          <button\n                            onClick={() => handleEdit(user)}\n                            className=\"text-blue-600 hover:text-blue-900\"\n                            title=\"编辑\"\n                          >\n                            <PencilIcon className=\"h-4 w-4\" />\n                          </button>\n                          <button\n                            onClick={() => handleToggleStatus(user.id, !user.isActive)}\n                            className={`${user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}\n                            title={user.isActive ? '禁用' : '启用'}\n                          >\n                            {user.isActive ? <XCircleIcon className=\"h-4 w-4\" /> : <CheckCircleIcon className=\"h-4 w-4\" />}\n                          </button>\n                          <button\n                            onClick={() => handleDelete(user.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                            title=\"删除\"\n                          >\n                            <TrashIcon className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* User Form Modal */}\n          {showForm && (\n            <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n              <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n                <div className=\"mt-3\">\n                  <div className=\"flex justify-between items-center mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">\n                      {editingUser ? '编辑用户' : '添加用户'}\n                    </h3>\n                    <button\n                      onClick={() => {\n                        setShowForm(false);\n                        setEditingUser(null);\n                        resetForm();\n                      }}\n                      className=\"text-gray-400 hover:text-gray-600\"\n                    >\n                      ×\n                    </button>\n                  </div>\n                  \n                  <form onSubmit={handleSubmit} className=\"space-y-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          用户名\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.username}\n                          onChange={(e) => setFormData({ ...formData, username: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          邮箱\n                        </label>\n                        <input\n                          type=\"email\"\n                          required\n                          value={formData.email}\n                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div className=\"md:col-span-2\">\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          姓名\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.fullName}\n                          onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          角色\n                        </label>\n                        <select\n                          value={formData.role}\n                          onChange={(e) => setFormData({ ...formData, role: e.target.value as AdminUser['role'] })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        >\n                          <option value=\"viewer\">查看者</option>\n                          <option value=\"manager\">经理</option>\n                          <option value=\"admin\">管理员</option>\n                          <option value=\"super_admin\">超级管理员</option>\n                        </select>\n                      </div>\n                      \n                      <div>\n                        <label className=\"flex items-center\">\n                          <input\n                            type=\"checkbox\"\n                            checked={formData.isActive}\n                            onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                            className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                          />\n                          <span className=\"ml-2 text-sm text-gray-700\">启用账户</span>\n                        </label>\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          密码 {editingUser && '(留空保持不变)'}\n                        </label>\n                        <input\n                          type=\"password\"\n                          required={!editingUser}\n                          value={formData.password}\n                          onChange={(e) => setFormData({ ...formData, password: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          确认密码\n                        </label>\n                        <input\n                          type=\"password\"\n                          required={!editingUser}\n                          value={formData.confirmPassword}\n                          onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex justify-end space-x-3 pt-4\">\n                      <button\n                        type=\"button\"\n                        onClick={() => {\n                          setShowForm(false);\n                          setEditingUser(null);\n                          resetForm();\n                        }}\n                        className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                      >\n                        取消\n                      </button>\n                      <button\n                        type=\"submit\"\n                        className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700\"\n                      >\n                        {editingUser ? '更新' : '创建'}\n                      </button>\n                    </div>\n                  </form>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAoB;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,UAAU;IACZ;IACA,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,IAAI,IAAI,EAAE;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,MAAM,cACR,CAAC,iBAAiB,EAAE,YAAY,EAAE,EAAE,GACpC;YACJ,MAAM,SAAS,cAAc,QAAQ;YAErC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY;gBACZ,eAAe;gBACf;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,YAAY;YACV,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI;YACf,UAAU;YACV,iBAAiB;YACjB,UAAU,KAAK,QAAQ;QACzB;QACA,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,wBAAwB;QAErC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,IAAI,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,qBAAqB,OAAO,IAAY;QAC5C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,IAAI,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;YACV,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,UAAU;YACV,iBAAiB;YACjB,UAAU;QACZ;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,eAAe;YACf,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,KAAK,IAAI;IACxB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa;YACjB,eAAe;gBAAE,OAAO;gBAA2B,MAAM;YAAQ;YACjE,SAAS;gBAAE,OAAO;gBAA6B,MAAM;YAAM;YAC3D,WAAW;gBAAE,OAAO;gBAAiC,MAAM;YAAK;YAChE,UAAU;gBAAE,OAAO;gBAA6B,MAAM;YAAM;QAC9D;QACA,MAAM,SAAS,UAAU,CAAC,KAAgC,IAAI,WAAW,MAAM;QAC/E,qBACE,qKAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,KAAK,EAAE;sBACvG,OAAO,IAAI;;;;;;IAGlB;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS;oDACP;oDACA,eAAe;oDACf,YAAY;gDACd;gDACA,WAAU;;kEAEV,qKAAC,gNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG3C,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAM,WAAU;;0DACf,qKAAC;gDAAM,WAAU;0DACf,cAAA,qKAAC;;sEACC,qKAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,qKAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,qKAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,qKAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,qKAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,qKAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAKnG,qKAAC;gDAAM,WAAU;0DACd,MAAM,GAAG,CAAC,CAAC,qBACV,qKAAC;wDAAiB,WAAU;;0EAC1B,qKAAC;gEAAG,WAAU;0EACZ,cAAA,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAI,WAAU;sFACb,cAAA,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC,wMAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAGxB,qKAAC;4EAAI,WAAU;;8FACb,qKAAC;oFAAI,WAAU;8FAAqC,KAAK,QAAQ;;;;;;8FACjE,qKAAC;oFAAI,WAAU;8FAAyB,KAAK,QAAQ;;;;;;8FACrD,qKAAC;oFAAI,WAAU;8FAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0EAIxD,qKAAC;gEAAG,WAAU;0EACX,aAAa,KAAK,IAAI;;;;;;0EAEzB,qKAAC;gEAAG,WAAU;0EACZ,cAAA,qKAAC;oEAAI,WAAU;;wEACZ,KAAK,QAAQ,iBACZ,qKAAC,sNAAA,CAAA,kBAAe;4EAAC,WAAU;;;;;iGAE3B,qKAAC,8MAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFAEzB,qKAAC;4EAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,QAAQ,GAAG,mBAAmB,gBAAgB;sFAC5E,KAAK,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;0EAI9B,qKAAC;gEAAG,WAAU;0EACX,KAAK,WAAW,GACb,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc,CAAC,WAC1C;;;;;;0EAGN,qKAAC;gEAAG,WAAU;0EACX,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;0EAE/C,qKAAC;gEAAG,WAAU;0EACZ,cAAA,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EACC,SAAS,IAAM,WAAW;4EAC1B,WAAU;4EACV,OAAM;sFAEN,cAAA,qKAAC,4MAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;;;;;;sFAExB,qKAAC;4EACC,SAAS,IAAM,mBAAmB,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ;4EACzD,WAAW,GAAG,KAAK,QAAQ,GAAG,oCAAoC,uCAAuC;4EACzG,OAAO,KAAK,QAAQ,GAAG,OAAO;sFAE7B,KAAK,QAAQ,iBAAG,qKAAC,8MAAA,CAAA,cAAW;gFAAC,WAAU;;;;;qGAAe,qKAAC,sNAAA,CAAA,kBAAe;gFAAC,WAAU;;;;;;;;;;;sFAEpF,qKAAC;4EACC,SAAS,IAAM,aAAa,KAAK,EAAE;4EACnC,WAAU;4EACV,OAAM;sFAEN,cAAA,qKAAC,0MAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uDA5DpB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;4BAwEzB,0BACC,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAG,WAAU;kEACX,cAAc,SAAS;;;;;;kEAE1B,qKAAC;wDACC,SAAS;4DACP,YAAY;4DACZ,eAAe;4DACf;wDACF;wDACA,WAAU;kEACX;;;;;;;;;;;;0DAKH,qKAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,KAAK;wEACrB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAClE,WAAU;;;;;;;;;;;;0EAId,qKAAC;gEAAI,WAAU;;kFACb,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4EAAsB;wEACtF,WAAU;;0FAEV,qKAAC;gFAAO,OAAM;0FAAS;;;;;;0FACvB,qKAAC;gFAAO,OAAM;0FAAU;;;;;;0FACxB,qKAAC;gFAAO,OAAM;0FAAQ;;;;;;0FACtB,qKAAC;gFAAO,OAAM;0FAAc;;;;;;;;;;;;;;;;;;0EAIhC,qKAAC;0EACC,cAAA,qKAAC;oEAAM,WAAU;;sFACf,qKAAC;4EACC,MAAK;4EACL,SAAS,SAAS,QAAQ;4EAC1B,UAAU,CAAC,IAAM,YAAY;oFAAE,GAAG,QAAQ;oFAAE,UAAU,EAAE,MAAM,CAAC,OAAO;gFAAC;4EACvE,WAAU;;;;;;sFAEZ,qKAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;;;;;;0EAIjD,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;;4EAA+C;4EAC1D,eAAe;;;;;;;kFAErB,qKAAC;wEACC,MAAK;wEACL,UAAU,CAAC;wEACX,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,qKAAC;;kFACC,qKAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,qKAAC;wEACC,MAAK;wEACL,UAAU,CAAC;wEACX,OAAO,SAAS,eAAe;wEAC/B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC5E,WAAU;;;;;;;;;;;;;;;;;;kEAKhB,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEACC,MAAK;gEACL,SAAS;oEACP,YAAY;oEACZ,eAAe;oEACf;gEACF;gEACA,WAAU;0EACX;;;;;;0EAGD,qKAAC;gEACC,MAAK;gEACL,WAAU;0EAET,cAAc,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9C", "debugId": null}}]}