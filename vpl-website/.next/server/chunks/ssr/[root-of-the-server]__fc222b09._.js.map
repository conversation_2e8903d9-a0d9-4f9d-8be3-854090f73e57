{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/analytics.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\nimport {\n  ChartBarIcon,\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon,\n  UsersIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  ArrowDownTrayIcon\n} from '@heroicons/react/24/outline';\n\ninterface AnalyticsData {\n  totalSubmissions: number;\n  todaySubmissions: number;\n  weeklySubmissions: number;\n  monthlySubmissions: number;\n  statusDistribution: Array<{\n    status: string;\n    count: number;\n    percentage: number;\n  }>;\n  serviceTypeDistribution: Array<{\n    type: string;\n    count: number;\n    percentage: number;\n  }>;\n  submissionTrends: Array<{\n    date: string;\n    count: number;\n  }>;\n}\n\nexport default function AnalyticsPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);\n  const [dateRange, setDateRange] = useState('30');\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchAnalytics();\n    }\n  }, [isAuthenticated, dateRange]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchAnalytics = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/analytics', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setAnalytics(data.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch analytics:', error);\n    }\n  };\n\n  const exportData = async (format: 'csv' | 'excel') => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/export?format=${format}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `analytics-${new Date().toISOString().split('T')[0]}.${format}`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      }\n    } catch (error) {\n      console.error('Failed to export data:', error);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: { [key: string]: string } = {\n      pending: '#F59E0B',\n      contacted: '#3B82F6',\n      'in-progress': '#8B5CF6',\n      completed: '#10B981',\n      closed: '#6B7280',\n    };\n    return colors[status] || '#6B7280';\n  };\n\n  const getServiceTypeColor = (type: string) => {\n    const colors: { [key: string]: string } = {\n      foreign_trade_lines: '#EF4444',\n      ecommerce_lines: '#F97316',\n      vpn_services: '#8B5CF6',\n      custom_solution: '#06B6D4',\n    };\n    return colors[type] || '#6B7280';\n  };\n\n  const getServiceTypeName = (serviceType: string) => {\n    const types: { [key: string]: string } = {\n      'foreign_trade_lines': '外贸网络线路',\n      'ecommerce_lines': '跨境电商线路',\n      'vpn_services': 'VPN服务',\n      'custom_solution': '定制解决方案',\n    };\n    return types[serviceType] || serviceType;\n  };\n\n  const getStatusName = (status: string) => {\n    const statuses: { [key: string]: string } = {\n      pending: '待处理',\n      contacted: '已联系',\n      'in-progress': '处理中',\n      completed: '已完成',\n      closed: '已关闭',\n    };\n    return statuses[status] || status;\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated || !analytics) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>数据分析 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">数据分析</h1>\n                <p className=\"text-gray-600\">客户咨询数据统计与分析</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <select\n                  value={dateRange}\n                  onChange={(e) => setDateRange(e.target.value)}\n                  className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"7\">最近7天</option>\n                  <option value=\"30\">最近30天</option>\n                  <option value=\"90\">最近90天</option>\n                </select>\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Key Metrics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <UsersIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">总咨询数</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{analytics.totalSubmissions}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <ArrowTrendingUpIcon className=\"h-6 w-6 text-green-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">今日新增</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{analytics.todaySubmissions}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <ChartBarIcon className=\"h-6 w-6 text-blue-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">本周咨询</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{analytics.weeklySubmissions}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <ClockIcon className=\"h-6 w-6 text-purple-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">本月咨询</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{analytics.monthlySubmissions}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Charts */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n            {/* Submission Trends */}\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">咨询趋势</h3>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <AreaChart data={analytics.submissionTrends}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis \n                    dataKey=\"date\" \n                    tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}\n                  />\n                  <YAxis />\n                  <Tooltip \n                    labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}\n                    formatter={(value) => [value, '咨询数量']}\n                  />\n                  <Area \n                    type=\"monotone\" \n                    dataKey=\"count\" \n                    stroke=\"#3B82F6\" \n                    fill=\"#3B82F6\" \n                    fillOpacity={0.1}\n                  />\n                </AreaChart>\n              </ResponsiveContainer>\n            </div>\n\n            {/* Status Distribution */}\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">状态分布</h3>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={analytics.statusDistribution}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={false}\n                    label={({ status, percentage }) => `${getStatusName(status)} ${percentage}%`}\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"count\"\n                  >\n                    {analytics.statusDistribution.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={getStatusColor(entry.status)} />\n                    ))}\n                  </Pie>\n                  <Tooltip formatter={(value, name) => [value, '数量']} />\n                </PieChart>\n              </ResponsiveContainer>\n            </div>\n          </div>\n\n          {/* Service Type Distribution */}\n          <div className=\"bg-white shadow rounded-lg p-6 mb-8\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">服务类型分布</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={analytics.serviceTypeDistribution}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis \n                  dataKey=\"type\" \n                  tickFormatter={(value) => getServiceTypeName(value)}\n                />\n                <YAxis />\n                <Tooltip \n                  labelFormatter={(value) => getServiceTypeName(value)}\n                  formatter={(value) => [value, '咨询数量']}\n                />\n                <Bar dataKey=\"count\" fill=\"#8B5CF6\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Detailed Statistics */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Status Details */}\n            <div className=\"bg-white shadow rounded-lg\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">状态详情</h3>\n              </div>\n              <div className=\"px-6 py-4\">\n                <div className=\"space-y-4\">\n                  {analytics.statusDistribution.map((item) => (\n                    <div key={item.status} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <div \n                          className=\"w-3 h-3 rounded-full mr-3\"\n                          style={{ backgroundColor: getStatusColor(item.status) }}\n                        ></div>\n                        <span className=\"text-sm text-gray-900\">{getStatusName(item.status)}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-sm font-medium text-gray-900\">{item.count}</span>\n                        <span className=\"text-sm text-gray-500\">({item.percentage}%)</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Service Type Details */}\n            <div className=\"bg-white shadow rounded-lg\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">服务类型详情</h3>\n              </div>\n              <div className=\"px-6 py-4\">\n                <div className=\"space-y-4\">\n                  {analytics.serviceTypeDistribution.map((item) => (\n                    <div key={item.type} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <div \n                          className=\"w-3 h-3 rounded-full mr-3\"\n                          style={{ backgroundColor: getServiceTypeColor(item.type) }}\n                        ></div>\n                        <span className=\"text-sm text-gray-900\">{getServiceTypeName(item.type)}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-sm font-medium text-gray-900\">{item.count}</span>\n                        <span className=\"text-sm text-gray-500\">({item.percentage}%)</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAAA;AAAA;AAAA;;;;;;;AAgCe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa,KAAK,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,QAAQ,EAAE;gBACjE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ;gBAC5E,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAoC;YACxC,SAAS;YACT,WAAW;YACX,eAAe;YACf,WAAW;YACX,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,SAAoC;YACxC,qBAAqB;YACrB,iBAAiB;YACjB,cAAc;YACd,iBAAiB;QACnB;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAmC;YACvC,uBAAuB;YACvB,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;QACrB;QACA,OAAO,KAAK,CAAC,YAAY,IAAI;IAC/B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,WAAsC;YAC1C,SAAS;YACT,WAAW;YACX,eAAe;YACf,WAAW;YACX,QAAQ;QACV;QACA,OAAO,QAAQ,CAAC,OAAO,IAAI;IAC7B;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,mBAAmB,CAAC,WAAW;QAClC,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAU;;kEAEV,qKAAC;wDAAO,OAAM;kEAAI;;;;;;kEAClB,qKAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,qKAAC;wDAAO,OAAM;kEAAK;;;;;;;;;;;;0DAErB,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,0MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,8NAAA,CAAA,sBAAmB;4DAAC,WAAU;;;;;;;;;;;kEAEjC,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOxF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,0MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3F,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,qKAAC,4JAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAO,QAAQ;0DACxC,cAAA,qKAAC,8IAAA,CAAA,YAAS;oDAAC,MAAM,UAAU,gBAAgB;;sEACzC,qKAAC,sJAAA,CAAA,gBAAa;4DAAC,iBAAgB;;;;;;sEAC/B,qKAAC,8IAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,eAAe,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB,CAAC,SAAS;oEAAE,OAAO;oEAAS,KAAK;gEAAU;;;;;;sEAEzG,qKAAC,8IAAA,CAAA,QAAK;;;;;sEACN,qKAAC,gJAAA,CAAA,UAAO;4DACN,gBAAgB,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB,CAAC;4DAC9D,WAAW,CAAC,QAAU;oEAAC;oEAAO;iEAAO;;;;;;sEAEvC,qKAAC,6IAAA,CAAA,OAAI;4DACH,MAAK;4DACL,SAAQ;4DACR,QAAO;4DACP,MAAK;4DACL,aAAa;;;;;;;;;;;;;;;;;;;;;;;kDAOrB,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,qKAAC,4JAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAO,QAAQ;0DACxC,cAAA,qKAAC,6IAAA,CAAA,WAAQ;;sEACP,qKAAC,wIAAA,CAAA,MAAG;4DACF,MAAM,UAAU,kBAAkB;4DAClC,IAAG;4DACH,IAAG;4DACH,WAAW;4DACX,OAAO,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,GAAK,GAAG,cAAc,QAAQ,CAAC,EAAE,WAAW,CAAC,CAAC;4DAC5E,aAAa;4DACb,MAAK;4DACL,SAAQ;sEAEP,UAAU,kBAAkB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACxC,qKAAC,6IAAA,CAAA,OAAI;oEAAuB,MAAM,eAAe,MAAM,MAAM;mEAAlD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sEAG9B,qKAAC,gJAAA,CAAA,UAAO;4DAAC,WAAW,CAAC,OAAO,OAAS;oEAAC;oEAAO;iEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1D,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,qKAAC,4JAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,qKAAC,6IAAA,CAAA,WAAQ;4CAAC,MAAM,UAAU,uBAAuB;;8DAC/C,qKAAC,sJAAA,CAAA,gBAAa;oDAAC,iBAAgB;;;;;;8DAC/B,qKAAC,8IAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,eAAe,CAAC,QAAU,mBAAmB;;;;;;8DAE/C,qKAAC,8IAAA,CAAA,QAAK;;;;;8DACN,qKAAC,gJAAA,CAAA,UAAO;oDACN,gBAAgB,CAAC,QAAU,mBAAmB;oDAC9C,WAAW,CAAC,QAAU;4DAAC;4DAAO;yDAAO;;;;;;8DAEvC,qKAAC,4IAAA,CAAA,MAAG;oDAAC,SAAQ;oDAAQ,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAMhC,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAI,WAAU;8DACZ,UAAU,kBAAkB,CAAC,GAAG,CAAC,CAAC,qBACjC,qKAAC;4DAAsB,WAAU;;8EAC/B,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,iBAAiB,eAAe,KAAK,MAAM;4EAAE;;;;;;sFAExD,qKAAC;4EAAK,WAAU;sFAAyB,cAAc,KAAK,MAAM;;;;;;;;;;;;8EAEpE,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAK,WAAU;sFAAqC,KAAK,KAAK;;;;;;sFAC/D,qKAAC;4EAAK,WAAU;;gFAAwB;gFAAE,KAAK,UAAU;gFAAC;;;;;;;;;;;;;;2DAVpD,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;kDAmB7B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAI,WAAU;8DACZ,UAAU,uBAAuB,CAAC,GAAG,CAAC,CAAC,qBACtC,qKAAC;4DAAoB,WAAU;;8EAC7B,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,iBAAiB,oBAAoB,KAAK,IAAI;4EAAE;;;;;;sFAE3D,qKAAC;4EAAK,WAAU;sFAAyB,mBAAmB,KAAK,IAAI;;;;;;;;;;;;8EAEvE,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAK,WAAU;sFAAqC,KAAK,KAAK;;;;;;sFAC/D,qKAAC;4EAAK,WAAU;;gFAAwB;gFAAE,KAAK,UAAU;gFAAC;;;;;;;;;;;;;;2DAVpD,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBvC", "debugId": null}}]}