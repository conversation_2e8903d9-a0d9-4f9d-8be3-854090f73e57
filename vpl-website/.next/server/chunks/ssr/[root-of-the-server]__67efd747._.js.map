{"version": 3, "sources": [], "sections": [{"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/login.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { EyeIcon, EyeSlashIcon, LockClosedIcon } from '@heroicons/react/24/outline';\n\nconst loginSchema = z.object({\n  username: z.string().min(1, 'Username is required'),\n  password: z.string().min(1, 'Password is required'),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nexport default function AdminLogin() {\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const router = useRouter();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n  });\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/admin/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      const result = await response.json();\n\n      if (response.ok) {\n        // Store token in localStorage\n        localStorage.setItem('adminToken', result.token);\n        router.push('/admin/dashboard');\n      } else {\n        setError(result.message || 'Login failed');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <Head>\n        <title>管理员登录 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div>\n            <div className=\"mx-auto h-16 w-16 flex items-center justify-center bg-blue-600 rounded-lg\">\n              <LockClosedIcon className=\"h-8 w-8 text-white\" />\n            </div>\n            <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n              管理员登录\n            </h2>\n            <p className=\"mt-2 text-center text-sm text-gray-600\">\n              VPL后台管理系统\n            </p>\n          </div>\n          \n          <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit(onSubmit)}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <p className=\"text-sm text-red-600\">{error}</p>\n              </div>\n            )}\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n                  用户名\n                </label>\n                <input\n                  {...register('username')}\n                  type=\"text\"\n                  autoComplete=\"username\"\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"请输入用户名\"\n                />\n                {errors.username && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.username.message}</p>\n                )}\n              </div>\n              \n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                  密码\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('password')}\n                    type={showPassword ? 'text' : 'password'}\n                    autoComplete=\"current-password\"\n                    className=\"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                    placeholder=\"请输入密码\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                    ) : (\n                      <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n                {errors.password && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    登录中...\n                  </div>\n                ) : (\n                  '登录'\n                )}\n              </button>\n            </div>\n          </form>\n          \n          <div className=\"text-center\">\n            <p className=\"text-xs text-gray-500\">\n              如果您忘记了登录凭据，请联系系统管理员\n            </p>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;AAEA,MAAM,cAAc,sGAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,UAAU,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,8BAA8B;gBAC9B,aAAa,OAAO,CAAC,cAAc,OAAO,KAAK;gBAC/C,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS,OAAO,OAAO,IAAI;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;;8CACC,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC,oNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;8CAE5B,qKAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,qKAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;sCAKxD,qKAAC;4BAAK,WAAU;4BAAiB,UAAU,aAAa;;gCACrD,uBACC,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;8CAIzC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;;8DACC,qKAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,qKAAC;oDACE,GAAG,SAAS,WAAW;oDACxB,MAAK;oDACL,cAAa;oDACb,WAAU;oDACV,aAAY;;;;;;gDAEb,OAAO,QAAQ,kBACd,qKAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;sDAIrE,qKAAC;;8DACC,qKAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DACE,GAAG,SAAS,WAAW;4DACxB,MAAM,eAAe,SAAS;4DAC9B,cAAa;4DACb,WAAU;4DACV,aAAY;;;;;;sEAEd,qKAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,gBAAgB,CAAC;sEAE/B,6BACC,qKAAC,gNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;qFAExB,qKAAC,sMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAIxB,OAAO,QAAQ,kBACd,qKAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAKvE,qKAAC;8CACC,cAAA,qKAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,0BACC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;;;;;gDAAuE;;;;;;mDAIxF;;;;;;;;;;;;;;;;;sCAMR,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}]}