{"version": 3, "sources": [], "sections": [{"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/lib/notifications.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nclass NotificationService {\n  private socket: Socket | null = null;\n  private isConnected = false;\n  private connectionAttempts = 0;\n  private maxRetries = 3;\n\n  connect() {\n    if (typeof window !== 'undefined' && !this.socket && this.connectionAttempts < this.maxRetries) {\n      try {\n        this.connectionAttempts++;\n        console.log(`Attempting to connect to Socket.IO server (attempt ${this.connectionAttempts})`);\n\n        this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {\n          path: '/api/socket',\n          transports: ['polling', 'websocket'],\n          timeout: 5000,\n          forceNew: true,\n        });\n\n        this.socket.on('connect', () => {\n          console.log('Connected to notification service');\n          this.isConnected = true;\n          this.connectionAttempts = 0; // Reset on successful connection\n        });\n\n        this.socket.on('disconnect', () => {\n          console.log('Disconnected from notification service');\n          this.isConnected = false;\n        });\n\n        this.socket.on('connect_error', (error) => {\n          console.warn('Socket.IO connection error:', error.message);\n          this.isConnected = false;\n\n          if (this.connectionAttempts >= this.maxRetries) {\n            console.warn('Max connection attempts reached. Socket.IO features will be disabled.');\n          }\n        });\n\n        this.socket.on('admin-joined', (data) => {\n          console.log('Successfully joined admin room:', data);\n        });\n\n      } catch (error) {\n        console.error('Failed to initialize Socket.IO:', error);\n        this.isConnected = false;\n      }\n    }\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n    }\n  }\n\n  joinAdminRoom() {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('join-admin');\n    } else {\n      console.warn('Cannot join admin room: Socket.IO not connected');\n    }\n  }\n\n  onNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.on('new-submission', callback);\n    } else {\n      console.warn('Cannot listen for new submissions: Socket.IO not available');\n    }\n  }\n\n  offNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.off('new-submission', callback);\n    }\n  }\n\n  emitNewSubmission(data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('new-submission', data);\n    } else {\n      console.warn('Cannot emit new submission: Socket.IO not connected');\n    }\n  }\n\n  // Admin notification methods\n  notifyAdmins(type: string, data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.to('admin').emit('admin-notification', {\n        type,\n        data,\n        timestamp: new Date().toISOString(),\n      });\n    } else {\n      console.warn('Cannot notify admins: Socket.IO not connected');\n    }\n  }\n\n  onAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.on('admin-notification', callback);\n    } else {\n      console.warn('Cannot listen for admin notifications: Socket.IO not available');\n    }\n  }\n\n  offAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.off('admin-notification', callback);\n    }\n  }\n\n  // Check if service is available\n  isAvailable(): boolean {\n    return this.socket !== null && this.isConnected;\n  }\n\n  // Get connection status\n  getStatus(): string {\n    if (!this.socket) return 'not-initialized';\n    if (this.isConnected) return 'connected';\n    return 'disconnected';\n  }\n}\n\nexport const notificationService = new NotificationService();\n\n// Browser notification utilities\nexport const requestNotificationPermission = async (): Promise<boolean> => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return false;\n  }\n\n  if (Notification.permission === 'granted') {\n    return true;\n  }\n\n  if (Notification.permission === 'denied') {\n    return false;\n  }\n\n  const permission = await Notification.requestPermission();\n  return permission === 'granted';\n};\n\nexport const showBrowserNotification = (title: string, options?: NotificationOptions) => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return;\n  }\n\n  if (Notification.permission === 'granted') {\n    new Notification(title, {\n      icon: '/favicon.ico',\n      badge: '/favicon.ico',\n      ...options,\n    });\n  }\n};\n\nexport default NotificationService;\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;AAEA,MAAM;IACI,SAAwB,KAAK;IAC7B,cAAc,MAAM;IACpB,qBAAqB,EAAE;IACvB,aAAa,EAAE;IAEvB,UAAU;QACR,IAAI,gBAAkB,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU;;IAyChG;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,gBAAgB;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,gBAAgB,QAA6B,EAAE;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB;QACnC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,iBAAiB,QAA6B,EAAE;QAC9C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB;QACpC;IACF;IAEA,kBAAkB,IAAS,EAAE;QAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;QACrC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,6BAA6B;IAC7B,aAAa,IAAY,EAAE,IAAS,EAAE;QACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,sBAAsB;gBACjD;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,oBAAoB,QAAqC,EAAE;QACzD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,sBAAsB;QACvC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,qBAAqB,QAAqC,EAAE;QAC1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB;QACxC;IACF;IAEA,gCAAgC;IAChC,cAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,WAAW;IACjD;IAEA,wBAAwB;IACxB,YAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO;QAC7B,OAAO;IACT;AACF;AAEO,MAAM,sBAAsB,IAAI;AAGhC,MAAM,gCAAgC;IAC3C,IAAI,gBAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE,OAAO;IACT;;;IAUA,MAAM;AAER;AAEO,MAAM,0BAA0B,CAAC,OAAe;IACrD,IAAI,gBAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE;IACF;;;AASF;uCAEe", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/NotificationCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { BellIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { notificationService, showBrowserNotification, requestNotificationPermission } from '../../lib/notifications';\n\ninterface Notification {\n  id: string;\n  type: 'new-submission' | 'system' | 'warning';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n\nexport default function NotificationCenter() {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [connectionStatus, setConnectionStatus] = useState<string>('not-initialized');\n\n  useEffect(() => {\n    // Initialize notification service with error handling\n    try {\n      notificationService.connect();\n\n      // Check connection status periodically\n      const statusInterval = setInterval(() => {\n        setConnectionStatus(notificationService.getStatus());\n      }, 2000);\n\n      // Try to join admin room after a short delay\n      setTimeout(() => {\n        notificationService.joinAdminRoom();\n      }, 1000);\n\n      // Request browser notification permission\n      requestNotificationPermission();\n\n      return () => {\n        clearInterval(statusInterval);\n      };\n    } catch (error) {\n      console.error('Failed to initialize notification service:', error);\n      setConnectionStatus('error');\n    }\n\n    // Listen for new submissions\n    const handleNewSubmission = (data: any) => {\n      const notification: Notification = {\n        id: Date.now().toString(),\n        type: 'new-submission',\n        title: '新的客户咨询',\n        message: `${data.companyName} 提交了新的咨询`,\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n\n      setNotifications(prev => [notification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(notification.title, {\n        body: notification.message,\n        tag: 'new-submission',\n      });\n    };\n\n    // Listen for admin notifications\n    const handleAdminNotification = (notification: any) => {\n      const newNotification: Notification = {\n        id: Date.now().toString(),\n        type: notification.type,\n        title: notification.title || '系统通知',\n        message: notification.message,\n        timestamp: notification.timestamp,\n        read: false,\n      };\n\n      setNotifications(prev => [newNotification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(newNotification.title, {\n        body: newNotification.message,\n        tag: notification.type,\n      });\n    };\n\n    notificationService.onNewSubmission(handleNewSubmission);\n    notificationService.onAdminNotification(handleAdminNotification);\n\n    return () => {\n      notificationService.offNewSubmission(handleNewSubmission);\n      notificationService.offAdminNotification(handleAdminNotification);\n      notificationService.disconnect();\n    };\n  }, []);\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id\n          ? { ...notification, read: true }\n          : notification\n      )\n    );\n    setUnreadCount(prev => Math.max(0, prev - 1));\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n    setUnreadCount(0);\n  };\n\n  const removeNotification = (id: string) => {\n    const notification = notifications.find(n => n.id === id);\n    if (notification && !notification.read) {\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    }\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'new-submission':\n        return '📧';\n      case 'system':\n        return '⚙️';\n      case 'warning':\n        return '⚠️';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) {\n      return '刚刚';\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes}分钟前`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)}小时前`;\n    } else {\n      return date.toLocaleDateString('zh-CN');\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\"\n      >\n        <BellIcon className=\"h-6 w-6\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n        {/* Connection status indicator */}\n        <span className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full ${\n          connectionStatus === 'connected' ? 'bg-green-500' :\n          connectionStatus === 'disconnected' ? 'bg-yellow-500' :\n          'bg-gray-400'\n        }`} title={`Socket.IO: ${connectionStatus}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 max-h-96 overflow-hidden\">\n            <div className=\"p-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-medium text-gray-900\">通知</h3>\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-500\"\n                  >\n                    全部标记为已读\n                  </button>\n                )}\n              </div>\n              {/* Connection status */}\n              <div className=\"mt-2 text-xs text-gray-500\">\n                实时通知: {\n                  connectionStatus === 'connected' ? '🟢 已连接' :\n                  connectionStatus === 'disconnected' ? '🟡 连接中断' :\n                  connectionStatus === 'error' ? '🔴 连接错误' :\n                  '⚪ 未连接'\n                }\n              </div>\n            </div>\n            \n            <div className=\"max-h-64 overflow-y-auto\">\n              {notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  暂无通知\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${\n                      !notification.read ? 'bg-blue-50' : ''\n                    }`}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-start space-x-3 flex-1\">\n                        <span className=\"text-lg\">\n                          {getNotificationIcon(notification.type)}\n                        </span>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {notification.title}\n                          </p>\n                          <p className=\"text-sm text-gray-600 mt-1\">\n                            {notification.message}\n                          </p>\n                          <p className=\"text-xs text-gray-400 mt-1\">\n                            {formatTimestamp(notification.timestamp)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {!notification.read && (\n                          <button\n                            onClick={() => markAsRead(notification.id)}\n                            className=\"w-2 h-2 bg-blue-500 rounded-full\"\n                            title=\"标记为已读\"\n                          />\n                        )}\n                        <button\n                          onClick={() => removeNotification(notification.id)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;;;AAJA;;;;;AAee,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,sDAAsD;QACtD,IAAI;YACF,oHAAA,CAAA,sBAAmB,CAAC,OAAO;YAE3B,uCAAuC;YACvC,MAAM,iBAAiB,YAAY;gBACjC,oBAAoB,oHAAA,CAAA,sBAAmB,CAAC,SAAS;YACnD,GAAG;YAEH,6CAA6C;YAC7C,WAAW;gBACT,oHAAA,CAAA,sBAAmB,CAAC,aAAa;YACnC,GAAG;YAEH,0CAA0C;YAC1C,CAAA,GAAA,oHAAA,CAAA,gCAA6B,AAAD;YAE5B,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,oBAAoB;QACtB;QAEA,6BAA6B;QAC7B,MAAM,sBAAsB,CAAC;YAC3B,MAAM,eAA6B;gBACjC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,OAAO;gBACP,SAAS,GAAG,KAAK,WAAW,CAAC,QAAQ,CAAC;gBACtC,WAAW,IAAI,OAAO,WAAW;gBACjC,MAAM;YACR;YAEA,iBAAiB,CAAA,OAAQ;oBAAC;uBAAiB;iBAAK;YAChD,eAAe,CAAA,OAAQ,OAAO;YAE9B,4BAA4B;YAC5B,CAAA,GAAA,oHAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa,KAAK,EAAE;gBAC1C,MAAM,aAAa,OAAO;gBAC1B,KAAK;YACP;QACF;QAEA,iCAAiC;QACjC,MAAM,0BAA0B,CAAC;YAC/B,MAAM,kBAAgC;gBACpC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,aAAa,IAAI;gBACvB,OAAO,aAAa,KAAK,IAAI;gBAC7B,SAAS,aAAa,OAAO;gBAC7B,WAAW,aAAa,SAAS;gBACjC,MAAM;YACR;YAEA,iBAAiB,CAAA,OAAQ;oBAAC;uBAAoB;iBAAK;YACnD,eAAe,CAAA,OAAQ,OAAO;YAE9B,4BAA4B;YAC5B,CAAA,GAAA,oHAAA,CAAA,0BAAuB,AAAD,EAAE,gBAAgB,KAAK,EAAE;gBAC7C,MAAM,gBAAgB,OAAO;gBAC7B,KAAK,aAAa,IAAI;YACxB;QACF;QAEA,oHAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QACpC,oHAAA,CAAA,sBAAmB,CAAC,mBAAmB,CAAC;QAExC,OAAO;YACL,oHAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC;YACrC,oHAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;YACzC,oHAAA,CAAA,sBAAmB,CAAC,UAAU;QAChC;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAC9B;QAGR,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IAC5C;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;QAE3D,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,gBAAgB,CAAC,aAAa,IAAI,EAAE;YACtC,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C;QACA,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,GAAG,cAAc,GAAG,CAAC;QAC9B,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;QAC/C,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,qKAAC,wMAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBACnB,cAAc,mBACb,qKAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;kCAIhC,qKAAC;wBAAK,WAAW,CAAC,iDAAiD,EACjE,qBAAqB,cAAc,iBACnC,qBAAqB,iBAAiB,kBACtC,eACA;wBAAE,OAAO,CAAC,WAAW,EAAE,kBAAkB;;;;;;;;;;;;YAG5C,wBACC;;kCACE,qKAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,cAAc,mBACb,qKAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAML,qKAAC;wCAAI,WAAU;;4CAA6B;4CAExC,qBAAqB,cAAc,WACnC,qBAAqB,iBAAiB,YACtC,qBAAqB,UAAU,YAC/B;;;;;;;;;;;;;0CAKN,qKAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,qKAAC;oCAAI,WAAU;8CAAgC;;;;;2CAI/C,cAAc,GAAG,CAAC,CAAC,6BACjB,qKAAC;wCAEC,WAAW,CAAC,8CAA8C,EACxD,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;kDAEF,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAK,WAAU;sEACb,oBAAoB,aAAa,IAAI;;;;;;sEAExC,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEAAE,WAAU;8EACV,aAAa,KAAK;;;;;;8EAErB,qKAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,qKAAC;oEAAE,WAAU;8EACV,gBAAgB,aAAa,SAAS;;;;;;;;;;;;;;;;;;8DAI7C,qKAAC;oDAAI,WAAU;;wDACZ,CAAC,aAAa,IAAI,kBACjB,qKAAC;4DACC,SAAS,IAAM,WAAW,aAAa,EAAE;4DACzC,WAAU;4DACV,OAAM;;;;;;sEAGV,qKAAC;4DACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,WAAU;sEAEV,cAAA,qKAAC,0MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAlCtB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AA+CxC", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/dashboard.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  ChartBarIcon,\n  EnvelopeIcon,\n  UsersIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  BellIcon\n} from '@heroicons/react/24/outline';\nimport NotificationCenter from '../../components/admin/NotificationCenter';\n\ninterface DashboardStats {\n  totalInquiries: number;\n  todayInquiries: number;\n  pendingInquiries: number;\n  totalUsers: number;\n}\n\ninterface ContactSubmission {\n  id: string;\n  companyName: string;\n  contactPerson: string;\n  email: string;\n  phone: string;\n  serviceType: string;\n  message: string;\n  submittedAt: string;\n  status: 'pending' | 'contacted' | 'closed';\n}\n\nexport default function AdminDashboard() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [stats, setStats] = useState<DashboardStats>({\n    totalInquiries: 0,\n    todayInquiries: 0,\n    pendingInquiries: 0,\n    totalUsers: 0,\n  });\n  const [recentSubmissions, setRecentSubmissions] = useState<ContactSubmission[]>([]);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n    if (isAuthenticated) {\n      fetchDashboardData();\n    }\n  }, [isAuthenticated]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    // Verify token with backend\n    fetch('/api/admin/verify', {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    })\n    .then(response => {\n      if (response.ok) {\n        setIsAuthenticated(true);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setStats(data.stats);\n        setRecentSubmissions(data.recentSubmissions);\n      }\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const getServiceTypeName = (serviceType: string) => {\n    const types: { [key: string]: string } = {\n      'foreign_trade_lines': '外贸网络线路',\n      'ecommerce_lines': '跨境电商外网线路',\n      'vpn_services': 'VPN服务',\n      'custom_solution': '定制解决方案',\n    };\n    return types[serviceType] || serviceType;\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },\n      contacted: { color: 'bg-blue-100 text-blue-800', text: '已联系' },\n      closed: { color: 'bg-green-100 text-green-800', text: '已完成' },\n    };\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>管理后台 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3\">\n                  <span className=\"text-white font-bold text-lg\">VPL</span>\n                </div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">管理后台</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <NotificationCenter />\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-700 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-1\" />\n                  退出登录\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content */}\n        <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <EnvelopeIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">总咨询数</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.totalInquiries}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <ChartBarIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">今日咨询</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.todayInquiries}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <BellIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">待处理</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.pendingInquiries}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <UsersIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">总用户数</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.totalUsers}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Recent Submissions */}\n          <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n            <div className=\"px-4 py-5 sm:px-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900\">最近的咨询</h3>\n              <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">最新提交的客户咨询信息</p>\n            </div>\n            <ul className=\"divide-y divide-gray-200\">\n              {recentSubmissions.map((submission) => (\n                <li key={submission.id}>\n                  <div className=\"px-4 py-4 sm:px-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-blue-600 font-medium text-sm\">\n                              {submission.companyName.charAt(0)}\n                            </span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"flex items-center\">\n                            <p className=\"text-sm font-medium text-gray-900\">\n                              {submission.companyName}\n                            </p>\n                            <span className=\"ml-2\">\n                              {getStatusBadge(submission.status)}\n                            </span>\n                          </div>\n                          <p className=\"text-sm text-gray-500\">\n                            {submission.contactPerson} • {submission.email}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {getServiceTypeName(submission.serviceType)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <p className=\"text-sm text-gray-500\">\n                          {new Date(submission.submittedAt).toLocaleDateString('zh-CN')}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-600 line-clamp-2\">\n                        {submission.message}\n                      </p>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n            {recentSubmissions.length === 0 && (\n              <div className=\"px-4 py-8 text-center\">\n                <p className=\"text-gray-500\">暂无咨询记录</p>\n              </div>\n            )}\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"mt-8 grid grid-cols-1 gap-5 sm:grid-cols-3\">\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <EnvelopeIcon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"ml-5\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">管理咨询</h3>\n                    <p className=\"text-sm text-gray-500\">查看和处理客户咨询</p>\n                  </div>\n                </div>\n                <div className=\"mt-3\">\n                  <button className=\"text-blue-600 hover:text-blue-500 text-sm font-medium\">\n                    查看全部 →\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <CogIcon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"ml-5\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">系统设置</h3>\n                    <p className=\"text-sm text-gray-500\">配置邮件和系统参数</p>\n                  </div>\n                </div>\n                <div className=\"mt-3\">\n                  <button className=\"text-blue-600 hover:text-blue-500 text-sm font-medium\">\n                    进入设置 →\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <ChartBarIcon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"ml-5\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">数据报告</h3>\n                    <p className=\"text-sm text-gray-500\">查看业务数据和统计</p>\n                  </div>\n                </div>\n                <div className=\"mt-3\">\n                  <button className=\"text-blue-600 hover:text-blue-500 text-sm font-medium\">\n                    查看报告 →\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"mt-8 bg-white shadow rounded-lg p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">快速操作</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <button\n                onClick={() => router.push('/admin/inquiries')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">客户咨询管理</div>\n                <div className=\"text-sm text-gray-500\">查看和管理所有客户咨询</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/analytics')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">数据分析</div>\n                <div className=\"text-sm text-gray-500\">查看统计数据和趋势</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/email-settings')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">邮件配置</div>\n                <div className=\"text-sm text-gray-500\">配置SMTP服务器设置</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/users')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">用户管理</div>\n                <div className=\"text-sm text-gray-500\">管理管理员账户</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/settings')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">系统设置</div>\n                <div className=\"text-sm text-gray-500\">网站和系统配置</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/logs')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">系统日志</div>\n                <div className=\"text-sm text-gray-500\">查看系统运行日志</div>\n              </button>\n            </div>\n          </div>\n        </main>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;;;;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,YAAY;IACd;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAClF,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,4BAA4B;QAC5B,MAAM,qBAAqB;YACzB,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;QACF,GACC,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS,EAAE,EAAE;gBACf,mBAAmB;YACrB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;gBACnB,qBAAqB,KAAK,iBAAiB;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAmC;YACvC,uBAAuB;YACvB,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;QACrB;QACA,OAAO,KAAK,CAAC,YAAY,IAAI;IAC/B;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YAC/D,WAAW;gBAAE,OAAO;gBAA6B,MAAM;YAAM;YAC7D,QAAQ;gBAAE,OAAO;gBAA+B,MAAM;YAAM;QAC9D;QACA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBACE,qKAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,KAAK,EAAE;sBACvG,OAAO,IAAI;;;;;;IAGlB;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAO,WAAU;kCAChB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC,0IAAA,CAAA,UAAkB;;;;;0DACnB,qKAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,qKAAC,0OAAA,CAAA,4BAAyB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShE,qKAAC;wBAAK,WAAU;;0CAEd,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOjF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOjF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,wMAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,MAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOnF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,0MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,qKAAC;oEAAG,WAAU;8EAAqC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS/E,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,qKAAC;gDAAE,WAAU;0DAAuC;;;;;;;;;;;;kDAEtD,qKAAC;wCAAG,WAAU;kDACX,kBAAkB,GAAG,CAAC,CAAC,2BACtB,qKAAC;0DACC,cAAA,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAI,WAAU;sFACb,cAAA,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAK,WAAU;8FACb,WAAW,WAAW,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;sFAIrC,qKAAC;4EAAI,WAAU;;8FACb,qKAAC;oFAAI,WAAU;;sGACb,qKAAC;4FAAE,WAAU;sGACV,WAAW,WAAW;;;;;;sGAEzB,qKAAC;4FAAK,WAAU;sGACb,eAAe,WAAW,MAAM;;;;;;;;;;;;8FAGrC,qKAAC;oFAAE,WAAU;;wFACV,WAAW,aAAa;wFAAC;wFAAI,WAAW,KAAK;;;;;;;8FAEhD,qKAAC;oFAAE,WAAU;8FACV,mBAAmB,WAAW,WAAW;;;;;;;;;;;;;;;;;;8EAIhD,qKAAC;oEAAI,WAAU;8EACb,cAAA,qKAAC;wEAAE,WAAU;kFACV,IAAI,KAAK,WAAW,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;sEAI3D,qKAAC;4DAAI,WAAU;sEACb,cAAA,qKAAC;gEAAE,WAAU;0EACV,WAAW,OAAO;;;;;;;;;;;;;;;;;+CApClB,WAAW,EAAE;;;;;;;;;;oCA2CzB,kBAAkB,MAAM,KAAK,mBAC5B,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;0CAMnC,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,qKAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;;;;;kDAOhF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEACb,cAAA,qKAAC,sMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAErB,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,qKAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;;;;;kDAOhF,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,qKAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASlF,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,qKAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,qKAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,qKAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,qKAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,qKAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,qKAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,qKAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,qKAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,qKAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,qKAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,qKAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,qKAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}]}