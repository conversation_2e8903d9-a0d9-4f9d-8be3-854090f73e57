{"version": 3, "sources": [], "sections": [{"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_7a2af4ec.module.css [ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_7a2af4ec-module__w1EmjG__className\",\n  \"variable\": \"geist_7a2af4ec-module__w1EmjG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_7a2af4ec.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22_app.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8IAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8IAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8IAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_94686b31.module.css [ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_94686b31-module__KnXEdG__className\",\n  \"variable\": \"geist_mono_94686b31-module__KnXEdG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_94686b31.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22_app.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,mJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,mJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,mJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/_app.tsx"], "sourcesContent": ["import type { AppProps } from 'next/app';\nimport { appWithTranslation } from 'next-i18next';\nimport { Geist, <PERSON>eist_Mono } from 'next/font/google';\nimport '../styles/globals.css';\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nfunction App({ Component, pageProps }: AppProps) {\n  return (\n    <div className={`${geistSans.variable} ${geistMono.variable} antialiased`}>\n      <Component {...pageProps} />\n    </div>\n  );\n}\n\nexport default appWithTranslation(App);\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;AAcA,SAAS,IAAI,EAAE,SAAS,EAAE,SAAS,EAAY;IAC7C,qBACE,qKAAC;QAAI,WAAW,GAAG,kIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,uIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;kBACvE,cAAA,qKAAC;YAAW,GAAG,SAAS;;;;;;;;;;;AAG9B;uCAEe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}]}