{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/logs.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  DocumentTextIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n  XCircleIcon,\n  CheckCircleIcon,\n  FunnelIcon,\n  ArrowPathIcon,\n  TrashIcon\n} from '@heroicons/react/24/outline';\n\ninterface LogEntry {\n  id: string;\n  timestamp: string;\n  level: 'info' | 'warning' | 'error' | 'debug';\n  category: 'system' | 'auth' | 'email' | 'api' | 'database';\n  message: string;\n  details?: any;\n  userId?: string;\n  ipAddress?: string;\n}\n\nexport default function LogsPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [logs, setLogs] = useState<LogEntry[]>([]);\n  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);\n  const [filters, setFilters] = useState({\n    level: '',\n    category: '',\n    dateRange: '',\n    search: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 50,\n    total: 0\n  });\n  const [autoRefresh, setAutoRefresh] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchLogs();\n    }\n  }, [isAuthenticated, pagination.page]);\n\n  useEffect(() => {\n    applyFilters();\n  }, [logs, filters]);\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    if (autoRefresh) {\n      interval = setInterval(() => {\n        fetchLogs();\n      }, 5000); // Refresh every 5 seconds\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [autoRefresh]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchLogs = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/logs?page=${pagination.page}&limit=${pagination.limit}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setLogs(data.data || []);\n        setPagination(prev => ({\n          ...prev,\n          total: data.pagination?.total || 0\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to fetch logs:', error);\n    }\n  };\n\n  const applyFilters = () => {\n    let filtered = [...logs];\n\n    if (filters.level) {\n      filtered = filtered.filter(log => log.level === filters.level);\n    }\n\n    if (filters.category) {\n      filtered = filtered.filter(log => log.category === filters.category);\n    }\n\n    if (filters.search) {\n      const searchTerm = filters.search.toLowerCase();\n      filtered = filtered.filter(log => \n        log.message.toLowerCase().includes(searchTerm) ||\n        log.category.toLowerCase().includes(searchTerm)\n      );\n    }\n\n    if (filters.dateRange) {\n      const now = new Date();\n      let cutoffDate = new Date();\n      \n      switch (filters.dateRange) {\n        case '1h':\n          cutoffDate.setHours(now.getHours() - 1);\n          break;\n        case '24h':\n          cutoffDate.setDate(now.getDate() - 1);\n          break;\n        case '7d':\n          cutoffDate.setDate(now.getDate() - 7);\n          break;\n        case '30d':\n          cutoffDate.setDate(now.getDate() - 30);\n          break;\n      }\n      \n      if (filters.dateRange !== '') {\n        filtered = filtered.filter(log => new Date(log.timestamp) >= cutoffDate);\n      }\n    }\n\n    setFilteredLogs(filtered);\n  };\n\n  const clearLogs = async () => {\n    if (!confirm('确定要清空所有日志吗？此操作不可撤销。')) return;\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/logs', {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        fetchLogs();\n      }\n    } catch (error) {\n      console.error('Failed to clear logs:', error);\n    }\n  };\n\n  const getLevelIcon = (level: string) => {\n    switch (level) {\n      case 'error':\n        return <XCircleIcon className=\"h-5 w-5 text-red-500\" />;\n      case 'warning':\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-500\" />;\n      case 'info':\n        return <InformationCircleIcon className=\"h-5 w-5 text-blue-500\" />;\n      case 'debug':\n        return <CheckCircleIcon className=\"h-5 w-5 text-gray-500\" />;\n      default:\n        return <InformationCircleIcon className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getLevelBadge = (level: string) => {\n    const levelConfig = {\n      error: { color: 'bg-red-100 text-red-800', text: '错误' },\n      warning: { color: 'bg-yellow-100 text-yellow-800', text: '警告' },\n      info: { color: 'bg-blue-100 text-blue-800', text: '信息' },\n      debug: { color: 'bg-gray-100 text-gray-800', text: '调试' },\n    };\n    const config = levelConfig[level as keyof typeof levelConfig] || levelConfig.info;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getCategoryName = (category: string) => {\n    const categories: { [key: string]: string } = {\n      system: '系统',\n      auth: '认证',\n      email: '邮件',\n      api: 'API',\n      database: '数据库',\n    };\n    return categories[category] || category;\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>系统日志 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">系统日志</h1>\n                <p className=\"text-gray-600\">查看系统运行日志和错误信息</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={autoRefresh}\n                    onChange={(e) => setAutoRefresh(e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">自动刷新</span>\n                </label>\n                <button\n                  onClick={fetchLogs}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <ArrowPathIcon className=\"h-4 w-4 mr-2\" />\n                  刷新\n                </button>\n                <button\n                  onClick={clearLogs}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <TrashIcon className=\"h-4 w-4 mr-2\" />\n                  清空日志\n                </button>\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center\">\n                  <FunnelIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n                  <span className=\"text-sm font-medium text-gray-700\">筛选：</span>\n                </div>\n                \n                <select\n                  value={filters.level}\n                  onChange={(e) => setFilters({ ...filters, level: e.target.value })}\n                  className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">所有级别</option>\n                  <option value=\"error\">错误</option>\n                  <option value=\"warning\">警告</option>\n                  <option value=\"info\">信息</option>\n                  <option value=\"debug\">调试</option>\n                </select>\n                \n                <select\n                  value={filters.category}\n                  onChange={(e) => setFilters({ ...filters, category: e.target.value })}\n                  className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">所有分类</option>\n                  <option value=\"system\">系统</option>\n                  <option value=\"auth\">认证</option>\n                  <option value=\"email\">邮件</option>\n                  <option value=\"api\">API</option>\n                  <option value=\"database\">数据库</option>\n                </select>\n                \n                <select\n                  value={filters.dateRange}\n                  onChange={(e) => setFilters({ ...filters, dateRange: e.target.value })}\n                  className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">所有时间</option>\n                  <option value=\"1h\">最近1小时</option>\n                  <option value=\"24h\">最近24小时</option>\n                  <option value=\"7d\">最近7天</option>\n                  <option value=\"30d\">最近30天</option>\n                </select>\n                \n                <input\n                  type=\"text\"\n                  placeholder=\"搜索日志...\"\n                  value={filters.search}\n                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}\n                  className=\"flex-1 max-w-xs border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Logs Table */}\n          <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      时间\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      级别\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      分类\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      消息\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      详情\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {filteredLogs.map((log) => (\n                    <tr key={log.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {new Date(log.timestamp).toLocaleString('zh-CN')}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          {getLevelIcon(log.level)}\n                          <span className=\"ml-2\">\n                            {getLevelBadge(log.level)}\n                          </span>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {getCategoryName(log.category)}\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-900\">\n                        <div className=\"max-w-xs truncate\" title={log.message}>\n                          {log.message}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\n                        {log.details && (\n                          <details className=\"cursor-pointer\">\n                            <summary className=\"text-blue-600 hover:text-blue-800\">查看详情</summary>\n                            <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto\">\n                              {JSON.stringify(log.details, null, 2)}\n                            </pre>\n                          </details>\n                        )}\n                        {log.ipAddress && (\n                          <div className=\"text-xs text-gray-400\">IP: {log.ipAddress}</div>\n                        )}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Pagination */}\n            <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n              <div className=\"flex-1 flex justify-between sm:hidden\">\n                <button\n                  onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n                  disabled={pagination.page === 1}\n                  className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  上一页\n                </button>\n                <button\n                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                  disabled={pagination.page * pagination.limit >= pagination.total}\n                  className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  下一页\n                </button>\n              </div>\n              <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                <div>\n                  <p className=\"text-sm text-gray-700\">\n                    显示 <span className=\"font-medium\">{(pagination.page - 1) * pagination.limit + 1}</span> 到{' '}\n                    <span className=\"font-medium\">\n                      {Math.min(pagination.page * pagination.limit, pagination.total)}\n                    </span>{' '}\n                    条，共 <span className=\"font-medium\">{pagination.total}</span> 条记录\n                  </p>\n                </div>\n                <div>\n                  <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                    <button\n                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n                      disabled={pagination.page === 1}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      上一页\n                    </button>\n                    <span className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\">\n                      第 {pagination.page} 页\n                    </span>\n                    <button\n                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                      disabled={pagination.page * pagination.limit >= pagination.total}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      下一页\n                    </button>\n                  </nav>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAiB,WAAW,IAAI;KAAC;IAErC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAM;KAAQ;IAElB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,aAAa;YACf,WAAW,YAAY;gBACrB;YACF,GAAG,OAAO,0BAA0B;QACtC;QACA,OAAO;YACL,IAAI,UAAU,cAAc;QAC9B;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,WAAW,IAAI,CAAC,OAAO,EAAE,WAAW,KAAK,EAAE,EAAE;gBAChG,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI,IAAI,EAAE;gBACvB,cAAc,CAAA,OAAQ,CAAC;wBACrB,GAAG,IAAI;wBACP,OAAO,KAAK,UAAU,EAAE,SAAS;oBACnC,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW;eAAI;SAAK;QAExB,IAAI,QAAQ,KAAK,EAAE;YACjB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK,QAAQ,KAAK;QAC/D;QAEA,IAAI,QAAQ,QAAQ,EAAE;YACpB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,QAAQ,QAAQ;QACrE;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,aAAa,QAAQ,MAAM,CAAC,WAAW;YAC7C,WAAW,SAAS,MAAM,CAAC,CAAA,MACzB,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,eACnC,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;QAExC;QAEA,IAAI,QAAQ,SAAS,EAAE;YACrB,MAAM,MAAM,IAAI;YAChB,IAAI,aAAa,IAAI;YAErB,OAAQ,QAAQ,SAAS;gBACvB,KAAK;oBACH,WAAW,QAAQ,CAAC,IAAI,QAAQ,KAAK;oBACrC;gBACF,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;gBACF,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;gBACF,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;YACJ;YAEA,IAAI,QAAQ,SAAS,KAAK,IAAI;gBAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,IAAI,SAAS,KAAK;YAC/D;QACF;QAEA,gBAAgB;IAClB;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,QAAQ,wBAAwB;QAErC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,qKAAC,8MAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,qKAAC,sOAAA,CAAA,0BAAuB;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBACH,qBAAO,qKAAC,kOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;YAC1C,KAAK;gBACH,qBAAO,qKAAC,sNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC;gBACE,qBAAO,qKAAC,kOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;QAC5C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc;YAClB,OAAO;gBAAE,OAAO;gBAA2B,MAAM;YAAK;YACtD,SAAS;gBAAE,OAAO;gBAAiC,MAAM;YAAK;YAC9D,MAAM;gBAAE,OAAO;gBAA6B,MAAM;YAAK;YACvD,OAAO;gBAAE,OAAO;gBAA6B,MAAM;YAAK;QAC1D;QACA,MAAM,SAAS,WAAW,CAAC,MAAkC,IAAI,YAAY,IAAI;QACjF,qBACE,qKAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,KAAK,EAAE;sBACvG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,aAAwC;YAC5C,QAAQ;YACR,MAAM;YACN,OAAO;YACP,KAAK;YACL,UAAU;QACZ;QACA,OAAO,UAAU,CAAC,SAAS,IAAI;IACjC;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAM,WAAU;;kEACf,qKAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;wDAChD,WAAU;;;;;;kEAEZ,qKAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,qKAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,qKAAC,kNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG5C,qKAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,qKAAC,0MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC,4MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,qKAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAGtD,qKAAC;gDACC,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,WAAU;;kEAEV,qKAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,qKAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,qKAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,qKAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,qKAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;0DAGxB,qKAAC;gDACC,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACnE,WAAU;;kEAEV,qKAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,qKAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,qKAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,qKAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,qKAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,qKAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;0DAG3B,qKAAC;gDACC,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACpE,WAAU;;kEAEV,qKAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,qKAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,qKAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,qKAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,qKAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;0DAGtB,qKAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,MAAM;gDACrB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAOlB,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAM,WAAU;;8DACf,qKAAC;oDAAM,WAAU;8DACf,cAAA,qKAAC;;0EACC,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,qKAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,qKAAC;oDAAM,WAAU;8DACd,aAAa,GAAG,CAAC,CAAC,oBACjB,qKAAC;4DAAgB,WAAU;;8EACzB,qKAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc,CAAC;;;;;;8EAE1C,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAI,WAAU;;4EACZ,aAAa,IAAI,KAAK;0FACvB,qKAAC;gFAAK,WAAU;0FACb,cAAc,IAAI,KAAK;;;;;;;;;;;;;;;;;8EAI9B,qKAAC;oEAAG,WAAU;8EACX,gBAAgB,IAAI,QAAQ;;;;;;8EAE/B,qKAAC;oEAAG,WAAU;8EACZ,cAAA,qKAAC;wEAAI,WAAU;wEAAoB,OAAO,IAAI,OAAO;kFAClD,IAAI,OAAO;;;;;;;;;;;8EAGhB,qKAAC;oEAAG,WAAU;;wEACX,IAAI,OAAO,kBACV,qKAAC;4EAAQ,WAAU;;8FACjB,qKAAC;oFAAQ,WAAU;8FAAoC;;;;;;8FACvD,qKAAC;oFAAI,WAAU;8FACZ,KAAK,SAAS,CAAC,IAAI,OAAO,EAAE,MAAM;;;;;;;;;;;;wEAIxC,IAAI,SAAS,kBACZ,qKAAC;4EAAI,WAAU;;gFAAwB;gFAAK,IAAI,SAAS;;;;;;;;;;;;;;2DA9BtD,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;kDAwCvB,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;gEAAG,CAAC;wDACnF,UAAU,WAAW,IAAI,KAAK;wDAC9B,WAAU;kEACX;;;;;;kEAGD,qKAAC;wDACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,KAAK,IAAI,GAAG;gEAAE,CAAC;wDACtE,UAAU,WAAW,IAAI,GAAG,WAAW,KAAK,IAAI,WAAW,KAAK;wDAChE,WAAU;kEACX;;;;;;;;;;;;0DAIH,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;kEACC,cAAA,qKAAC;4DAAE,WAAU;;gEAAwB;8EAChC,qKAAC;oEAAK,WAAU;8EAAe,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAG;;;;;;gEAAS;gEAAG;8EACzF,qKAAC;oEAAK,WAAU;8EACb,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;;;;;;gEACxD;gEAAI;8EACR,qKAAC;oEAAK,WAAU;8EAAe,WAAW,KAAK;;;;;;gEAAQ;;;;;;;;;;;;kEAG/D,qKAAC;kEACC,cAAA,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;4EAAG,CAAC;oEACnF,UAAU,WAAW,IAAI,KAAK;oEAC9B,WAAU;8EACX;;;;;;8EAGD,qKAAC;oEAAK,WAAU;;wEAAgH;wEAC3H,WAAW,IAAI;wEAAC;;;;;;;8EAErB,qKAAC;oEACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,MAAM,KAAK,IAAI,GAAG;4EAAE,CAAC;oEACtE,UAAU,WAAW,IAAI,GAAG,WAAW,KAAK,IAAI,WAAW,KAAK;oEAChE,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}]}