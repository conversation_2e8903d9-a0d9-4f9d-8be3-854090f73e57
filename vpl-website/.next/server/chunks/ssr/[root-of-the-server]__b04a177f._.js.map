{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/analytics/index.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport { \n  ChartBarIcon, \n  ArrowDownTrayIcon,\n  CalendarIcon,\n  ClockIcon,\n  TrendingUpIcon,\n  UsersIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\n\ninterface AnalyticsData {\n  totalSubmissions: number;\n  todaySubmissions: number;\n  weeklySubmissions: number;\n  monthlySubmissions: number;\n  conversionRate: number;\n  averageResponseTime: number;\n  topServiceTypes: Array<{\n    type: string;\n    count: number;\n    percentage: number;\n  }>;\n  submissionTrends: Array<{\n    date: string;\n    count: number;\n  }>;\n  statusDistribution: Array<{\n    status: string;\n    count: number;\n    percentage: number;\n  }>;\n  geographicDistribution: Array<{\n    region: string;\n    count: number;\n    percentage: number;\n  }>;\n  timeDistribution: Array<{\n    hour: number;\n    count: number;\n  }>;\n}\n\nexport default function Analytics() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);\n  const [dateRange, setDateRange] = useState('30d');\n  const [isExporting, setIsExporting] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchAnalytics();\n    }\n  }, [isAuthenticated, dateRange]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    fetch('/api/admin/verify', {\n      headers: { 'Authorization': `Bearer ${token}` }\n    })\n    .then(response => {\n      if (response.ok) {\n        setIsAuthenticated(true);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const fetchAnalytics = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/analytics/dashboard?dateRange=${dateRange}`, {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setAnalytics(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch analytics:', error);\n    }\n  };\n\n  const handleExport = async (format: 'csv' | 'excel') => {\n    setIsExporting(true);\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/export/submissions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          format,\n          fields: ['id', 'companyName', 'contactPerson', 'phone', 'email', 'serviceType', 'status', 'submittedAt']\n        })\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `submissions_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      } else {\n        alert('导出失败');\n      }\n    } catch (error) {\n      alert('导出时发生错误');\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const getServiceTypeName = (type: string) => {\n    const names = {\n      foreign_trade_lines: '外贸专线',\n      ecommerce_lines: '跨境电商专线',\n      vpn_services: 'VPN服务',\n      custom_solution: '定制解决方案'\n    };\n    return names[type as keyof typeof names] || type;\n  };\n\n  const getStatusName = (status: string) => {\n    const names = {\n      pending: '待处理',\n      contacted: '已联系',\n      closed: '已关闭'\n    };\n    return names[status as keyof typeof names] || status;\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      contacted: 'bg-blue-100 text-blue-800',\n      closed: 'bg-green-100 text-green-800'\n    };\n    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>数据分析 - VPL管理后台</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3\">\n                  <span className=\"text-white font-bold text-lg\">VPL</span>\n                </div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">数据分析</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-700 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-1\" />\n                  退出登录\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Controls */}\n          <div className=\"mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n            <div className=\"flex items-center space-x-4 mb-4 sm:mb-0\">\n              <select\n                value={dateRange}\n                onChange={(e) => setDateRange(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              >\n                <option value=\"7d\">最近7天</option>\n                <option value=\"30d\">最近30天</option>\n                <option value=\"90d\">最近90天</option>\n                <option value=\"1y\">最近1年</option>\n              </select>\n            </div>\n\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={() => handleExport('csv')}\n                disabled={isExporting}\n                className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n              >\n                <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                导出CSV\n              </button>\n              <button\n                onClick={() => handleExport('excel')}\n                disabled={isExporting}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n              >\n                <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                导出Excel\n              </button>\n            </div>\n          </div>\n\n          {analytics && (\n            <>\n              {/* Key Metrics */}\n              <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <ChartBarIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">总咨询数</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.totalSubmissions.toLocaleString()}</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <CalendarIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">今日咨询</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.todaySubmissions}</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <TrendingUpIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">转化率</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.conversionRate}%</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <ClockIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">平均响应时间</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.averageResponseTime}小时</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Charts Grid */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n                {/* Service Types Distribution */}\n                <div className=\"bg-white shadow rounded-lg p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">服务类型分布</h3>\n                  <div className=\"space-y-3\">\n                    {analytics.topServiceTypes.map((service, index) => (\n                      <div key={service.type} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <div className={`w-3 h-3 rounded-full mr-3 ${\n                            index === 0 ? 'bg-blue-500' :\n                            index === 1 ? 'bg-green-500' :\n                            index === 2 ? 'bg-yellow-500' : 'bg-red-500'\n                          }`}></div>\n                          <span className=\"text-sm text-gray-700\">{getServiceTypeName(service.type)}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-sm font-medium text-gray-900\">{service.count}</span>\n                          <span className=\"text-sm text-gray-500\">({service.percentage}%)</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Status Distribution */}\n                <div className=\"bg-white shadow rounded-lg p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">处理状态分布</h3>\n                  <div className=\"space-y-3\">\n                    {analytics.statusDistribution.map((status) => (\n                      <div key={status.status} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status.status)}`}>\n                            {getStatusName(status.status)}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-sm font-medium text-gray-900\">{status.count}</span>\n                          <span className=\"text-sm text-gray-500\">({status.percentage}%)</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Geographic Distribution */}\n              <div className=\"bg-white shadow rounded-lg p-6 mb-8\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">地域分布</h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {analytics.geographicDistribution.map((region) => (\n                    <div key={region.region} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <span className=\"text-sm font-medium text-gray-900\">{region.region}</span>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-sm text-gray-700\">{region.count}</span>\n                        <span className=\"text-xs text-gray-500\">({region.percentage}%)</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Submission Trends */}\n              <div className=\"bg-white shadow rounded-lg p-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">提交趋势（最近30天）</h3>\n                <div className=\"h-64 flex items-end space-x-1\">\n                  {analytics.submissionTrends.slice(-30).map((trend, index) => {\n                    const maxCount = Math.max(...analytics.submissionTrends.map(t => t.count));\n                    const height = (trend.count / maxCount) * 100;\n                    return (\n                      <div\n                        key={trend.date}\n                        className=\"flex-1 bg-blue-500 rounded-t\"\n                        style={{ height: `${height}%` }}\n                        title={`${trend.date}: ${trend.count} 条咨询`}\n                      ></div>\n                    );\n                  })}\n                </div>\n                <div className=\"mt-2 flex justify-between text-xs text-gray-500\">\n                  <span>30天前</span>\n                  <span>今天</span>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AA0Ce,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,qBAAqB;YACzB,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD,GACC,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS,EAAE,EAAE;gBACf,mBAAmB;YACrB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,yCAAyC,EAAE,WAAW,EAAE;gBACpF,SAAS;oBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAAC;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,aAAa,OAAO,IAAI;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ;wBAAC;wBAAM;wBAAe;wBAAiB;wBAAS;wBAAS;wBAAe;wBAAU;qBAAc;gBAC1G;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,UAAU,SAAS,OAAO;gBAC3G,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ;YACZ,qBAAqB;YACrB,iBAAiB;YACjB,cAAc;YACd,iBAAiB;QACnB;QACA,OAAO,KAAK,CAAC,KAA2B,IAAI;IAC9C;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ;YACZ,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA,OAAO,KAAK,CAAC,OAA6B,IAAI;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAO,WAAU;kCAChB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;0DAGD,qKAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,qKAAC,0OAAA,CAAA,4BAAyB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhE,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,WAAU;;8DAEV,qKAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,qKAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,qKAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,qKAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;kDAIvB,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,UAAU;gDACV,WAAU;;kEAEV,qKAAC,0NAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGhD,qKAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,UAAU;gDACV,WAAU;;kEAEV,qKAAC,0NAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAMnD,2BACC;;kDAEE,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;0EAE1B,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC;;sFACC,qKAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,qKAAC;4EAAG,WAAU;sFAAqC,UAAU,gBAAgB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOtG,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC,gNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;0EAE1B,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC;;sFACC,qKAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,qKAAC;4EAAG,WAAU;sFAAqC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOvF,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC,+KAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;0EAE5B,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC;;sFACC,qKAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,qKAAC;4EAAG,WAAU;;gFAAqC,UAAU,cAAc;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOtF,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC,0MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAEvB,qKAAC;gEAAI,WAAU;0EACb,cAAA,qKAAC;;sFACC,qKAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,qKAAC;4EAAG,WAAU;;gFAAqC,UAAU,mBAAmB;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAS7F,qKAAC;wCAAI,WAAU;;0DAEb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,qKAAC;wDAAI,WAAU;kEACZ,UAAU,eAAe,CAAC,GAAG,CAAC,CAAC,SAAS,sBACvC,qKAAC;gEAAuB,WAAU;;kFAChC,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAW,CAAC,0BAA0B,EACzC,UAAU,IAAI,gBACd,UAAU,IAAI,iBACd,UAAU,IAAI,kBAAkB,cAChC;;;;;;0FACF,qKAAC;gFAAK,WAAU;0FAAyB,mBAAmB,QAAQ,IAAI;;;;;;;;;;;;kFAE1E,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAK,WAAU;0FAAqC,QAAQ,KAAK;;;;;;0FAClE,qKAAC;gFAAK,WAAU;;oFAAwB;oFAAE,QAAQ,UAAU;oFAAC;;;;;;;;;;;;;;+DAXvD,QAAQ,IAAI;;;;;;;;;;;;;;;;0DAmB5B,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,qKAAC;wDAAI,WAAU;kEACZ,UAAU,kBAAkB,CAAC,GAAG,CAAC,CAAC,uBACjC,qKAAC;gEAAwB,WAAU;;kFACjC,qKAAC;wEAAI,WAAU;kFACb,cAAA,qKAAC;4EAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,OAAO,MAAM,GAAG;sFACzG,cAAc,OAAO,MAAM;;;;;;;;;;;kFAGhC,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAK,WAAU;0FAAqC,OAAO,KAAK;;;;;;0FACjE,qKAAC;gFAAK,WAAU;;oFAAwB;oFAAE,OAAO,UAAU;oFAAC;;;;;;;;;;;;;;+DARtD,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kDAiB/B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,qKAAC;gDAAI,WAAU;0DACZ,UAAU,sBAAsB,CAAC,GAAG,CAAC,CAAC,uBACrC,qKAAC;wDAAwB,WAAU;;0EACjC,qKAAC;gEAAK,WAAU;0EAAqC,OAAO,MAAM;;;;;;0EAClE,qKAAC;gEAAI,WAAU;;kFACb,qKAAC;wEAAK,WAAU;kFAAyB,OAAO,KAAK;;;;;;kFACrD,qKAAC;wEAAK,WAAU;;4EAAwB;4EAAE,OAAO,UAAU;4EAAC;;;;;;;;;;;;;;uDAJtD,OAAO,MAAM;;;;;;;;;;;;;;;;kDAY7B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,qKAAC;gDAAI,WAAU;0DACZ,UAAU,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO;oDACjD,MAAM,WAAW,KAAK,GAAG,IAAI,UAAU,gBAAgB,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;oDACxE,MAAM,SAAS,AAAC,MAAM,KAAK,GAAG,WAAY;oDAC1C,qBACE,qKAAC;wDAEC,WAAU;wDACV,OAAO;4DAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;wDAAC;wDAC9B,OAAO,GAAG,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC;uDAHrC,MAAM,IAAI;;;;;gDAMrB;;;;;;0DAEF,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;kEAAK;;;;;;kEACN,qKAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}]}