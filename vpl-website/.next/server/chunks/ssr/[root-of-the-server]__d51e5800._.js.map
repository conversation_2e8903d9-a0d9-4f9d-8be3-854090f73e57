{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' },\n  { code: 'ru', name: 'Русский', flag: '🇷🇺' },\n];\n\nexport default function LanguageSwitcher() {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];\n\n  const handleLanguageChange = (langCode: string) => {\n    const { pathname, asPath, query } = router;\n    router.push({ pathname, query }, asPath, { locale: langCode });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n        aria-label={t('navigation.language')}\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.name}</span>\n        <span className=\"sm:hidden\">{currentLanguage.flag}</span>\n        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 ${\n                    language.code === router.locale\n                      ? 'bg-blue-50 text-blue-600'\n                      : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {language.code === router.locale && (\n                    <span className=\"ml-auto text-blue-600\">✓</span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC,EAAE;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QACpC,OAAO,IAAI,CAAC;YAAE;YAAU;QAAM,GAAG,QAAQ;YAAE,QAAQ;QAAS;QAC5D,UAAU;IACZ;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAY,EAAE;;kCAEd,qKAAC,gNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,qKAAC;wBAAK,WAAU;kCAAoB,gBAAgB,IAAI;;;;;;kCACxD,qKAAC;wBAAK,WAAU;kCAAa,gBAAgB,IAAI;;;;;;kCACjD,qKAAC,sNAAA,CAAA,kBAAe;wBAAC,WAAW,CAAC,0CAA0C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAGtG,wBACC;;kCACE,qKAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,qKAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,CAAC,4FAA4F,EACtG,SAAS,IAAI,KAAK,OAAO,MAAM,GAC3B,6BACA,iBACJ;;sDAEF,qKAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,qKAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,SAAS,IAAI,KAAK,OAAO,MAAM,kBAC9B,qKAAC;4CAAK,WAAU;sDAAwB;;;;;;;mCAXrC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport LanguageSwitcher from './LanguageSwitcher';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const navigation = [\n    { name: t('navigation.home'), href: '/' },\n    { name: t('navigation.services'), href: '/services' },\n    { name: t('navigation.features'), href: '/features' },\n    { name: t('navigation.contact'), href: '/contact' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return router.pathname === '/';\n    }\n    return router.pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <span className=\"text-xl font-bold text-gray-900\">{t('brand.name')}</span>\n                <p className=\"text-xs text-gray-500 max-w-xs truncate\">{t('brand.tagline')}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-blue-600 border-b-2 border-blue-600'\n                    : 'text-gray-700 hover:text-blue-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side - Language switcher and CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LanguageSwitcher />\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('buttons.contact_us')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-2\">\n            <LanguageSwitcher />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-2 border-t border-gray-200\">\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  {t('buttons.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAoB,MAAM;QAAI;QACxC;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;KACnD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,OAAO,QAAQ,KAAK;QAC7B;QACA,OAAO,OAAO,QAAQ,CAAC,UAAU,CAAC;IACpC;IAEA,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAK,WAAU;0DAAmC,EAAE;;;;;;0DACrD,qKAAC;gDAAE,WAAU;0DAA2C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,qKAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6DAA6D,EACvE,SAAS,KAAK,IAAI,IACd,6CACA,qCACJ;8CAED,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,qKAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BACC,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,qKAAC,0MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,CAAC,qEAAqE,EAC/E,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;8CAED,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const { t } = useTranslation('common');\n\n  const services = [\n    { name: '外贸网络线路', href: '/services/foreign-trade' },\n    { name: '跨境电商线路', href: '/services/ecommerce' },\n    { name: 'VPN服务', href: '/services/vpn' },\n    { name: '定制解决方案', href: '/services/custom' },\n  ];\n\n  const support = [\n    { name: '技术支持', href: '/support' },\n    { name: '服务条款', href: '/terms' },\n    { name: '隐私政策', href: '/privacy' },\n    { name: '常见问题', href: '/faq' },\n  ];\n\n  const company = [\n    { name: '关于我们', href: '/about' },\n    { name: '新闻动态', href: '/news' },\n    { name: '合作伙伴', href: '/partners' },\n    { name: '招聘信息', href: '/careers' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t('brand.name')}</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 text-sm leading-relaxed\">\n              {t('brand.tagline')}\n            </p>\n            \n            {/* Key features */}\n            <div className=\"space-y-2 mb-6\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ShieldCheckIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>AES/RSA/TLS加密</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <GlobeAltIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>全球网络覆盖</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ServerIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>7x24技术支持</span>\n              </div>\n            </div>\n\n            {/* Contact info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+86 400-xxx-xxxx</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>中国 · 深圳</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.services')}</h3>\n            <ul className=\"space-y-2\">\n              {services.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.support')}</h3>\n            <ul className=\"space-y-2\">\n              {support.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.company')}</h3>\n            <ul className=\"space-y-2\">\n              {company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('footer.copyright')}\n            </p>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                服务条款\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                隐私政策\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                网站地图\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAae,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM;QAA0B;QAClD;YAAE,MAAM;YAAU,MAAM;QAAsB;QAC9C;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAU,MAAM;QAAmB;KAC5C;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAO;KAC9B;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAY;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,qKAAC;QAAO,WAAU;kBAChB,cAAA,qKAAC;YAAI,WAAU;;8BAEb,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,qKAAC;4CAAK,WAAU;sDAAqB,EAAE;;;;;;;;;;;;8CAEzC,qKAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,sNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,0MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,gNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,qKAAC;8DAAK;;;;;;;;;;;;sDAER,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,4MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,qKAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,qBACb,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,qKAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,qKAAC;sDACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,qKAAC;gCAAI,WAAU;;kDACb,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwE;;;;;;kDAGtG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;kDAGxG,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStH", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function Layout({ children, className = '' }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAe;IACtE,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC,+HAAA,CAAA,UAAM;;;;;0BACP,qKAAC;gBAAK,WAAW,CAAC,OAAO,EAAE,WAAW;0BACnC;;;;;;0BAEH,qKAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/services/ecommerce_lines.tsx"], "sourcesContent": ["import { GetStaticProps } from 'next';\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\nimport { useTranslation } from 'next-i18next';\nimport Head from 'next/head';\nimport Link from 'next/link';\nimport Layout from '../../components/layout/Layout';\nimport { \n  ShoppingCartIcon, \n  GlobeAltIcon, \n  ChartBarIcon,\n  ClockIcon,\n  CpuChipIcon,\n  EyeIcon\n} from '@heroicons/react/24/outline';\n\nexport default function EcommerceLines() {\n  const { t } = useTranslation(['common', 'services']);\n\n  const features = [\n    {\n      icon: ShoppingCartIcon,\n      title: '多平台支持',\n      description: '支持Amazon、eBay、Shopify等主流电商平台'\n    },\n    {\n      icon: ChartBarIcon,\n      title: '高可用性',\n      description: '99.9%的服务可用性保障，确保业务不中断'\n    },\n    {\n      icon: CpuChipIcon,\n      title: '流量优化',\n      description: '智能流量优化技术，提升页面加载速度'\n    },\n    {\n      icon: EyeIcon,\n      title: '实时监控',\n      description: '24/7实时监控网络状态，及时发现并解决问题'\n    },\n    {\n      icon: GlobeAltIcon,\n      title: '全球覆盖',\n      description: '覆盖全球主要电商市场的网络节点'\n    },\n    {\n      icon: ClockIcon,\n      title: '低延迟',\n      description: '优化的网络路由，确保最低的访问延迟'\n    }\n  ];\n\n  const platforms = [\n    { name: 'Amazon', logo: '🛒' },\n    { name: 'eBay', logo: '🏪' },\n    { name: 'Shopify', logo: '🛍️' },\n    { name: 'AliExpress', logo: '📦' },\n    { name: 'Wish', logo: '⭐' },\n    { name: 'Etsy', logo: '🎨' }\n  ];\n\n  return (\n    <>\n      <Head>\n        <title>跨境电商专线 - VPL专业网络解决方案</title>\n        <meta name=\"description\" content=\"VPL跨境电商专线服务，优化网络连接，提升用户购物体验，支持主流电商平台，确保业务稳定运行。\" />\n        <meta name=\"keywords\" content=\"跨境电商,网络专线,电商优化,Amazon,eBay,Shopify\" />\n      </Head>\n      \n      <Layout>\n        {/* Hero Section */}\n        <section className=\"bg-gradient-to-br from-green-50 to-emerald-100 py-20\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl\">\n                跨境电商专线\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\">\n                优化的跨境电商网络连接解决方案，提升用户购物体验，助力您的电商业务全球化\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Platforms Section */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">支持的电商平台</h2>\n              <p className=\"text-gray-600\">为主流电商平台提供专业的网络优化服务</p>\n            </div>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8\">\n              {platforms.map((platform, index) => (\n                <div key={index} className=\"text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n                  <div className=\"text-4xl mb-3\">{platform.logo}</div>\n                  <h3 className=\"font-semibold text-gray-900\">{platform.name}</h3>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section className=\"py-24 bg-gray-50\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">服务特性</h2>\n              <p className=\"text-lg text-gray-600\">专业的跨境电商网络优化服务</p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {features.map((feature, index) => {\n                const IconComponent = feature.icon;\n                return (\n                  <div key={index} className=\"relative p-8 bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow\">\n                    <div className=\"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-6\">\n                      <IconComponent className=\"w-6 h-6 text-green-600\" />\n                    </div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-gray-600\">\n                      {feature.description}\n                    </p>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 bg-green-600\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n                提升您的电商业务\n              </h2>\n              <p className=\"mt-4 text-lg text-green-100\">\n                联系我们获取专业的跨境电商网络解决方案\n              </p>\n              <div className=\"mt-8\">\n                <Link\n                  href=\"/contact\"\n                  className=\"inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-green-600 shadow-sm hover:bg-gray-50 transition-colors duration-200\"\n                >\n                  联系我们\n                </Link>\n              </div>\n            </div>\n          </div>\n        </section>\n      </Layout>\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async ({ locale }) => {\n  return {\n    props: {\n      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'services'])),\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAW;IAEnD,MAAM,WAAW;QACf;YACE,MAAM,wNAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gNAAA,CAAA,eAAY;YAClB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,8MAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,sMAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gNAAA,CAAA,eAAY;YAClB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,0MAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAU,MAAM;QAAK;QAC7B;YAAE,MAAM;YAAQ,MAAM;QAAK;QAC3B;YAAE,MAAM;YAAW,MAAM;QAAM;QAC/B;YAAE,MAAM;YAAc,MAAM;QAAK;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAK;KAC5B;IAED,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAGhC,qKAAC,+HAAA,CAAA,UAAM;;kCAEL,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,qKAAC;wCAAE,WAAU;kDAAyD;;;;;;;;;;;;;;;;;;;;;;kCAQ5E,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,qKAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,qKAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,qKAAC;4CAAgB,WAAU;;8DACzB,qKAAC;oDAAI,WAAU;8DAAiB,SAAS,IAAI;;;;;;8DAC7C,qKAAC;oDAAG,WAAU;8DAA+B,SAAS,IAAI;;;;;;;2CAFlD;;;;;;;;;;;;;;;;;;;;;kCAUlB,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,qKAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,qKAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wCACtB,MAAM,gBAAgB,QAAQ,IAAI;wCAClC,qBACE,qKAAC;4CAAgB,WAAU;;8DACzB,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAc,WAAU;;;;;;;;;;;8DAE3B,qKAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,qKAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;2CARd;;;;;oCAYd;;;;;;;;;;;;;;;;;kCAMN,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,qKAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAG3C,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;AAEO,MAAM,iBAAiC,OAAO,EAAE,MAAM,EAAE;IAC7D,OAAO;QACL,OAAO;YACL,GAAI,MAAM,CAAA,GAAA,uLAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,MAAM;gBAAC;gBAAU;aAAW,CAAC;QAC1E;IACF;AACF", "debugId": null}}]}