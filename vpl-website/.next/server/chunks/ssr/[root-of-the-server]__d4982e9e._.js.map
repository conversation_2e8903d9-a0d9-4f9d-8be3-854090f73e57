{"version": 3, "sources": [], "sections": [{"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/system/config.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport { useForm } from 'react-hook-form';\nimport { \n  CogIcon, \n  EnvelopeIcon, \n  ShieldCheckIcon, \n  GlobeAltIcon,\n  DocumentTextIcon,\n  CheckIcon,\n  XMarkIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { SystemConfig } from '@/types/system';\n\ninterface ConfigFormData {\n  [key: string]: string | number | boolean;\n}\n\nconst configCategories = [\n  { key: 'general', label: '基本设置', icon: CogIcon },\n  { key: 'email', label: '邮件配置', icon: EnvelopeIcon },\n  { key: 'form', label: '表单设置', icon: DocumentTextIcon },\n  { key: 'security', label: '安全设置', icon: ShieldCheckIcon },\n  { key: 'localization', label: '多语言', icon: GlobeAltIcon },\n];\n\nexport default function SystemConfig() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeCategory, setActiveCategory] = useState('general');\n  const [configs, setConfigs] = useState<SystemConfig[]>([]);\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);\n  const router = useRouter();\n\n  const { register, handleSubmit, reset, formState: { errors, isDirty } } = useForm<ConfigFormData>();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchConfigs();\n    }\n  }, [isAuthenticated, activeCategory]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    fetch('/api/admin/verify', {\n      headers: { 'Authorization': `Bearer ${token}` }\n    })\n    .then(response => {\n      if (response.ok) {\n        setIsAuthenticated(true);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const fetchConfigs = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/system/config?category=${activeCategory}`, {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setConfigs(Array.isArray(result.data) ? result.data : []);\n        \n        // Reset form with current values\n        const formData: ConfigFormData = {};\n        result.data?.forEach((config: SystemConfig) => {\n          formData[config.key] = config.value;\n        });\n        reset(formData);\n      }\n    } catch (error) {\n      console.error('Failed to fetch configs:', error);\n    }\n  };\n\n  const onSubmit = async (data: ConfigFormData) => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const configUpdates = configs.map(config => ({\n        key: config.key,\n        value: data[config.key],\n        category: config.category\n      }));\n\n      const response = await fetch('/api/admin/system/config', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ configs: configUpdates })\n      });\n\n      const result = await response.json();\n\n      if (response.ok) {\n        setSaveMessage({ type: 'success', text: '配置保存成功！' });\n        fetchConfigs(); // Refresh data\n      } else {\n        setSaveMessage({ type: 'error', text: result.message || '保存失败' });\n      }\n    } catch (error) {\n      setSaveMessage({ type: 'error', text: '网络错误，请重试' });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const renderConfigField = (config: SystemConfig) => {\n    const commonProps = {\n      ...register(config.key, { \n        required: config.required ? `${config.label}是必填项` : false,\n        min: config.validation?.min ? { value: config.validation.min, message: `最小值为 ${config.validation.min}` } : undefined,\n        max: config.validation?.max ? { value: config.validation.max, message: `最大值为 ${config.validation.max}` } : undefined,\n        pattern: config.validation?.pattern ? { value: new RegExp(config.validation.pattern), message: '格式不正确' } : undefined\n      }),\n      className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n    };\n\n    switch (config.type) {\n      case 'boolean':\n        return (\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              {...register(config.key)}\n              className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600\">{config.description}</span>\n          </div>\n        );\n      \n      case 'number':\n        return (\n          <input\n            type=\"number\"\n            {...commonProps}\n            min={config.validation?.min}\n            max={config.validation?.max}\n          />\n        );\n      \n      case 'password':\n        return (\n          <input\n            type=\"password\"\n            {...commonProps}\n            placeholder=\"••••••••\"\n          />\n        );\n      \n      default:\n        return (\n          <input\n            type={config.type === 'email' ? 'email' : 'text'}\n            {...commonProps}\n          />\n        );\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>系统配置 - VPL管理后台</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3\">\n                  <span className=\"text-white font-bold text-lg\">VPL</span>\n                </div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">系统配置</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-700 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-1\" />\n                  退出登录\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          <div className=\"lg:grid lg:grid-cols-12 lg:gap-x-5\">\n            {/* Sidebar */}\n            <aside className=\"py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3\">\n              <nav className=\"space-y-1\">\n                {configCategories.map((category) => {\n                  const Icon = category.icon;\n                  return (\n                    <button\n                      key={category.key}\n                      onClick={() => setActiveCategory(category.key)}\n                      className={`${\n                        activeCategory === category.key\n                          ? 'bg-blue-50 border-blue-500 text-blue-700'\n                          : 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900'\n                      } group border-l-4 px-3 py-2 flex items-center text-sm font-medium w-full text-left`}\n                    >\n                      <Icon className=\"text-gray-400 group-hover:text-gray-500 flex-shrink-0 -ml-1 mr-3 h-6 w-6\" />\n                      <span className=\"truncate\">{category.label}</span>\n                    </button>\n                  );\n                })}\n              </nav>\n            </aside>\n\n            {/* Main content */}\n            <div className=\"space-y-6 sm:px-6 lg:px-0 lg:col-span-9\">\n              <form onSubmit={handleSubmit(onSubmit)}>\n                <div className=\"shadow sm:rounded-md sm:overflow-hidden\">\n                  <div className=\"bg-white py-6 px-4 space-y-6 sm:p-6\">\n                    <div>\n                      <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                        {configCategories.find(c => c.key === activeCategory)?.label}\n                      </h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        配置系统的基本参数和设置\n                      </p>\n                    </div>\n\n                    {/* Save Message */}\n                    {saveMessage && (\n                      <div className={`rounded-md p-4 ${\n                        saveMessage.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'\n                      }`}>\n                        <div className=\"flex\">\n                          <div className=\"flex-shrink-0\">\n                            {saveMessage.type === 'success' ? (\n                              <CheckIcon className=\"h-5 w-5 text-green-400\" />\n                            ) : (\n                              <XMarkIcon className=\"h-5 w-5 text-red-400\" />\n                            )}\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className={`text-sm font-medium ${\n                              saveMessage.type === 'success' ? 'text-green-800' : 'text-red-800'\n                            }`}>\n                              {saveMessage.text}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Config Fields */}\n                    <div className=\"grid grid-cols-1 gap-6\">\n                      {configs.map((config) => (\n                        <div key={config.key}>\n                          <label htmlFor={config.key} className=\"block text-sm font-medium text-gray-700\">\n                            {config.label}\n                            {config.required && <span className=\"text-red-500 ml-1\">*</span>}\n                          </label>\n                          {renderConfigField(config)}\n                          {config.description && config.type !== 'boolean' && (\n                            <p className=\"mt-1 text-sm text-gray-500\">{config.description}</p>\n                          )}\n                          {errors[config.key] && (\n                            <p className=\"mt-1 text-sm text-red-600\">{errors[config.key]?.message}</p>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div className=\"px-4 py-3 bg-gray-50 text-right sm:px-6\">\n                    <button\n                      type=\"submit\"\n                      disabled={isSaving || !isDirty}\n                      className=\"bg-blue-600 border border-transparent rounded-md shadow-sm py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      {isSaving ? '保存中...' : '保存配置'}\n                    </button>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;AAgBA,MAAM,mBAAmB;IACvB;QAAE,KAAK;QAAW,OAAO;QAAQ,MAAM,sMAAA,CAAA,UAAO;IAAC;IAC/C;QAAE,KAAK;QAAS,OAAO;QAAQ,MAAM,gNAAA,CAAA,eAAY;IAAC;IAClD;QAAE,KAAK;QAAQ,OAAO;QAAQ,MAAM,wNAAA,CAAA,mBAAgB;IAAC;IACrD;QAAE,KAAK;QAAY,OAAO;QAAQ,MAAM,sNAAA,CAAA,kBAAe;IAAC;IACxD;QAAE,KAAK;QAAgB,OAAO;QAAO,MAAM,gNAAA,CAAA,eAAY;IAAC;CACzD;AAEc,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAsD;IACnG,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IAEhF,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAiB;KAAe;IAEpC,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,qBAAqB;YACzB,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD,GACC,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS,EAAE,EAAE;gBACf,mBAAmB;YACrB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,kCAAkC,EAAE,gBAAgB,EAAE;gBAClF,SAAS;oBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAAC;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,WAAW,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG,EAAE;gBAExD,iCAAiC;gBACjC,MAAM,WAA2B,CAAC;gBAClC,OAAO,IAAI,EAAE,QAAQ,CAAC;oBACpB,QAAQ,CAAC,OAAO,GAAG,CAAC,GAAG,OAAO,KAAK;gBACrC;gBACA,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3C,KAAK,OAAO,GAAG;oBACf,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC;oBACvB,UAAU,OAAO,QAAQ;gBAC3B,CAAC;YAED,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAc;YAChD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;oBAAE,MAAM;oBAAW,MAAM;gBAAU;gBAClD,gBAAgB,eAAe;YACjC,OAAO;gBACL,eAAe;oBAAE,MAAM;oBAAS,MAAM,OAAO,OAAO,IAAI;gBAAO;YACjE;QACF,EAAE,OAAO,OAAO;YACd,eAAe;gBAAE,MAAM;gBAAS,MAAM;YAAW;QACnD,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc;YAClB,GAAG,SAAS,OAAO,GAAG,EAAE;gBACtB,UAAU,OAAO,QAAQ,GAAG,GAAG,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG;gBACpD,KAAK,OAAO,UAAU,EAAE,MAAM;oBAAE,OAAO,OAAO,UAAU,CAAC,GAAG;oBAAE,SAAS,CAAC,KAAK,EAAE,OAAO,UAAU,CAAC,GAAG,EAAE;gBAAC,IAAI;gBAC3G,KAAK,OAAO,UAAU,EAAE,MAAM;oBAAE,OAAO,OAAO,UAAU,CAAC,GAAG;oBAAE,SAAS,CAAC,KAAK,EAAE,OAAO,UAAU,CAAC,GAAG,EAAE;gBAAC,IAAI;gBAC3G,SAAS,OAAO,UAAU,EAAE,UAAU;oBAAE,OAAO,IAAI,OAAO,OAAO,UAAU,CAAC,OAAO;oBAAG,SAAS;gBAAQ,IAAI;YAC7G,EAAE;YACF,WAAW;QACb;QAEA,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,qBACE,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BACC,MAAK;4BACJ,GAAG,SAAS,OAAO,GAAG,CAAC;4BACxB,WAAU;;;;;;sCAEZ,qKAAC;4BAAK,WAAU;sCAA8B,OAAO,WAAW;;;;;;;;;;;;YAItE,KAAK;gBACH,qBACE,qKAAC;oBACC,MAAK;oBACJ,GAAG,WAAW;oBACf,KAAK,OAAO,UAAU,EAAE;oBACxB,KAAK,OAAO,UAAU,EAAE;;;;;;YAI9B,KAAK;gBACH,qBACE,qKAAC;oBACC,MAAK;oBACJ,GAAG,WAAW;oBACf,aAAY;;;;;;YAIlB;gBACE,qBACE,qKAAC;oBACC,MAAM,OAAO,IAAI,KAAK,UAAU,UAAU;oBACzC,GAAG,WAAW;;;;;;QAGvB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAO,WAAU;kCAChB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;0DAGD,qKAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,qKAAC,0OAAA,CAAA,4BAAyB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhE,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;;8CAEb,qKAAC;oCAAM,WAAU;8CACf,cAAA,qKAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC;4CACrB,MAAM,OAAO,SAAS,IAAI;4CAC1B,qBACE,qKAAC;gDAEC,SAAS,IAAM,kBAAkB,SAAS,GAAG;gDAC7C,WAAW,GACT,mBAAmB,SAAS,GAAG,GAC3B,6CACA,wEACL,kFAAkF,CAAC;;kEAEpF,qKAAC;wDAAK,WAAU;;;;;;kEAChB,qKAAC;wDAAK,WAAU;kEAAY,SAAS,KAAK;;;;;;;+CATrC,SAAS,GAAG;;;;;wCAYvB;;;;;;;;;;;8CAKJ,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAK,UAAU,aAAa;kDAC3B,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;;8EACC,qKAAC;oEAAG,WAAU;8EACX,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,iBAAiB;;;;;;8EAEzD,qKAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;wDAM3C,6BACC,qKAAC;4DAAI,WAAW,CAAC,eAAe,EAC9B,YAAY,IAAI,KAAK,YAAY,wCAAwC,mCACzE;sEACA,cAAA,qKAAC;gEAAI,WAAU;;kFACb,qKAAC;wEAAI,WAAU;kFACZ,YAAY,IAAI,KAAK,0BACpB,qKAAC,0MAAA,CAAA,YAAS;4EAAC,WAAU;;;;;iGAErB,qKAAC,0MAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;kFAGzB,qKAAC;wEAAI,WAAU;kFACb,cAAA,qKAAC;4EAAE,WAAW,CAAC,oBAAoB,EACjC,YAAY,IAAI,KAAK,YAAY,mBAAmB,gBACpD;sFACC,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;sEAQ3B,qKAAC;4DAAI,WAAU;sEACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,qKAAC;;sFACC,qKAAC;4EAAM,SAAS,OAAO,GAAG;4EAAE,WAAU;;gFACnC,OAAO,KAAK;gFACZ,OAAO,QAAQ,kBAAI,qKAAC;oFAAK,WAAU;8FAAoB;;;;;;;;;;;;wEAEzD,kBAAkB;wEAClB,OAAO,WAAW,IAAI,OAAO,IAAI,KAAK,2BACrC,qKAAC;4EAAE,WAAU;sFAA8B,OAAO,WAAW;;;;;;wEAE9D,MAAM,CAAC,OAAO,GAAG,CAAC,kBACjB,qKAAC;4EAAE,WAAU;sFAA6B,MAAM,CAAC,OAAO,GAAG,CAAC,EAAE;;;;;;;mEAVxD,OAAO,GAAG;;;;;;;;;;;;;;;;8DAiB1B,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDACC,MAAK;wDACL,UAAU,YAAY,CAAC;wDACvB,WAAU;kEAET,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7C", "debugId": null}}]}