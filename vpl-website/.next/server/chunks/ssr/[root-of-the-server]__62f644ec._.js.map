{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/settings.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  CogIcon,\n  BuildingOfficeIcon,\n  EnvelopeIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  ServerIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline';\n\ninterface SystemConfig {\n  id: string;\n  category: 'general' | 'email' | 'notifications' | 'security' | 'maintenance';\n  key: string;\n  value: string;\n  description: string;\n  type: 'string' | 'number' | 'boolean' | 'json';\n  isEditable: boolean;\n}\n\nexport default function SettingsPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [configs, setConfigs] = useState<SystemConfig[]>([]);\n  const [activeTab, setActiveTab] = useState('general');\n  const [saveStatus, setSaveStatus] = useState<{ success: boolean; message: string } | null>(null);\n  const [formData, setFormData] = useState<{ [key: string]: any }>({});\n  const router = useRouter();\n\n  const tabs = [\n    { id: 'general', name: '基本设置', icon: BuildingOfficeIcon },\n    { id: 'email', name: '邮件设置', icon: EnvelopeIcon },\n    { id: 'notifications', name: '通知设置', icon: BellIcon },\n    { id: 'security', name: '安全设置', icon: ShieldCheckIcon },\n    { id: 'maintenance', name: '维护设置', icon: ServerIcon },\n  ];\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchConfigs();\n    }\n  }, [isAuthenticated]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchConfigs = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/system-config', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        const configsData = data.data || [];\n        setConfigs(configsData);\n        \n        // Initialize form data\n        const initialFormData: { [key: string]: any } = {};\n        configsData.forEach((config: SystemConfig) => {\n          if (config.type === 'boolean') {\n            initialFormData[config.key] = config.value === 'true';\n          } else if (config.type === 'number') {\n            initialFormData[config.key] = parseFloat(config.value) || 0;\n          } else {\n            initialFormData[config.key] = config.value;\n          }\n        });\n        setFormData(initialFormData);\n      }\n    } catch (error) {\n      console.error('Failed to fetch configs:', error);\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/system-config', {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ configs: formData })\n      });\n\n      if (response.ok) {\n        setSaveStatus({ success: true, message: '设置保存成功' });\n        fetchConfigs();\n      } else {\n        setSaveStatus({ success: false, message: '保存失败，请重试' });\n      }\n    } catch (error) {\n      setSaveStatus({ success: false, message: '保存失败：网络错误' });\n    }\n\n    // Clear status after 3 seconds\n    setTimeout(() => setSaveStatus(null), 3000);\n  };\n\n  const handleInputChange = (key: string, value: any, type: string) => {\n    let processedValue = value;\n    \n    if (type === 'number') {\n      processedValue = parseFloat(value) || 0;\n    } else if (type === 'boolean') {\n      processedValue = value;\n    }\n    \n    setFormData(prev => ({\n      ...prev,\n      [key]: processedValue\n    }));\n  };\n\n  const renderConfigInput = (config: SystemConfig) => {\n    const value = formData[config.key] ?? config.value;\n\n    if (!config.isEditable) {\n      return (\n        <div className=\"text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-md\">\n          {config.value}\n        </div>\n      );\n    }\n\n    switch (config.type) {\n      case 'boolean':\n        return (\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={value === true || value === 'true'}\n              onChange={(e) => handleInputChange(config.key, e.target.checked, config.type)}\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-700\">启用</span>\n          </label>\n        );\n      \n      case 'number':\n        return (\n          <input\n            type=\"number\"\n            value={value}\n            onChange={(e) => handleInputChange(config.key, e.target.value, config.type)}\n            className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          />\n        );\n      \n      case 'json':\n        return (\n          <textarea\n            value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value}\n            onChange={(e) => {\n              try {\n                const parsed = JSON.parse(e.target.value);\n                handleInputChange(config.key, parsed, config.type);\n              } catch {\n                handleInputChange(config.key, e.target.value, config.type);\n              }\n            }}\n            rows={4}\n            className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono\"\n          />\n        );\n      \n      default:\n        return (\n          <input\n            type=\"text\"\n            value={value}\n            onChange={(e) => handleInputChange(config.key, e.target.value, config.type)}\n            className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          />\n        );\n    }\n  };\n\n  const filteredConfigs = configs.filter(config => config.category === activeTab);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>系统设置 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">系统设置</h1>\n                <p className=\"text-gray-600\">管理网站和系统配置</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={handleSave}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  <CheckCircleIcon className=\"h-4 w-4 mr-2\" />\n                  保存设置\n                </button>\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Save Status */}\n        {saveStatus && (\n          <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4`}>\n            <div className={`p-4 rounded-md ${\n              saveStatus.success \n                ? 'bg-green-50 border border-green-200' \n                : 'bg-red-50 border border-red-200'\n            }`}>\n              <div className=\"flex\">\n                {saveStatus.success ? (\n                  <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                ) : (\n                  <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                )}\n                <div className=\"ml-3\">\n                  <p className={`text-sm ${\n                    saveStatus.success ? 'text-green-800' : 'text-red-800'\n                  }`}>\n                    {saveStatus.message}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          <div className=\"flex\">\n            {/* Sidebar */}\n            <div className=\"w-64 bg-white shadow rounded-lg mr-6 h-fit\">\n              <nav className=\"p-4\">\n                <ul className=\"space-y-2\">\n                  {tabs.map((tab) => {\n                    const Icon = tab.icon;\n                    return (\n                      <li key={tab.id}>\n                        <button\n                          onClick={() => setActiveTab(tab.id)}\n                          className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                            activeTab === tab.id\n                              ? 'bg-blue-100 text-blue-700'\n                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                          }`}\n                        >\n                          <Icon className=\"h-5 w-5 mr-3\" />\n                          {tab.name}\n                        </button>\n                      </li>\n                    );\n                  })}\n                </ul>\n              </nav>\n            </div>\n\n            {/* Content */}\n            <div className=\"flex-1 bg-white shadow rounded-lg\">\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-6\">\n                  {tabs.find(tab => tab.id === activeTab)?.name}\n                </h3>\n                \n                <div className=\"space-y-6\">\n                  {filteredConfigs.length > 0 ? (\n                    filteredConfigs.map((config) => (\n                      <div key={config.id} className=\"border-b border-gray-200 pb-6 last:border-b-0\">\n                        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n                          <div className=\"lg:col-span-1\">\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                              {config.key.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                            </label>\n                            <p className=\"text-sm text-gray-500\">{config.description}</p>\n                          </div>\n                          <div className=\"lg:col-span-2\">\n                            {renderConfigInput(config)}\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"text-center py-12\">\n                      <CogIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无配置项</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        此分类下暂时没有可配置的选项。\n                      </p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAgD;IAC3F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClE,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,MAAM;YAAQ,MAAM,4NAAA,CAAA,qBAAkB;QAAC;QACxD;YAAE,IAAI;YAAS,MAAM;YAAQ,MAAM,gNAAA,CAAA,eAAY;QAAC;QAChD;YAAE,IAAI;YAAiB,MAAM;YAAQ,MAAM,wMAAA,CAAA,WAAQ;QAAC;QACpD;YAAE,IAAI;YAAY,MAAM;YAAQ,MAAM,sNAAA,CAAA,kBAAe;QAAC;QACtD;YAAE,IAAI;YAAe,MAAM;YAAQ,MAAM,4MAAA,CAAA,aAAU;QAAC;KACrD;IAED,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,cAAc,KAAK,IAAI,IAAI,EAAE;gBACnC,WAAW;gBAEX,uBAAuB;gBACvB,MAAM,kBAA0C,CAAC;gBACjD,YAAY,OAAO,CAAC,CAAC;oBACnB,IAAI,OAAO,IAAI,KAAK,WAAW;wBAC7B,eAAe,CAAC,OAAO,GAAG,CAAC,GAAG,OAAO,KAAK,KAAK;oBACjD,OAAO,IAAI,OAAO,IAAI,KAAK,UAAU;wBACnC,eAAe,CAAC,OAAO,GAAG,CAAC,GAAG,WAAW,OAAO,KAAK,KAAK;oBAC5D,OAAO;wBACL,eAAe,CAAC,OAAO,GAAG,CAAC,GAAG,OAAO,KAAK;oBAC5C;gBACF;gBACA,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAS;YAC3C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc;oBAAE,SAAS;oBAAM,SAAS;gBAAS;gBACjD;YACF,OAAO;gBACL,cAAc;oBAAE,SAAS;oBAAO,SAAS;gBAAW;YACtD;QACF,EAAE,OAAO,OAAO;YACd,cAAc;gBAAE,SAAS;gBAAO,SAAS;YAAY;QACvD;QAEA,+BAA+B;QAC/B,WAAW,IAAM,cAAc,OAAO;IACxC;IAEA,MAAM,oBAAoB,CAAC,KAAa,OAAY;QAClD,IAAI,iBAAiB;QAErB,IAAI,SAAS,UAAU;YACrB,iBAAiB,WAAW,UAAU;QACxC,OAAO,IAAI,SAAS,WAAW;YAC7B,iBAAiB;QACnB;QAEA,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC,IAAI,OAAO,KAAK;QAElD,IAAI,CAAC,OAAO,UAAU,EAAE;YACtB,qBACE,qKAAC;gBAAI,WAAU;0BACZ,OAAO,KAAK;;;;;;QAGnB;QAEA,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,qBACE,qKAAC;oBAAM,WAAU;;sCACf,qKAAC;4BACC,MAAK;4BACL,SAAS,UAAU,QAAQ,UAAU;4BACrC,UAAU,CAAC,IAAM,kBAAkB,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,OAAO,IAAI;4BAC5E,WAAU;;;;;;sCAEZ,qKAAC;4BAAK,WAAU;sCAA6B;;;;;;;;;;;;YAInD,KAAK;gBACH,qBACE,qKAAC;oBACC,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,IAAI;oBAC1E,WAAU;;;;;;YAIhB,KAAK;gBACH,qBACE,qKAAC;oBACC,OAAO,OAAO,UAAU,WAAW,KAAK,SAAS,CAAC,OAAO,MAAM,KAAK;oBACpE,UAAU,CAAC;wBACT,IAAI;4BACF,MAAM,SAAS,KAAK,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK;4BACxC,kBAAkB,OAAO,GAAG,EAAE,QAAQ,OAAO,IAAI;wBACnD,EAAE,OAAM;4BACN,kBAAkB,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,IAAI;wBAC3D;oBACF;oBACA,MAAM;oBACN,WAAU;;;;;;YAIhB;gBACE,qBACE,qKAAC;oBACC,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,IAAI;oBAC1E,WAAU;;;;;;QAGlB;IACF;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IAErE,IAAI,WAAW;QACb,qBACE,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,qKAAC,sNAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG9C,qKAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASR,4BACC,qKAAC;wBAAI,WAAW,CAAC,2CAA2C,CAAC;kCAC3D,cAAA,qKAAC;4BAAI,WAAW,CAAC,eAAe,EAC9B,WAAW,OAAO,GACd,wCACA,mCACJ;sCACA,cAAA,qKAAC;gCAAI,WAAU;;oCACZ,WAAW,OAAO,iBACjB,qKAAC,sNAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;6DAE3B,qKAAC,sOAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDAErC,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAE,WAAW,CAAC,QAAQ,EACrB,WAAW,OAAO,GAAG,mBAAmB,gBACxC;sDACC,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/B,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;;8CAEb,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAG,WAAU;sDACX,KAAK,GAAG,CAAC,CAAC;gDACT,MAAM,OAAO,IAAI,IAAI;gDACrB,qBACE,qKAAC;8DACC,cAAA,qKAAC;wDACC,SAAS,IAAM,aAAa,IAAI,EAAE;wDAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,8BACA,sDACJ;;0EAEF,qKAAC;gEAAK,WAAU;;;;;;4DACf,IAAI,IAAI;;;;;;;mDAVJ,IAAI,EAAE;;;;;4CAcnB;;;;;;;;;;;;;;;;8CAMN,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DACX,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;0DAG3C,qKAAC;gDAAI,WAAU;0DACZ,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,uBACnB,qKAAC;wDAAoB,WAAU;kEAC7B,cAAA,qKAAC;4DAAI,WAAU;;8EACb,qKAAC;oEAAI,WAAU;;sFACb,qKAAC;4EAAM,WAAU;sFACd,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;sFAEpE,qKAAC;4EAAE,WAAU;sFAAyB,OAAO,WAAW;;;;;;;;;;;;8EAE1D,qKAAC;oEAAI,WAAU;8EACZ,kBAAkB;;;;;;;;;;;;uDATf,OAAO,EAAE;;;;8EAerB,qKAAC;oDAAI,WAAU;;sEACb,qKAAC,sMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,qKAAC;4DAAG,WAAU;sEAAyC;;;;;;sEACvD,qKAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAahE", "debugId": null}}]}