{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/email-config/index.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\nimport fs from 'fs';\nimport path from 'path';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst EMAIL_CONFIG_FILE = path.join(DATA_DIR, 'email-config.json');\n\n// Ensure data directory and file exist\nif (!fs.existsSync(DATA_DIR)) {\n  fs.mkdirSync(DATA_DIR, { recursive: true });\n}\n\nif (!fs.existsSync(EMAIL_CONFIG_FILE)) {\n  fs.writeFileSync(EMAIL_CONFIG_FILE, JSON.stringify([], null, 2));\n}\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Verify admin token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({\n      success: false,\n      message: 'No token provided'\n    });\n  }\n\n  const token = authHeader.substring(7);\n  \n  try {\n    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (jwtError) {\n    return res.status(401).json({\n      success: false,\n      message: 'Invalid token'\n    });\n  }\n\n  if (req.method === 'GET') {\n    try {\n      const data = fs.readFileSync(EMAIL_CONFIG_FILE, 'utf8');\n      const configs = JSON.parse(data);\n      \n      return res.status(200).json({\n        success: true,\n        message: 'Email configurations retrieved successfully',\n        data: configs,\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to retrieve email configurations',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else if (req.method === 'POST') {\n    try {\n      const data = fs.readFileSync(EMAIL_CONFIG_FILE, 'utf8');\n      const configs = JSON.parse(data);\n      \n      const newConfig = {\n        id: Date.now().toString(),\n        ...req.body,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n\n      // If this is set as default, remove default from others\n      if (newConfig.isDefault) {\n        configs.forEach((config: any) => {\n          config.isDefault = false;\n        });\n      }\n\n      configs.push(newConfig);\n      fs.writeFileSync(EMAIL_CONFIG_FILE, JSON.stringify(configs, null, 2));\n\n      return res.status(201).json({\n        success: true,\n        message: 'Email configuration created successfully',\n        data: newConfig,\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to create email configuration',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAE9C,uCAAuC;AACvC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;IAC5B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU;QAAE,WAAW;IAAK;AAC3C;AAEA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,oBAAoB;IACrC,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,mBAAmB,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;AAC/D;AAEe,eAAe,QAAQ,GAAmB,EAAE,GAAoB;IAC7E,qBAAqB;IACrB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;IAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;IAEnC,IAAI;QACF,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IAC9C,EAAE,OAAO,UAAU;QACjB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,mBAAmB;YAChD,MAAM,UAAU,KAAK,KAAK,CAAC;YAE3B,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO,IAAI,IAAI,MAAM,KAAK,QAAQ;QAChC,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,mBAAmB;YAChD,MAAM,UAAU,KAAK,KAAK,CAAC;YAE3B,MAAM,YAAY;gBAChB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,GAAG,IAAI,IAAI;gBACX,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,wDAAwD;YACxD,IAAI,UAAU,SAAS,EAAE;gBACvB,QAAQ,OAAO,CAAC,CAAC;oBACf,OAAO,SAAS,GAAG;gBACrB;YACF;YAEA,QAAQ,IAAI,CAAC;YACb,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,mBAAmB,KAAK,SAAS,CAAC,SAAS,MAAM;YAElE,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO;QACL,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}