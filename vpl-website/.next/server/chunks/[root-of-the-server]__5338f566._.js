module.exports = {

"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsonwebtoken", () => require("jsonwebtoken"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/pages/api/admin/logs.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "addLogEntry": ()=>addLogEntry,
    "default": ()=>handler
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/jsonwebtoken [external] (jsonwebtoken, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const LOGS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'system-logs.json');
// Ensure data directory and file exist
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
        recursive: true
    });
}
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(LOGS_FILE)) {
    // Create some sample logs
    const sampleLogs = [
        {
            id: '1',
            timestamp: new Date().toISOString(),
            level: 'info',
            category: 'system',
            message: '系统启动成功',
            details: {
                version: '1.0.0',
                environment: 'production'
            }
        },
        {
            id: '2',
            timestamp: new Date(Date.now() - 60000).toISOString(),
            level: 'info',
            category: 'auth',
            message: '管理员登录成功',
            details: {
                username: 'admin'
            },
            ipAddress: '127.0.0.1'
        },
        {
            id: '3',
            timestamp: new Date(Date.now() - 120000).toISOString(),
            level: 'warning',
            category: 'email',
            message: 'SMTP连接超时，正在重试',
            details: {
                host: 'smtp.example.com',
                port: 587
            }
        }
    ];
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(LOGS_FILE, JSON.stringify(sampleLogs, null, 2));
}
async function handler(req, res) {
    // Verify admin token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
            success: false,
            message: 'No token provided'
        });
    }
    const token = authHeader.substring(7);
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$jsonwebtoken__$5b$external$5d$__$28$jsonwebtoken$2c$__cjs$29$__["default"].verify(token, process.env.JWT_SECRET || 'your-secret-key');
    } catch (jwtError) {
        return res.status(401).json({
            success: false,
            message: 'Invalid token'
        });
    }
    if (req.method === 'GET') {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(LOGS_FILE, 'utf8');
            let logs = JSON.parse(data);
            // Sort by timestamp (newest first)
            logs.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
            // Apply pagination
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 50;
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedLogs = logs.slice(startIndex, endIndex);
            return res.status(200).json({
                success: true,
                message: 'System logs retrieved successfully',
                data: paginatedLogs,
                pagination: {
                    page,
                    limit,
                    total: logs.length,
                    totalPages: Math.ceil(logs.length / limit)
                }
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to retrieve system logs',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else if (req.method === 'POST') {
        // Add new log entry
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(LOGS_FILE, 'utf8');
            const logs = JSON.parse(data);
            const newLog = {
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                ...req.body
            };
            logs.push(newLog);
            // Keep only the last 10000 logs to prevent file from growing too large
            if (logs.length > 10000) {
                logs.splice(0, logs.length - 10000);
            }
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(LOGS_FILE, JSON.stringify(logs, null, 2));
            return res.status(201).json({
                success: true,
                message: 'Log entry created successfully',
                data: newLog
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to create log entry',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else if (req.method === 'DELETE') {
        // Clear all logs
        try {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(LOGS_FILE, JSON.stringify([], null, 2));
            // Add a log entry about the clearing
            const clearLog = {
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                level: 'info',
                category: 'system',
                message: '系统日志已清空',
                details: {
                    clearedBy: 'admin'
                }
            };
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(LOGS_FILE, JSON.stringify([
                clearLog
            ], null, 2));
            return res.status(200).json({
                success: true,
                message: 'All logs cleared successfully'
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Failed to clear logs',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    } else {
        return res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
}
const addLogEntry = (level, category, message, details, userId, ipAddress)=>{
    try {
        const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(LOGS_FILE, 'utf8');
        const logs = JSON.parse(data);
        const newLog = {
            id: Date.now().toString(),
            timestamp: new Date().toISOString(),
            level,
            category,
            message,
            details,
            userId,
            ipAddress
        };
        logs.push(newLog);
        // Keep only the last 10000 logs
        if (logs.length > 10000) {
            logs.splice(0, logs.length - 10000);
        }
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(LOGS_FILE, JSON.stringify(logs, null, 2));
    } catch (error) {
        console.error('Failed to add log entry:', error);
    }
};
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__5338f566._.js.map