{"version": 3, "sources": [], "sections": [{"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/users/index.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\nimport bcrypt from 'bcryptjs';\nimport fs from 'fs';\nimport path from 'path';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst USERS_FILE = path.join(DATA_DIR, 'admin-users.json');\n\n// Ensure data directory and file exist\nif (!fs.existsSync(DATA_DIR)) {\n  fs.mkdirSync(DATA_DIR, { recursive: true });\n}\n\nif (!fs.existsSync(USERS_FILE)) {\n  // Create default admin user\n  const defaultAdmin = {\n    id: '1',\n    username: 'admin',\n    email: '<EMAIL>',\n    fullName: '系统管理员',\n    role: 'super_admin',\n    password: bcrypt.hashSync('admin123', 10),\n    isActive: true,\n    createdAt: new Date().toISOString(),\n    createdBy: 'system',\n  };\n  fs.writeFileSync(USERS_FILE, JSON.stringify([defaultAdmin], null, 2));\n}\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Verify admin token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({\n      success: false,\n      message: 'No token provided'\n    });\n  }\n\n  const token = authHeader.substring(7);\n  \n  try {\n    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (jwtError) {\n    return res.status(401).json({\n      success: false,\n      message: 'Invalid token'\n    });\n  }\n\n  if (req.method === 'GET') {\n    try {\n      const data = fs.readFileSync(USERS_FILE, 'utf8');\n      const users = JSON.parse(data);\n      \n      // Remove password from response\n      const safeUsers = users.map((user: any) => {\n        const { password, ...safeUser } = user;\n        return safeUser;\n      });\n      \n      return res.status(200).json({\n        success: true,\n        message: 'Users retrieved successfully',\n        data: safeUsers,\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to retrieve users',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else if (req.method === 'POST') {\n    try {\n      const data = fs.readFileSync(USERS_FILE, 'utf8');\n      const users = JSON.parse(data);\n      \n      const { username, email, fullName, role, password, isActive } = req.body;\n\n      // Check if username or email already exists\n      const existingUser = users.find((user: any) => \n        user.username === username || user.email === email\n      );\n\n      if (existingUser) {\n        return res.status(400).json({\n          success: false,\n          message: '用户名或邮箱已存在'\n        });\n      }\n\n      const hashedPassword = bcrypt.hashSync(password, 10);\n      \n      const newUser = {\n        id: Date.now().toString(),\n        username,\n        email,\n        fullName,\n        role,\n        password: hashedPassword,\n        isActive: isActive !== undefined ? isActive : true,\n        createdAt: new Date().toISOString(),\n        createdBy: 'admin', // TODO: Get from JWT token\n      };\n\n      users.push(newUser);\n      fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));\n\n      // Remove password from response\n      const { password: _, ...safeUser } = newUser;\n\n      return res.status(201).json({\n        success: true,\n        message: 'User created successfully',\n        data: safeUser,\n      });\n    } catch (error) {\n      return res.status(500).json({\n        success: false,\n        message: 'Failed to create user',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n    }\n  } else {\n    return res.status(405).json({\n      success: false,\n      message: 'Method not allowed'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAEvC,uCAAuC;AACvC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;IAC5B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU;QAAE,WAAW;IAAK;AAC3C;AAEA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;IAC9B,4BAA4B;IAC5B,MAAM,eAAe;QACnB,IAAI;QACJ,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU,gHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,YAAY;QACtC,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC;QAAC;KAAa,EAAE,MAAM;AACpE;AAEe,eAAe,QAAQ,GAAmB,EAAE,GAAoB;IAC7E,qBAAqB;IACrB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;IAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;IAEnC,IAAI;QACF,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IAC9C,EAAE,OAAO,UAAU;QACjB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI,IAAI,MAAM,KAAK,OAAO;QACxB,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;YACzC,MAAM,QAAQ,KAAK,KAAK,CAAC;YAEzB,gCAAgC;YAChC,MAAM,YAAY,MAAM,GAAG,CAAC,CAAC;gBAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,GAAG;gBAClC,OAAO;YACT;YAEA,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO,IAAI,IAAI,MAAM,KAAK,QAAQ;QAChC,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;YACzC,MAAM,QAAQ,KAAK,KAAK,CAAC;YAEzB,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,IAAI;YAExE,4CAA4C;YAC5C,MAAM,eAAe,MAAM,IAAI,CAAC,CAAC,OAC/B,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAK,KAAK;YAG/C,IAAI,cAAc;gBAChB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,MAAM,iBAAiB,gHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;YAEjD,MAAM,UAAU;gBACd,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA;gBACA;gBACA;gBACA,UAAU;gBACV,UAAU,aAAa,YAAY,WAAW;gBAC9C,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW;YACb;YAEA,MAAM,IAAI,CAAC;YACX,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;YAEzD,gCAAgC;YAChC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,UAAU,GAAG;YAErC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF,OAAO;QACL,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}