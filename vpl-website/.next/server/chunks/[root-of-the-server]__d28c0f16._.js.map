{"version": 3, "sources": [], "sections": [{"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/api/admin/users/index.ts"], "sourcesContent": ["import type { NextApiRequest, NextApiResponse } from 'next';\nimport jwt from 'jsonwebtoken';\nimport bcrypt from 'bcryptjs';\nimport fs from 'fs';\nimport path from 'path';\nimport { AdminUser, CreateUserRequest, UpdateUserRequest, UserResponse } from '@/types/user';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst USERS_FILE = path.join(DATA_DIR, 'admin-users.json');\n\n// Ensure data directory and file exist\nif (!fs.existsSync(DATA_DIR)) {\n  fs.mkdirSync(DATA_DIR, { recursive: true });\n}\n\nif (!fs.existsSync(USERS_FILE)) {\n  // Create default admin user with enhanced structure\n  const defaultAdmin: AdminUser = {\n    id: '1',\n    username: 'admin',\n    email: '<EMAIL>',\n    fullName: '系统管理员',\n    role: {\n      id: '1',\n      name: 'super_admin',\n      displayName: '超级管理员',\n      description: '拥有所有系统权限',\n      permissions: [],\n      isSystem: true,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    },\n    permissions: [],\n    isActive: true,\n    loginAttempts: 0,\n    twoFactorEnabled: false,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n    createdBy: 'system',\n    // Store hashed password separately (not in the AdminUser type for security)\n    password: bcrypt.hashSync('admin123', 10)\n  } as any;\n  fs.writeFileSync(USERS_FILE, JSON.stringify([defaultAdmin], null, 2));\n}\n\nfunction verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n}\n\nfunction checkPermission(user: any, permission: string): boolean {\n  // Super admin has all permissions\n  if (user.role === 'super_admin') return true;\n\n  // Check specific permissions\n  return user.permissions?.includes(permission) || false;\n}\n\nexport default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse<UserResponse>\n) {\n  try {\n    // Verify authentication\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return res.status(401).json({\n        success: false,\n        message: 'No token provided'\n      });\n    }\n\n    const token = authHeader.substring(7);\n    const decoded = verifyToken(token);\n\n    if (req.method === 'GET') {\n      // Check read permission\n      if (!checkPermission(decoded, 'users:read')) {\n        return res.status(403).json({\n          success: false,\n          message: 'Insufficient permissions'\n        });\n      }\n\n      const { page = '1', limit = '10', search, role, status } = req.query;\n      const pageNum = parseInt(page as string);\n      const limitNum = parseInt(limit as string);\n\n      const data = fs.readFileSync(USERS_FILE, 'utf8');\n      let users = JSON.parse(data);\n\n      // Apply filters\n      if (search && typeof search === 'string') {\n        const searchLower = search.toLowerCase();\n        users = users.filter((user: any) =>\n          user.username.toLowerCase().includes(searchLower) ||\n          user.fullName.toLowerCase().includes(searchLower) ||\n          user.email.toLowerCase().includes(searchLower)\n        );\n      }\n\n      if (role && typeof role === 'string') {\n        users = users.filter((user: any) => user.role?.name === role || user.role === role);\n      }\n\n      if (status && typeof status === 'string') {\n        const isActive = status === 'active';\n        users = users.filter((user: any) => user.isActive === isActive);\n      }\n\n      // Remove password from response\n      const safeUsers = users.map((user: any) => {\n        const { password, ...safeUser } = user;\n        return safeUser;\n      });\n\n      // Pagination\n      const total = safeUsers.length;\n      const totalPages = Math.ceil(total / limitNum);\n      const startIndex = (pageNum - 1) * limitNum;\n      const endIndex = startIndex + limitNum;\n      const paginatedUsers = safeUsers.slice(startIndex, endIndex);\n\n      return res.status(200).json({\n        success: true,\n        message: 'Users retrieved successfully',\n        data: paginatedUsers,\n        pagination: {\n          page: pageNum,\n          limit: limitNum,\n          total,\n          totalPages\n        }\n      });\n    } else if (req.method === 'POST') {\n      // Check create permission\n      if (!checkPermission(decoded, 'users:create')) {\n        return res.status(403).json({\n          success: false,\n          message: 'Insufficient permissions'\n        });\n      }\n\n      const createRequest: CreateUserRequest = req.body;\n\n      // Validate required fields\n      if (!createRequest.username || !createRequest.email || !createRequest.fullName || !createRequest.password || !createRequest.role) {\n        return res.status(400).json({\n          success: false,\n          message: 'Missing required fields'\n        });\n      }\n\n      const data = fs.readFileSync(USERS_FILE, 'utf8');\n      const users = JSON.parse(data);\n\n      // Check if username or email already exists\n      const existingUser = users.find((user: any) =>\n        user.username === createRequest.username || user.email === createRequest.email\n      );\n\n      if (existingUser) {\n        return res.status(400).json({\n          success: false,\n          message: 'Username or email already exists'\n        });\n      }\n\n      const hashedPassword = await bcrypt.hash(createRequest.password, 10);\n\n      const newUser: AdminUser = {\n        id: Date.now().toString(),\n        username: createRequest.username,\n        email: createRequest.email,\n        fullName: createRequest.fullName,\n        role: {\n          id: '2',\n          name: createRequest.role,\n          displayName: createRequest.role === 'admin' ? '管理员' : '用户',\n          description: '',\n          permissions: [],\n          isSystem: false,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        },\n        permissions: [],\n        isActive: createRequest.isActive ?? true,\n        loginAttempts: 0,\n        twoFactorEnabled: false,\n        phone: createRequest.phone,\n        department: createRequest.department,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: decoded.username,\n        password: hashedPassword\n      } as any;\n\n      users.push(newUser);\n      fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));\n\n      // Remove password from response\n      const { password: _, ...safeUser } = newUser;\n\n      return res.status(201).json({\n        success: true,\n        message: 'User created successfully',\n        data: safeUser\n      });\n\n    } else {\n      return res.status(405).json({\n        success: false,\n        message: 'Method not allowed'\n      });\n    }\n\n  } catch (error) {\n    console.error('Users API error:', error);\n    return res.status(500).json({\n      success: false,\n      message: 'Internal server error'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;;;;;AAGA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAEvC,uCAAuC;AACvC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;IAC5B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU;QAAE,WAAW;IAAK;AAC3C;AAEA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;IAC9B,oDAAoD;IACpD,MAAM,eAA0B;QAC9B,IAAI;QACJ,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;YACJ,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;YACb,aAAa,EAAE;YACf,UAAU;YACV,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,aAAa,EAAE;QACf,UAAU;QACV,eAAe;QACf,kBAAkB;QAClB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;QACX,4EAA4E;QAC5E,UAAU,gHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,YAAY;IACxC;IACA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC;QAAC;KAAa,EAAE,MAAM;AACpE;AAEA,SAAS,YAAY,KAAa;IAChC,IAAI;QACF,OAAO,iHAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;IACrD,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,SAAS,gBAAgB,IAAS,EAAE,UAAkB;IACpD,kCAAkC;IAClC,IAAI,KAAK,IAAI,KAAK,eAAe,OAAO;IAExC,6BAA6B;IAC7B,OAAO,KAAK,WAAW,EAAE,SAAS,eAAe;AACnD;AAEe,eAAe,QAC5B,GAAmB,EACnB,GAAkC;IAElC,IAAI;QACF,wBAAwB;QACxB,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAE5B,IAAI,IAAI,MAAM,KAAK,OAAO;YACxB,wBAAwB;YACxB,IAAI,CAAC,gBAAgB,SAAS,eAAe;gBAC3C,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,MAAM,EAAE,OAAO,GAAG,EAAE,QAAQ,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK;YACpE,MAAM,UAAU,SAAS;YACzB,MAAM,WAAW,SAAS;YAE1B,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;YACzC,IAAI,QAAQ,KAAK,KAAK,CAAC;YAEvB,gBAAgB;YAChB,IAAI,UAAU,OAAO,WAAW,UAAU;gBACxC,MAAM,cAAc,OAAO,WAAW;gBACtC,QAAQ,MAAM,MAAM,CAAC,CAAC,OACpB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEtC;YAEA,IAAI,QAAQ,OAAO,SAAS,UAAU;gBACpC,QAAQ,MAAM,MAAM,CAAC,CAAC,OAAc,KAAK,IAAI,EAAE,SAAS,QAAQ,KAAK,IAAI,KAAK;YAChF;YAEA,IAAI,UAAU,OAAO,WAAW,UAAU;gBACxC,MAAM,WAAW,WAAW;gBAC5B,QAAQ,MAAM,MAAM,CAAC,CAAC,OAAc,KAAK,QAAQ,KAAK;YACxD;YAEA,gCAAgC;YAChC,MAAM,YAAY,MAAM,GAAG,CAAC,CAAC;gBAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,GAAG;gBAClC,OAAO;YACT;YAEA,aAAa;YACb,MAAM,QAAQ,UAAU,MAAM;YAC9B,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;YACrC,MAAM,aAAa,CAAC,UAAU,CAAC,IAAI;YACnC,MAAM,WAAW,aAAa;YAC9B,MAAM,iBAAiB,UAAU,KAAK,CAAC,YAAY;YAEnD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;gBACN,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP;oBACA;gBACF;YACF;QACF,OAAO,IAAI,IAAI,MAAM,KAAK,QAAQ;YAChC,0BAA0B;YAC1B,IAAI,CAAC,gBAAgB,SAAS,iBAAiB;gBAC7C,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,MAAM,gBAAmC,IAAI,IAAI;YAEjD,2BAA2B;YAC3B,IAAI,CAAC,cAAc,QAAQ,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,cAAc,QAAQ,IAAI,CAAC,cAAc,QAAQ,IAAI,CAAC,cAAc,IAAI,EAAE;gBAChI,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;YACzC,MAAM,QAAQ,KAAK,KAAK,CAAC;YAEzB,4CAA4C;YAC5C,MAAM,eAAe,MAAM,IAAI,CAAC,CAAC,OAC/B,KAAK,QAAQ,KAAK,cAAc,QAAQ,IAAI,KAAK,KAAK,KAAK,cAAc,KAAK;YAGhF,IAAI,cAAc;gBAChB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,MAAM,iBAAiB,MAAM,gHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,cAAc,QAAQ,EAAE;YAEjE,MAAM,UAAqB;gBACzB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,UAAU,cAAc,QAAQ;gBAChC,OAAO,cAAc,KAAK;gBAC1B,UAAU,cAAc,QAAQ;gBAChC,MAAM;oBACJ,IAAI;oBACJ,MAAM,cAAc,IAAI;oBACxB,aAAa,cAAc,IAAI,KAAK,UAAU,QAAQ;oBACtD,aAAa;oBACb,aAAa,EAAE;oBACf,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,aAAa,EAAE;gBACf,UAAU,cAAc,QAAQ,IAAI;gBACpC,eAAe;gBACf,kBAAkB;gBAClB,OAAO,cAAc,KAAK;gBAC1B,YAAY,cAAc,UAAU;gBACpC,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,QAAQ,QAAQ;gBAC3B,UAAU;YACZ;YAEA,MAAM,IAAI,CAAC;YACX,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;YAEzD,gCAAgC;YAChC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,UAAU,GAAG;YAErC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QAEF,OAAO;YACL,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1B,SAAS;YACT,SAAS;QACX;IACF;AACF", "debugId": null}}]}