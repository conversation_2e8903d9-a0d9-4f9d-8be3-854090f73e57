{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' },\n  { code: 'ru', name: 'Русский', flag: '🇷🇺' },\n];\n\nexport default function LanguageSwitcher() {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];\n\n  const handleLanguageChange = (langCode: string) => {\n    const { pathname, asPath, query } = router;\n    router.push({ pathname, query }, asPath, { locale: langCode });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n        aria-label={t('navigation.language')}\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.name}</span>\n        <span className=\"sm:hidden\">{currentLanguage.flag}</span>\n        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 ${\n                    language.code === router.locale\n                      ? 'bg-blue-50 text-blue-600'\n                      : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {language.code === router.locale && (\n                    <span className=\"ml-auto text-blue-600\">✓</span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;;;AALA;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC,EAAE;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QACpC,OAAO,IAAI,CAAC;YAAE;YAAU;QAAM,GAAG,QAAQ;YAAE,QAAQ;QAAS;QAC5D,UAAU;IACZ;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAY,EAAE;;kCAEd,0JAAC,mNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,0JAAC;wBAAK,WAAU;kCAAoB,gBAAgB,IAAI;;;;;;kCACxD,0JAAC;wBAAK,WAAU;kCAAa,gBAAgB,IAAI;;;;;;kCACjD,0JAAC,yNAAA,CAAA,kBAAe;wBAAC,WAAW,AAAC,6CAAuE,OAA3B,SAAS,eAAe;;;;;;;;;;;;YAGlG,wBACC;;kCACE,0JAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,0JAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,AAAC,+FAIX,OAHC,SAAS,IAAI,KAAK,OAAO,MAAM,GAC3B,6BACA;;sDAGN,0JAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,0JAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,SAAS,IAAI,KAAK,OAAO,MAAM,kBAC9B,0JAAC;4CAAK,WAAU;sDAAwB;;;;;;;mCAXrC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC;GAzDwB;;QAEP,0HAAA,CAAA,YAAS;QACV,4JAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport LanguageSwitcher from './LanguageSwitcher';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const navigation = [\n    { name: t('navigation.home'), href: '/' },\n    { name: t('navigation.services'), href: '/services' },\n    { name: t('navigation.features'), href: '/features' },\n    { name: t('navigation.contact'), href: '/contact' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return router.pathname === '/';\n    }\n    return router.pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <span className=\"text-xl font-bold text-gray-900\">{t('brand.name')}</span>\n                <p className=\"text-xs text-gray-500 max-w-xs truncate\">{t('brand.tagline')}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-blue-600 border-b-2 border-blue-600'\n                    : 'text-gray-700 hover:text-blue-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side - Language switcher and CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LanguageSwitcher />\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('buttons.contact_us')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-2\">\n            <LanguageSwitcher />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-2 border-t border-gray-200\">\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  {t('buttons.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAoB,MAAM;QAAI;QACxC;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;KACnD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,OAAO,QAAQ,KAAK;QAC7B;QACA,OAAO,OAAO,QAAQ,CAAC,UAAU,CAAC;IACpC;IAEA,qBACE,0JAAC;QAAO,WAAU;kBAChB,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAK,WAAU;0DAAmC,EAAE;;;;;;0DACrD,0JAAC;gDAAE,WAAU;0DAA2C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,0JAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC,wHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,gEAIX,OAHC,SAAS,KAAK,IAAI,IACd,6CACA;8CAGL,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,0JAAC;4BAAI,WAAU;;8CACb,0JAAC,4IAAA,CAAA,UAAgB;;;;;8CACjB,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,0JAAC;4BAAI,WAAU;;8CACb,0JAAC,4IAAA,CAAA,UAAgB;;;;;8CACjB,0JAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BACC,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC,wHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,AAAC,wEAIX,OAHC,SAAS,KAAK,IAAI,IACd,6BACA;8CAGL,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GAlHwB;;QAEP,0HAAA,CAAA,YAAS;QACV,4JAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const { t } = useTranslation('common');\n\n  const services = [\n    { name: '外贸网络线路', href: '/services/foreign-trade' },\n    { name: '跨境电商线路', href: '/services/ecommerce' },\n    { name: 'VPN服务', href: '/services/vpn' },\n    { name: '定制解决方案', href: '/services/custom' },\n  ];\n\n  const support = [\n    { name: '技术支持', href: '/support' },\n    { name: '服务条款', href: '/terms' },\n    { name: '隐私政策', href: '/privacy' },\n    { name: '常见问题', href: '/faq' },\n  ];\n\n  const company = [\n    { name: '关于我们', href: '/about' },\n    { name: '新闻动态', href: '/news' },\n    { name: '合作伙伴', href: '/partners' },\n    { name: '招聘信息', href: '/careers' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t('brand.name')}</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 text-sm leading-relaxed\">\n              {t('brand.tagline')}\n            </p>\n            \n            {/* Key features */}\n            <div className=\"space-y-2 mb-6\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ShieldCheckIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>AES/RSA/TLS加密</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <GlobeAltIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>全球网络覆盖</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ServerIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>7x24技术支持</span>\n              </div>\n            </div>\n\n            {/* Contact info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+86 400-xxx-xxxx</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>中国 · 深圳</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.services')}</h3>\n            <ul className=\"space-y-2\">\n              {services.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.support')}</h3>\n            <ul className=\"space-y-2\">\n              {support.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.company')}</h3>\n            <ul className=\"space-y-2\">\n              {company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('footer.copyright')}\n            </p>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                服务条款\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                隐私政策\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                网站地图\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAae,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM;QAA0B;QAClD;YAAE,MAAM;YAAU,MAAM;QAAsB;QAC9C;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAU,MAAM;QAAmB;KAC5C;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAO;KAC9B;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAY;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,0JAAC;QAAO,WAAU;kBAChB,cAAA,0JAAC;YAAI,WAAU;;8BAEb,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,0JAAC;4CAAK,WAAU;sDAAqB,EAAE;;;;;;;;;;;;8CAEzC,0JAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,yNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,mNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,+MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,0JAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,6MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,mNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,+MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,0JAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,qBACb,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,0JAAC;gCAAI,WAAU;;kDACb,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwE;;;;;;kDAGtG,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;kDAGxG,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStH;GApJwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function Layout({ children, className = '' }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,OAAO,KAAyC;QAAzC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAe,GAAzC;IAC7B,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC,kIAAA,CAAA,UAAM;;;;;0BACP,0JAAC;gBAAK,WAAW,AAAC,UAAmB,OAAV;0BACxB;;;;;;0BAEH,0JAAC,kIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KAVwB", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useTranslation } from 'next-i18next';\nimport Link from 'next/link';\nimport { ArrowRightIcon, PlayIcon } from '@heroicons/react/24/outline';\nimport { useState } from 'react';\n\nexport default function HeroSection() {\n  const { t } = useTranslation(['home', 'common']);\n  const [isVideoPlaying, setIsVideoPlaying] = useState(false);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: [0.25, 0.25, 0.25, 0.75],\n      },\n    },\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Video/Image */}\n      <div className=\"absolute inset-0 z-0\">\n        {isVideoPlaying ? (\n          <video\n            autoPlay\n            muted\n            loop\n            className=\"w-full h-full object-cover\"\n            poster=\"/images/hero-bg.jpg\"\n          >\n            <source src=\"/videos/hero-video.mp4\" type=\"video/mp4\" />\n          </video>\n        ) : (\n          <div \n            className=\"w-full h-full bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900\"\n            style={{\n              backgroundImage: `linear-gradient(rgba(30, 58, 138, 0.8), rgba(67, 56, 202, 0.8)), url('/images/network-bg.jpg')`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n            }}\n          />\n        )}\n        \n        {/* Animated overlay pattern */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/50 to-transparent\">\n          <div className=\"absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10\"></div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"max-w-4xl mx-auto\"\n        >\n          {/* Badge */}\n          <motion.div variants={itemVariants} className=\"mb-8\">\n            <span className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100/20 text-blue-100 border border-blue-300/30 backdrop-blur-sm\">\n              <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n              {t('home:hero.badge')}\n            </span>\n          </motion.div>\n\n          {/* Main Title */}\n          <motion.h1 \n            variants={itemVariants}\n            className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\"\n          >\n            <span className=\"block\">{t('home:hero.title_line1')}</span>\n            <span className=\"block bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent\">\n              {t('home:hero.title_line2')}\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p \n            variants={itemVariants}\n            className=\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed\"\n          >\n            {t('home:hero.subtitle')}\n          </motion.p>\n\n          {/* Description */}\n          <motion.p \n            variants={itemVariants}\n            className=\"text-lg text-blue-200/80 mb-12 max-w-2xl mx-auto\"\n          >\n            {t('home:hero.description')}\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n          >\n            <Link\n              href=\"/contact\"\n              className=\"group inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n            >\n              {t('common:cta.get_started')}\n              <ArrowRightIcon className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n            \n            <button\n              onClick={() => setIsVideoPlaying(!isVideoPlaying)}\n              className=\"group inline-flex items-center px-8 py-4 text-lg font-semibold text-white border-2 border-white/30 rounded-full hover:border-white/60 hover:bg-white/10 transition-all duration-300 backdrop-blur-sm\"\n            >\n              <PlayIcon className=\"mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300\" />\n              {t('common:cta.watch_demo')}\n            </button>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-wrap justify-center items-center gap-8 text-blue-200/60\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n              <span className=\"text-sm font-medium\">{t('home:hero.trust.secure')}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-blue-400 rounded-full\"></div>\n              <span className=\"text-sm font-medium\">{t('home:hero.trust.reliable')}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-purple-400 rounded-full\"></div>\n              <span className=\"text-sm font-medium\">{t('home:hero.trust.professional')}</span>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 1.5, duration: 0.8 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"flex flex-col items-center text-white/60\">\n          <span className=\"text-sm mb-2\">{t('common:scroll_down')}</span>\n          <motion.div\n            animate={{ y: [0, 8, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\"\n          >\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-white/60 rounded-full mt-2\"\n            />\n          </motion.div>\n        </div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAQ;KAAS;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,0JAAC;QAAQ,WAAU;;0BAEjB,0JAAC;gBAAI,WAAU;;oBACZ,+BACC,0JAAC;wBACC,QAAQ;wBACR,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,QAAO;kCAEP,cAAA,0JAAC;4BAAO,KAAI;4BAAyB,MAAK;;;;;;;;;;6CAG5C,0JAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAkB;4BAClB,gBAAgB;4BAChB,oBAAoB;wBACtB;;;;;;kCAKJ,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,0JAAC;gCAAK,WAAU;;kDACd,0JAAC;wCAAK,WAAU;;;;;;oCACf,EAAE;;;;;;;;;;;;sCAKP,0JAAC,sLAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;8CAEV,0JAAC;oCAAK,WAAU;8CAAS,EAAE;;;;;;8CAC3B,0JAAC;oCAAK,WAAU;8CACb,EAAE;;;;;;;;;;;;sCAKP,0JAAC,sLAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE;;;;;;sCAIL,0JAAC,sLAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE;;;;;;sCAIL,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCAET,EAAE;sDACH,0JAAC,uNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;8CAG5B,0JAAC;oCACC,SAAS,IAAM,kBAAkB,CAAC;oCAClC,WAAU;;sDAEV,0JAAC,2MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,EAAE;;;;;;;;;;;;;sCAKP,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;;;;;sDACf,0JAAC;4CAAK,WAAU;sDAAuB,EAAE;;;;;;;;;;;;8CAE3C,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;;;;;sDACf,0JAAC;4CAAK,WAAU;sDAAuB,EAAE;;;;;;;;;;;;8CAE3C,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;;;;;sDACf,0JAAC;4CAAK,WAAU;sDAAuB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjD,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;0BAEV,cAAA,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAK,WAAU;sCAAgB,EAAE;;;;;;sCAClC,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAG;iCAAE;4BAAC;4BACxB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;sCAEV,cAAA,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GAxKwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/ui/AnimatedSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { ReactNode } from 'react';\n\ninterface AnimatedSectionProps {\n  children: ReactNode;\n  className?: string;\n  delay?: number;\n  direction?: 'up' | 'down' | 'left' | 'right' | 'fade';\n  duration?: number;\n}\n\nexport default function AnimatedSection({\n  children,\n  className = '',\n  delay = 0,\n  direction = 'up',\n  duration = 0.6\n}: AnimatedSectionProps) {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const variants = {\n    hidden: {\n      opacity: 0,\n      y: direction === 'up' ? 50 : direction === 'down' ? -50 : 0,\n      x: direction === 'left' ? 50 : direction === 'right' ? -50 : 0,\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      x: 0,\n      transition: {\n        duration,\n        delay,\n        ease: [0.25, 0.25, 0.25, 0.75],\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      ref={ref}\n      initial=\"hidden\"\n      animate={inView ? 'visible' : 'hidden'}\n      variants={variants}\n      className={className}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAce,SAAS,gBAAgB,KAMjB;QANiB,EACtC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,YAAY,IAAI,EAChB,WAAW,GAAG,EACO,GANiB;;IAOtC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,WAAW;QACf,QAAQ;YACN,SAAS;YACT,GAAG,cAAc,OAAO,KAAK,cAAc,SAAS,CAAC,KAAK;YAC1D,GAAG,cAAc,SAAS,KAAK,cAAc,UAAU,CAAC,KAAK;QAC/D;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,GAAG;YACH,YAAY;gBACV;gBACA;gBACA,MAAM;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAQ;QACR,SAAS,SAAS,YAAY;QAC9B,UAAU;QACV,WAAW;kBAEV;;;;;;AAGP;GAzCwB;;QAOA,+JAAA,CAAA,YAAS;;;KAPT", "debugId": null}}, {"offset": {"line": 1968, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useTranslation } from 'next-i18next';\nimport Link from 'next/link';\nimport { \n  GlobeAltIcon, \n  ShoppingCartIcon, \n  ShieldCheckIcon, \n  CogIcon,\n  ArrowRightIcon,\n  CheckIcon\n} from '@heroicons/react/24/outline';\nimport AnimatedSection from '../ui/AnimatedSection';\n\nconst services = [\n  {\n    id: 'foreign_trade_lines',\n    icon: GlobeAltIcon,\n    color: 'from-blue-500 to-cyan-500',\n    bgColor: 'bg-blue-50',\n    iconColor: 'text-blue-600',\n    features: [\n      'dedicated_bandwidth',\n      'global_coverage',\n      'low_latency',\n      'enterprise_support'\n    ]\n  },\n  {\n    id: 'ecommerce_lines',\n    icon: ShoppingCartIcon,\n    color: 'from-green-500 to-emerald-500',\n    bgColor: 'bg-green-50',\n    iconColor: 'text-green-600',\n    features: [\n      'multi_platform',\n      'high_availability',\n      'traffic_optimization',\n      'real_time_monitoring'\n    ]\n  },\n  {\n    id: 'vpn_services',\n    icon: ShieldCheckIcon,\n    color: 'from-purple-500 to-indigo-500',\n    bgColor: 'bg-purple-50',\n    iconColor: 'text-purple-600',\n    features: [\n      'military_encryption',\n      'zero_logs',\n      'global_servers',\n      'unlimited_bandwidth'\n    ]\n  },\n  {\n    id: 'custom_solution',\n    icon: CogIcon,\n    color: 'from-orange-500 to-red-500',\n    bgColor: 'bg-orange-50',\n    iconColor: 'text-orange-600',\n    features: [\n      'tailored_design',\n      'expert_consultation',\n      'scalable_architecture',\n      'ongoing_support'\n    ]\n  }\n];\n\nexport default function ServicesSection() {\n  const { t } = useTranslation(['home', 'common']);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: [0.25, 0.25, 0.25, 0.75],\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 bg-gradient-to-b from-gray-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4\">\n            {t('home:services.badge')}\n          </span>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            {t('home:services.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {t('home:services.subtitle')}\n          </p>\n        </AnimatedSection>\n\n        {/* Services Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\"\n        >\n          {services.map((service, index) => {\n            const Icon = service.icon;\n            return (\n              <motion.div\n                key={service.id}\n                variants={cardVariants}\n                className=\"group relative\"\n              >\n                <div className=\"relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden\">\n                  {/* Background Gradient */}\n                  <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />\n                  \n                  {/* Icon */}\n                  <div className={`inline-flex items-center justify-center w-16 h-16 ${service.bgColor} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                    <Icon className={`h-8 w-8 ${service.iconColor}`} />\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                    {t(`home:services.items.${service.id}.title`)}\n                  </h3>\n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {t(`home:services.items.${service.id}.description`)}\n                  </p>\n\n                  {/* Features */}\n                  <ul className=\"space-y-2 mb-6\">\n                    {service.features.map((feature) => (\n                      <li key={feature} className=\"flex items-center text-sm text-gray-600\">\n                        <CheckIcon className=\"h-4 w-4 text-green-500 mr-2 flex-shrink-0\" />\n                        {t(`home:services.features.${feature}`)}\n                      </li>\n                    ))}\n                  </ul>\n\n                  {/* CTA */}\n                  <Link\n                    href={`/services/${service.id}`}\n                    className={`inline-flex items-center text-sm font-semibold bg-gradient-to-r ${service.color} bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300`}\n                  >\n                    {t('common:learn_more')}\n                    <ArrowRightIcon className=\"ml-1 h-4 w-4\" />\n                  </Link>\n\n                  {/* Hover Effect */}\n                  <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${service.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left`} />\n                </div>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        {/* Bottom CTA */}\n        <AnimatedSection delay={0.3} className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 mb-8\">\n            {t('home:services.cta_text')}\n          </p>\n          <Link\n            href=\"/contact\"\n            className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n          >\n            {t('common:cta.contact_us')}\n            <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n          </Link>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAbA;;;;;;AAeA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM,mNAAA,CAAA,eAAY;QAClB,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM,2NAAA,CAAA,mBAAgB;QACtB,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM,yNAAA,CAAA,kBAAe;QACrB,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM,yMAAA,CAAA,UAAO;QACb,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAQ;KAAS;IAE/C,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,0JAAC;QAAQ,WAAU;kBACjB,cAAA,0JAAC;YAAI,WAAU;;8BAEb,0JAAC,uIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,0JAAC;4BAAK,WAAU;sCACb,EAAE;;;;;;sCAEL,0JAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,0JAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;oBACzC,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,0JAAC,sLAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;sCAEV,cAAA,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCAAI,WAAW,AAAC,sCAAmD,OAAd,QAAQ,KAAK,EAAC;;;;;;kDAGpE,0JAAC;wCAAI,WAAW,AAAC,qDAAoE,OAAhB,QAAQ,OAAO,EAAC;kDACnF,cAAA,0JAAC;4CAAK,WAAW,AAAC,WAA4B,OAAlB,QAAQ,SAAS;;;;;;;;;;;kDAI/C,0JAAC;wCAAG,WAAU;kDACX,EAAE,AAAC,uBAAiC,OAAX,QAAQ,EAAE,EAAC;;;;;;kDAEvC,0JAAC;wCAAE,WAAU;kDACV,EAAE,AAAC,uBAAiC,OAAX,QAAQ,EAAE,EAAC;;;;;;kDAIvC,0JAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,0JAAC;gDAAiB,WAAU;;kEAC1B,0JAAC,6MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpB,EAAE,AAAC,0BAAiC,OAAR;;+CAFtB;;;;;;;;;;kDAQb,0JAAC,wHAAA,CAAA,UAAI;wCACH,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;wCAC7B,WAAW,AAAC,mEAAgF,OAAd,QAAQ,KAAK,EAAC;;4CAE3F,EAAE;0DACH,0JAAC,uNAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;kDAI5B,0JAAC;wCAAI,WAAW,AAAC,yDAAsE,OAAd,QAAQ,KAAK,EAAC;;;;;;;;;;;;2BAzCpF,QAAQ,EAAE;;;;;oBA6CrB;;;;;;8BAIF,0JAAC,uIAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;;sCACrC,0JAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,0JAAC,wHAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCAET,EAAE;8CACH,0JAAC,uNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;GArHwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/ui/CountUpNumber.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useInView } from 'react-intersection-observer';\n\ninterface CountUpNumberProps {\n  end: number;\n  duration?: number;\n  suffix?: string;\n  prefix?: string;\n  className?: string;\n}\n\nexport default function CountUpNumber({\n  end,\n  duration = 2000,\n  suffix = '',\n  prefix = '',\n  className = ''\n}: CountUpNumberProps) {\n  const [count, setCount] = useState(0);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  useEffect(() => {\n    if (!inView) return;\n\n    let startTime: number;\n    let animationFrame: number;\n\n    const animate = (currentTime: number) => {\n      if (!startTime) startTime = currentTime;\n      const progress = Math.min((currentTime - startTime) / duration, 1);\n      \n      // Easing function for smooth animation\n      const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n      setCount(Math.floor(easeOutQuart * end));\n\n      if (progress < 1) {\n        animationFrame = requestAnimationFrame(animate);\n      }\n    };\n\n    animationFrame = requestAnimationFrame(animate);\n\n    return () => {\n      if (animationFrame) {\n        cancelAnimationFrame(animationFrame);\n      }\n    };\n  }, [inView, end, duration]);\n\n  return (\n    <span ref={ref} className={className}>\n      {prefix}{count.toLocaleString()}{suffix}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAae,SAAS,cAAc,KAMjB;QANiB,EACpC,GAAG,EACH,WAAW,IAAI,EACf,SAAS,EAAE,EACX,SAAS,EAAE,EACX,YAAY,EAAE,EACK,GANiB;;IAOpC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,IAAI;YACJ,IAAI;YAEJ,MAAM;mDAAU,CAAC;oBACf,IAAI,CAAC,WAAW,YAAY;oBAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;oBAEhE,uCAAuC;oBACvC,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;oBAChD,SAAS,KAAK,KAAK,CAAC,eAAe;oBAEnC,IAAI,WAAW,GAAG;wBAChB,iBAAiB,sBAAsB;oBACzC;gBACF;;YAEA,iBAAiB,sBAAsB;YAEvC;2CAAO;oBACL,IAAI,gBAAgB;wBAClB,qBAAqB;oBACvB;gBACF;;QACF;kCAAG;QAAC;QAAQ;QAAK;KAAS;IAE1B,qBACE,0JAAC;QAAK,KAAK;QAAK,WAAW;;YACxB;YAAQ,MAAM,cAAc;YAAI;;;;;;;AAGvC;GA9CwB;;QAQA,+JAAA,CAAA,YAAS;;;KART", "debugId": null}}, {"offset": {"line": 2385, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useTranslation } from 'next-i18next';\nimport CountUpNumber from '../ui/CountUpNumber';\nimport AnimatedSection from '../ui/AnimatedSection';\nimport { \n  UsersIcon, \n  GlobeAltIcon, \n  ClockIcon, \n  ShieldCheckIcon,\n  TrophyIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nconst stats = [\n  {\n    id: 'clients',\n    icon: UsersIcon,\n    value: 5000,\n    suffix: '+',\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-100'\n  },\n  {\n    id: 'countries',\n    icon: GlobeAltIcon,\n    value: 120,\n    suffix: '+',\n    color: 'text-green-600',\n    bgColor: 'bg-green-100'\n  },\n  {\n    id: 'uptime',\n    icon: ClockIcon,\n    value: 99.9,\n    suffix: '%',\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-100'\n  },\n  {\n    id: 'experience',\n    icon: TrophyIcon,\n    value: 15,\n    suffix: '+',\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-100'\n  },\n  {\n    id: 'servers',\n    icon: ServerIcon,\n    value: 200,\n    suffix: '+',\n    color: 'text-indigo-600',\n    bgColor: 'bg-indigo-100'\n  },\n  {\n    id: 'security',\n    icon: ShieldCheckIcon,\n    value: 100,\n    suffix: '%',\n    color: 'text-red-600',\n    bgColor: 'bg-red-100'\n  }\n];\n\nexport default function StatsSection() {\n  const { t } = useTranslation(['home']);\n\n  return (\n    <section className=\"py-24 bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/50 to-transparent\"></div>\n      \n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <span className=\"inline-block px-4 py-2 bg-blue-100/20 text-blue-100 rounded-full text-sm font-semibold mb-4 backdrop-blur-sm border border-blue-300/30\">\n            {t('home:stats.badge')}\n          </span>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            {t('home:stats.title')}\n          </h2>\n          <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n            {t('home:stats.subtitle')}\n          </p>\n        </AnimatedSection>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8\">\n          {stats.map((stat, index) => {\n            const Icon = stat.icon;\n            return (\n              <AnimatedSection\n                key={stat.id}\n                delay={index * 0.1}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-2xl\">\n                  {/* Icon */}\n                  <div className={`inline-flex items-center justify-center w-16 h-16 ${stat.bgColor} rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                    <Icon className={`h-8 w-8 ${stat.color}`} />\n                  </div>\n\n                  {/* Number */}\n                  <div className=\"text-3xl md:text-4xl font-bold text-white mb-2\">\n                    <CountUpNumber\n                      end={stat.value}\n                      suffix={stat.suffix}\n                      duration={2500}\n                    />\n                  </div>\n\n                  {/* Label */}\n                  <p className=\"text-blue-100 font-medium\">\n                    {t(`home:stats.items.${stat.id}.label`)}\n                  </p>\n\n                  {/* Description */}\n                  <p className=\"text-blue-200/70 text-sm mt-2\">\n                    {t(`home:stats.items.${stat.id}.description`)}\n                  </p>\n                </div>\n              </AnimatedSection>\n            );\n          })}\n        </div>\n\n        {/* Bottom Text */}\n        <AnimatedSection delay={0.6} className=\"text-center mt-16\">\n          <p className=\"text-lg text-blue-100 max-w-4xl mx-auto leading-relaxed\">\n            {t('home:stats.bottom_text')}\n          </p>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAcA,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM,6MAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,mNAAA,CAAA,eAAY;QAClB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,6MAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,+MAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,+MAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,yNAAA,CAAA,kBAAe;QACrB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;IACX;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;KAAO;IAErC,qBACE,0JAAC;QAAQ,WAAU;;0BAEjB,0JAAC;gBAAI,WAAU;;;;;;0BACf,0JAAC;gBAAI,WAAU;;;;;;0BAEf,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC,uIAAA,CAAA,UAAe;wBAAC,WAAU;;0CACzB,0JAAC;gCAAK,WAAU;0CACb,EAAE;;;;;;0CAEL,0JAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,0JAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAKP,0JAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;4BAChB,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,0JAAC,uIAAA,CAAA,UAAe;gCAEd,OAAO,QAAQ;gCACf,WAAU;0CAEV,cAAA,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAI,WAAW,AAAC,qDAAiE,OAAb,KAAK,OAAO,EAAC;sDAChF,cAAA,0JAAC;gDAAK,WAAW,AAAC,WAAqB,OAAX,KAAK,KAAK;;;;;;;;;;;sDAIxC,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC,qIAAA,CAAA,UAAa;gDACZ,KAAK,KAAK,KAAK;gDACf,QAAQ,KAAK,MAAM;gDACnB,UAAU;;;;;;;;;;;sDAKd,0JAAC;4CAAE,WAAU;sDACV,EAAE,AAAC,oBAA2B,OAAR,KAAK,EAAE,EAAC;;;;;;sDAIjC,0JAAC;4CAAE,WAAU;sDACV,EAAE,AAAC,oBAA2B,OAAR,KAAK,EAAE,EAAC;;;;;;;;;;;;+BA1B9B,KAAK,EAAE;;;;;wBA+BlB;;;;;;kCAIF,0JAAC,uIAAA,CAAA,UAAe;wBAAC,OAAO;wBAAK,WAAU;kCACrC,cAAA,0JAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;GAxEwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 2633, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/index.tsx"], "sourcesContent": ["import { GetStaticProps } from 'next';\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\nimport { useTranslation } from 'next-i18next';\nimport Head from 'next/head';\nimport Layout from '../components/layout/Layout';\nimport HeroSection from '../components/home/<USER>';\nimport ServicesSection from '../components/home/<USER>';\nimport StatsSection from '../components/home/<USER>';\n\nexport default function Home() {\n  const { t } = useTranslation(['common', 'home']);\n\n  return (\n    <>\n      <Head>\n        <title>{`VPL - ${t('brand.tagline')}`}</title>\n        <meta name=\"description\" content={t('home:hero.subtitle')} />\n        <meta name=\"keywords\" content=\"外贸网络线路,跨境电商,VPN服务,网络加密,AES加密,RSA加密,TLS加密\" />\n      </Head>\n\n      <Layout>\n        <HeroSection />\n        <ServicesSection />\n        <StatsSection />\n      </Layout>\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async ({ locale }) => {\n  return {\n    props: {\n      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'home'])),\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAO;IAE/C,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAO,AAAC,SAA2B,OAAnB,EAAE;;;;;;kCACnB,0JAAC;wBAAK,MAAK;wBAAc,SAAS,EAAE;;;;;;kCACpC,0JAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAGhC,0JAAC,kIAAA,CAAA,UAAM;;kCACL,0JAAC,qIAAA,CAAA,UAAW;;;;;kCACZ,0JAAC,yIAAA,CAAA,UAAe;;;;;kCAChB,0JAAC,sIAAA,CAAA,UAAY;;;;;;;;;;;;;AAIrB;GAlBwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 2735, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}