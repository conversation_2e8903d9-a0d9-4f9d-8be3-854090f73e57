{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/inquiries.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport { \n  MagnifyingGlassIcon,\n  FunnelIcon,\n  EllipsisVerticalIcon,\n  EyeIcon,\n  PencilIcon,\n  TrashIcon,\n  ArrowDownTrayIcon,\n  CheckIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { ContactSubmission, SubmissionFilters, PaginationParams } from '../../types/admin';\n\nexport default function InquiriesPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [submissions, setSubmissions] = useState<ContactSubmission[]>([]);\n  const [selectedSubmissions, setSelectedSubmissions] = useState<string[]>([]);\n  const [filters, setFilters] = useState<SubmissionFilters>({});\n  const [pagination, setPagination] = useState<PaginationParams>({\n    page: 1,\n    limit: 10,\n    sortBy: 'submittedAt',\n    sortOrder: 'desc'\n  });\n  const [totalPages, setTotalPages] = useState(1);\n  const [showFilters, setShowFilters] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchSubmissions();\n    }\n  }, [isAuthenticated, filters, pagination]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchSubmissions = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const queryParams = new URLSearchParams({\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n        sortBy: pagination.sortBy || 'submittedAt',\n        sortOrder: pagination.sortOrder || 'desc',\n      });\n\n      // Add filters to query params\n      if (filters.status?.length) {\n        filters.status.forEach(status => queryParams.append('status', status));\n      }\n      if (filters.serviceType?.length) {\n        filters.serviceType.forEach(type => queryParams.append('serviceType', type));\n      }\n      if (filters.priority?.length) {\n        filters.priority.forEach(priority => queryParams.append('priority', priority));\n      }\n      if (filters.search) {\n        queryParams.append('search', filters.search);\n      }\n      if (filters.dateRange) {\n        queryParams.append('dateStart', filters.dateRange.start);\n        queryParams.append('dateEnd', filters.dateRange.end);\n      }\n\n      const response = await fetch(`/api/admin/submissions?${queryParams}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSubmissions(data.data || []);\n        setTotalPages(data.pagination?.totalPages || 1);\n      }\n    } catch (error) {\n      console.error('Failed to fetch submissions:', error);\n    }\n  };\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setFilters({ ...filters, search: searchTerm });\n    setPagination({ ...pagination, page: 1 });\n  };\n\n  const handleStatusChange = async (id: string, status: string) => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/submissions/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ status })\n      });\n\n      if (response.ok) {\n        fetchSubmissions();\n      }\n    } catch (error) {\n      console.error('Failed to update status:', error);\n    }\n  };\n\n  const handleBulkAction = async (action: string) => {\n    if (selectedSubmissions.length === 0) return;\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      let updates = {};\n\n      switch (action) {\n        case 'mark-contacted':\n          updates = { status: 'contacted' };\n          break;\n        case 'mark-completed':\n          updates = { status: 'completed' };\n          break;\n        case 'delete':\n          // Handle bulk delete\n          break;\n        default:\n          return;\n      }\n\n      const response = await fetch('/api/admin/submissions', {\n        method: 'PATCH',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          ids: selectedSubmissions,\n          updates\n        })\n      });\n\n      if (response.ok) {\n        setSelectedSubmissions([]);\n        fetchSubmissions();\n      }\n    } catch (error) {\n      console.error('Failed to perform bulk action:', error);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },\n      contacted: { color: 'bg-blue-100 text-blue-800', text: '已联系' },\n      'in-progress': { color: 'bg-purple-100 text-purple-800', text: '处理中' },\n      completed: { color: 'bg-green-100 text-green-800', text: '已完成' },\n      closed: { color: 'bg-gray-100 text-gray-800', text: '已关闭' },\n    };\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      low: { color: 'bg-gray-100 text-gray-800', text: '低' },\n      medium: { color: 'bg-blue-100 text-blue-800', text: '中' },\n      high: { color: 'bg-orange-100 text-orange-800', text: '高' },\n      urgent: { color: 'bg-red-100 text-red-800', text: '紧急' },\n    };\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getServiceTypeName = (serviceType: string) => {\n    const types: { [key: string]: string } = {\n      'foreign_trade_lines': '外贸网络线路',\n      'ecommerce_lines': '跨境电商线路',\n      'vpn_services': 'VPN服务',\n      'custom_solution': '定制解决方案',\n    };\n    return types[serviceType] || serviceType;\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>客户咨询管理 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">客户咨询管理</h1>\n                <p className=\"text-gray-600\">管理和跟踪所有客户咨询</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Search and Filters */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"p-6\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n                {/* Search */}\n                <form onSubmit={handleSearch} className=\"flex-1 max-w-lg\">\n                  <div className=\"relative\">\n                    <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      placeholder=\"搜索公司名称、联系人、邮箱...\"\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                </form>\n\n                {/* Action Buttons */}\n                <div className=\"flex items-center space-x-3\">\n                  <button\n                    onClick={() => setShowFilters(!showFilters)}\n                    className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                  >\n                    <FunnelIcon className=\"h-4 w-4 mr-2\" />\n                    筛选\n                  </button>\n                  \n                  {selectedSubmissions.length > 0 && (\n                    <div className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={() => handleBulkAction('mark-contacted')}\n                        className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n                      >\n                        标记已联系\n                      </button>\n                      <button\n                        onClick={() => handleBulkAction('mark-completed')}\n                        className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700\"\n                      >\n                        标记完成\n                      </button>\n                    </div>\n                  )}\n                  \n                  <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\">\n                    <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                    导出\n                  </button>\n                </div>\n              </div>\n\n              {/* Filters Panel */}\n              {showFilters && (\n                <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                    {/* Status Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">状态</label>\n                      <select\n                        multiple\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                        onChange={(e) => {\n                          const values = Array.from(e.target.selectedOptions, option => option.value);\n                          setFilters({ ...filters, status: values });\n                        }}\n                      >\n                        <option value=\"pending\">待处理</option>\n                        <option value=\"contacted\">已联系</option>\n                        <option value=\"in-progress\">处理中</option>\n                        <option value=\"completed\">已完成</option>\n                        <option value=\"closed\">已关闭</option>\n                      </select>\n                    </div>\n\n                    {/* Service Type Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">服务类型</label>\n                      <select\n                        multiple\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                        onChange={(e) => {\n                          const values = Array.from(e.target.selectedOptions, option => option.value);\n                          setFilters({ ...filters, serviceType: values });\n                        }}\n                      >\n                        <option value=\"foreign_trade_lines\">外贸网络线路</option>\n                        <option value=\"ecommerce_lines\">跨境电商线路</option>\n                        <option value=\"vpn_services\">VPN服务</option>\n                        <option value=\"custom_solution\">定制解决方案</option>\n                      </select>\n                    </div>\n\n                    {/* Priority Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">优先级</label>\n                      <select\n                        multiple\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                        onChange={(e) => {\n                          const values = Array.from(e.target.selectedOptions, option => option.value);\n                          setFilters({ ...filters, priority: values });\n                        }}\n                      >\n                        <option value=\"low\">低</option>\n                        <option value=\"medium\">中</option>\n                        <option value=\"high\">高</option>\n                        <option value=\"urgent\">紧急</option>\n                      </select>\n                    </div>\n\n                    {/* Date Range Filter */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">日期范围</label>\n                      <div className=\"space-y-2\">\n                        <input\n                          type=\"date\"\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                          onChange={(e) => {\n                            setFilters({\n                              ...filters,\n                              dateRange: {\n                                start: e.target.value,\n                                end: filters.dateRange?.end || ''\n                              }\n                            });\n                          }}\n                        />\n                        <input\n                          type=\"date\"\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                          onChange={(e) => {\n                            setFilters({\n                              ...filters,\n                              dateRange: {\n                                start: filters.dateRange?.start || '',\n                                end: e.target.value\n                              }\n                            });\n                          }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Submissions Table */}\n          <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedSubmissions.length === submissions.length && submissions.length > 0}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setSelectedSubmissions(submissions.map(s => s.id));\n                          } else {\n                            setSelectedSubmissions([]);\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                      />\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      公司信息\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      服务类型\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      优先级\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      提交时间\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      操作\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {submissions.map((submission) => (\n                    <tr key={submission.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedSubmissions.includes(submission.id)}\n                          onChange={(e) => {\n                            if (e.target.checked) {\n                              setSelectedSubmissions([...selectedSubmissions, submission.id]);\n                            } else {\n                              setSelectedSubmissions(selectedSubmissions.filter(id => id !== submission.id));\n                            }\n                          }}\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">{submission.companyName}</div>\n                          <div className=\"text-sm text-gray-500\">{submission.contactPerson}</div>\n                          <div className=\"text-sm text-gray-500\">{submission.email}</div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span className=\"text-sm text-gray-900\">\n                          {getServiceTypeName(submission.serviceType)}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {getStatusBadge(submission.status)}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {getPriorityBadge(submission.priority)}\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\n                        {new Date(submission.submittedAt).toLocaleDateString('zh-CN')}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-2\">\n                          <button\n                            onClick={() => router.push(`/admin/inquiries/${submission.id}`)}\n                            className=\"text-blue-600 hover:text-blue-900\"\n                            title=\"查看详情\"\n                          >\n                            <EyeIcon className=\"h-4 w-4\" />\n                          </button>\n                          <button\n                            onClick={() => router.push(`/admin/inquiries/${submission.id}/edit`)}\n                            className=\"text-green-600 hover:text-green-900\"\n                            title=\"编辑\"\n                          >\n                            <PencilIcon className=\"h-4 w-4\" />\n                          </button>\n                          <div className=\"relative\">\n                            <select\n                              value={submission.status}\n                              onChange={(e) => handleStatusChange(submission.id, e.target.value)}\n                              className=\"text-xs border border-gray-300 rounded px-2 py-1\"\n                            >\n                              <option value=\"pending\">待处理</option>\n                              <option value=\"contacted\">已联系</option>\n                              <option value=\"in-progress\">处理中</option>\n                              <option value=\"completed\">已完成</option>\n                              <option value=\"closed\">已关闭</option>\n                            </select>\n                          </div>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Pagination */}\n            <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n              <div className=\"flex-1 flex justify-between sm:hidden\">\n                <button\n                  onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}\n                  disabled={pagination.page === 1}\n                  className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  上一页\n                </button>\n                <button\n                  onClick={() => setPagination({ ...pagination, page: Math.min(totalPages, pagination.page + 1) })}\n                  disabled={pagination.page === totalPages}\n                  className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  下一页\n                </button>\n              </div>\n              <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                <div>\n                  <p className=\"text-sm text-gray-700\">\n                    显示第 <span className=\"font-medium\">{(pagination.page - 1) * pagination.limit + 1}</span> 到{' '}\n                    <span className=\"font-medium\">\n                      {Math.min(pagination.page * pagination.limit, submissions.length)}\n                    </span>{' '}\n                    条记录\n                  </p>\n                </div>\n                <div>\n                  <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                    <button\n                      onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}\n                      disabled={pagination.page === 1}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      上一页\n                    </button>\n                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (\n                      <button\n                        key={page}\n                        onClick={() => setPagination({ ...pagination, page })}\n                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                          page === pagination.page\n                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\n                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                        }`}\n                      >\n                        {page}\n                      </button>\n                    ))}\n                    <button\n                      onClick={() => setPagination({ ...pagination, page: Math.min(totalPages, pagination.page + 1) })}\n                      disabled={pagination.page === totalPages}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      下一页\n                    </button>\n                  </nav>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAae,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACtE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAoB;QAC7D,MAAM;QACN,OAAO;QACP,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,iBAAiB;gBACnB;YACF;QACF;kCAAG;QAAC;QAAiB;QAAS;KAAW;IAEzC,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,IAAI;gBAUE,iBAGA,sBAGA;YAfJ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,cAAc,IAAI,gBAAgB;gBACtC,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAChC,QAAQ,WAAW,MAAM,IAAI;gBAC7B,WAAW,WAAW,SAAS,IAAI;YACrC;YAEA,8BAA8B;YAC9B,KAAI,kBAAA,QAAQ,MAAM,cAAd,sCAAA,gBAAgB,MAAM,EAAE;gBAC1B,QAAQ,MAAM,CAAC,OAAO,CAAC,CAAA,SAAU,YAAY,MAAM,CAAC,UAAU;YAChE;YACA,KAAI,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,MAAM,EAAE;gBAC/B,QAAQ,WAAW,CAAC,OAAO,CAAC,CAAA,OAAQ,YAAY,MAAM,CAAC,eAAe;YACxE;YACA,KAAI,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,MAAM,EAAE;gBAC5B,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,WAAY,YAAY,MAAM,CAAC,YAAY;YACtE;YACA,IAAI,QAAQ,MAAM,EAAE;gBAClB,YAAY,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC7C;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,YAAY,MAAM,CAAC,aAAa,QAAQ,SAAS,CAAC,KAAK;gBACvD,YAAY,MAAM,CAAC,WAAW,QAAQ,SAAS,CAAC,GAAG;YACrD;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,0BAAqC,OAAZ,cAAe;gBACpE,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;oBAGD;gBAFd,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,IAAI,IAAI,EAAE;gBAC9B,cAAc,EAAA,mBAAA,KAAK,UAAU,cAAf,uCAAA,iBAAiB,UAAU,KAAI;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAW;QAC5C,cAAc;YAAE,GAAG,UAAU;YAAE,MAAM;QAAE;IACzC;IAEA,MAAM,qBAAqB,OAAO,IAAY;QAC5C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,0BAA4B,OAAH,KAAM;gBAC3D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;oBAC3B,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,oBAAoB,MAAM,KAAK,GAAG;QAEtC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,UAAU,CAAC;YAEf,OAAQ;gBACN,KAAK;oBACH,UAAU;wBAAE,QAAQ;oBAAY;oBAChC;gBACF,KAAK;oBACH,UAAU;wBAAE,QAAQ;oBAAY;oBAChC;gBACF,KAAK;oBAEH;gBACF;oBACE;YACJ;YAEA,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;oBAC3B,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK;oBACL;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,uBAAuB,EAAE;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YAC/D,WAAW;gBAAE,OAAO;gBAA6B,MAAM;YAAM;YAC7D,eAAe;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YACrE,WAAW;gBAAE,OAAO;gBAA+B,MAAM;YAAM;YAC/D,QAAQ;gBAAE,OAAO;gBAA6B,MAAM;YAAM;QAC5D;QACA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBACE,0JAAC;YAAK,WAAW,AAAC,2EAAuF,OAAb,OAAO,KAAK;sBACrG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAAiB;YACrB,KAAK;gBAAE,OAAO;gBAA6B,MAAM;YAAI;YACrD,QAAQ;gBAAE,OAAO;gBAA6B,MAAM;YAAI;YACxD,MAAM;gBAAE,OAAO;gBAAiC,MAAM;YAAI;YAC1D,QAAQ;gBAAE,OAAO;gBAA2B,MAAM;YAAK;QACzD;QACA,MAAM,SAAS,cAAc,CAAC,SAAwC,IAAI,eAAe,MAAM;QAC/F,qBACE,0JAAC;YAAK,WAAW,AAAC,2EAAuF,OAAb,OAAO,KAAK;sBACrG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAmC;YACvC,uBAAuB;YACvB,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;QACrB;QACA,OAAO,KAAK,CAAC,YAAY,IAAI;IAC/B;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,0JAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DAEb,0JAAC;oDAAK,UAAU;oDAAc,WAAU;8DACtC,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC,iOAAA,CAAA,sBAAmB;gEAAC,WAAU;;;;;;0EAC/B,0JAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;8DAMhB,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DACC,SAAS,IAAM,eAAe,CAAC;4DAC/B,WAAU;;8EAEV,0JAAC,+MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAIxC,oBAAoB,MAAM,GAAG,mBAC5B,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EACX;;;;;;8EAGD,0JAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EACX;;;;;;;;;;;;sEAML,0JAAC;4DAAO,WAAU;;8EAChB,0JAAC,6NAAA,CAAA,oBAAiB;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;wCAOnD,6BACC,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;;kEAEb,0JAAC;;0EACC,0JAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,0JAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,UAAU,CAAC;oEACT,MAAM,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;oEAC1E,WAAW;wEAAE,GAAG,OAAO;wEAAE,QAAQ;oEAAO;gEAC1C;;kFAEA,0JAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,0JAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,0JAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,0JAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,0JAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAK3B,0JAAC;;0EACC,0JAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,0JAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,UAAU,CAAC;oEACT,MAAM,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;oEAC1E,WAAW;wEAAE,GAAG,OAAO;wEAAE,aAAa;oEAAO;gEAC/C;;kFAEA,0JAAC;wEAAO,OAAM;kFAAsB;;;;;;kFACpC,0JAAC;wEAAO,OAAM;kFAAkB;;;;;;kFAChC,0JAAC;wEAAO,OAAM;kFAAe;;;;;;kFAC7B,0JAAC;wEAAO,OAAM;kFAAkB;;;;;;;;;;;;;;;;;;kEAKpC,0JAAC;;0EACC,0JAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,0JAAC;gEACC,QAAQ;gEACR,WAAU;gEACV,UAAU,CAAC;oEACT,MAAM,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;oEAC1E,WAAW;wEAAE,GAAG,OAAO;wEAAE,UAAU;oEAAO;gEAC5C;;kFAEA,0JAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,0JAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,0JAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,0JAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAK3B,0JAAC;;0EACC,0JAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEACC,MAAK;wEACL,WAAU;wEACV,UAAU,CAAC;gFAKA;4EAJT,WAAW;gFACT,GAAG,OAAO;gFACV,WAAW;oFACT,OAAO,EAAE,MAAM,CAAC,KAAK;oFACrB,KAAK,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,GAAG,KAAI;gFACjC;4EACF;wEACF;;;;;;kFAEF,0JAAC;wEACC,MAAK;wEACL,WAAU;wEACV,UAAU,CAAC;gFAIE;4EAHX,WAAW;gFACT,GAAG,OAAO;gFACV,WAAW;oFACT,OAAO,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,KAAK,KAAI;oFACnC,KAAK,EAAE,MAAM,CAAC,KAAK;gFACrB;4EACF;wEACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWhB,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAM,WAAU;;8DACf,0JAAC;oDAAM,WAAU;8DACf,cAAA,0JAAC;;0EACC,0JAAC;gEAAG,WAAU;0EACZ,cAAA,0JAAC;oEACC,MAAK;oEACL,SAAS,oBAAoB,MAAM,KAAK,YAAY,MAAM,IAAI,YAAY,MAAM,GAAG;oEACnF,UAAU,CAAC;wEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4EACpB,uBAAuB,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;wEAClD,OAAO;4EACL,uBAAuB,EAAE;wEAC3B;oEACF;oEACA,WAAU;;;;;;;;;;;0EAGd,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,0JAAC;oDAAM,WAAU;8DACd,YAAY,GAAG,CAAC,CAAC,2BAChB,0JAAC;4DAAuB,WAAU;;8EAChC,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEACC,MAAK;wEACL,SAAS,oBAAoB,QAAQ,CAAC,WAAW,EAAE;wEACnD,UAAU,CAAC;4EACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gFACpB,uBAAuB;uFAAI;oFAAqB,WAAW,EAAE;iFAAC;4EAChE,OAAO;gFACL,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,KAAM,OAAO,WAAW,EAAE;4EAC9E;wEACF;wEACA,WAAU;;;;;;;;;;;8EAGd,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;;0FACC,0JAAC;gFAAI,WAAU;0FAAqC,WAAW,WAAW;;;;;;0FAC1E,0JAAC;gFAAI,WAAU;0FAAyB,WAAW,aAAa;;;;;;0FAChE,0JAAC;gFAAI,WAAU;0FAAyB,WAAW,KAAK;;;;;;;;;;;;;;;;;8EAG5D,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAK,WAAU;kFACb,mBAAmB,WAAW,WAAW;;;;;;;;;;;8EAG9C,0JAAC;oEAAG,WAAU;8EACX,eAAe,WAAW,MAAM;;;;;;8EAEnC,0JAAC;oEAAG,WAAU;8EACX,iBAAiB,WAAW,QAAQ;;;;;;8EAEvC,0JAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,WAAW,WAAW,EAAE,kBAAkB,CAAC;;;;;;8EAEvD,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFACC,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,oBAAiC,OAAd,WAAW,EAAE;gFAC5D,WAAU;gFACV,OAAM;0FAEN,cAAA,0JAAC,yMAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;;;;;;0FAErB,0JAAC;gFACC,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,oBAAiC,OAAd,WAAW,EAAE,EAAC;gFAC7D,WAAU;gFACV,OAAM;0FAEN,cAAA,0JAAC,+MAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;;;;;;0FAExB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFACC,OAAO,WAAW,MAAM;oFACxB,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oFACjE,WAAU;;sGAEV,0JAAC;4FAAO,OAAM;sGAAU;;;;;;sGACxB,0JAAC;4FAAO,OAAM;sGAAY;;;;;;sGAC1B,0JAAC;4FAAO,OAAM;sGAAc;;;;;;sGAC5B,0JAAC;4FAAO,OAAM;sGAAY;;;;;;sGAC1B,0JAAC;4FAAO,OAAM;sGAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2DA9DxB,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;kDA0E9B,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDACC,SAAS,IAAM,cAAc;gEAAE,GAAG,UAAU;gEAAE,MAAM,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,GAAG;4DAAG;wDACrF,UAAU,WAAW,IAAI,KAAK;wDAC9B,WAAU;kEACX;;;;;;kEAGD,0JAAC;wDACC,SAAS,IAAM,cAAc;gEAAE,GAAG,UAAU;gEAAE,MAAM,KAAK,GAAG,CAAC,YAAY,WAAW,IAAI,GAAG;4DAAG;wDAC9F,UAAU,WAAW,IAAI,KAAK;wDAC9B,WAAU;kEACX;;;;;;;;;;;;0DAIH,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;kEACC,cAAA,0JAAC;4DAAE,WAAU;;gEAAwB;8EAC/B,0JAAC;oEAAK,WAAU;8EAAe,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAG;;;;;;gEAAS;gEAAG;8EAC1F,0JAAC;oEAAK,WAAU;8EACb,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,YAAY,MAAM;;;;;;gEAC1D;gEAAI;;;;;;;;;;;;kEAIhB,0JAAC;kEACC,cAAA,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEACC,SAAS,IAAM,cAAc;4EAAE,GAAG,UAAU;4EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,GAAG;wEAAG;oEACrF,UAAU,WAAW,IAAI,KAAK;oEAC9B,WAAU;8EACX;;;;;;gEAGA,MAAM,IAAI,CAAC;oEAAE,QAAQ;gEAAW,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,qBACxD,0JAAC;wEAEC,SAAS,IAAM,cAAc;gFAAE,GAAG,UAAU;gFAAE;4EAAK;wEACnD,WAAW,AAAC,0EAIX,OAHC,SAAS,WAAW,IAAI,GACpB,kDACA;kFAGL;uEARI;;;;;8EAWT,0JAAC;oEACC,SAAS,IAAM,cAAc;4EAAE,GAAG,UAAU;4EAAE,MAAM,KAAK,GAAG,CAAC,YAAY,WAAW,IAAI,GAAG;wEAAG;oEAC9F,UAAU,WAAW,IAAI,KAAK;oEAC9B,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GAljBwB;;QAeP,0HAAA,CAAA,YAAS;;;KAfF", "debugId": null}}, {"offset": {"line": 1756, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/inquiries\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}