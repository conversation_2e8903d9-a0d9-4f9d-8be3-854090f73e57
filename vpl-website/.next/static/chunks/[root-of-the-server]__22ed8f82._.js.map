{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/lib/notifications.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nclass NotificationService {\n  private socket: Socket | null = null;\n  private isConnected = false;\n  private connectionAttempts = 0;\n  private maxRetries = 3;\n\n  connect() {\n    if (typeof window !== 'undefined' && !this.socket && this.connectionAttempts < this.maxRetries) {\n      try {\n        this.connectionAttempts++;\n        console.log(`Attempting to connect to Socket.IO server (attempt ${this.connectionAttempts})`);\n\n        this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {\n          path: '/api/socket',\n          transports: ['polling', 'websocket'],\n          timeout: 5000,\n          forceNew: true,\n        });\n\n        this.socket.on('connect', () => {\n          console.log('Connected to notification service');\n          this.isConnected = true;\n          this.connectionAttempts = 0; // Reset on successful connection\n        });\n\n        this.socket.on('disconnect', () => {\n          console.log('Disconnected from notification service');\n          this.isConnected = false;\n        });\n\n        this.socket.on('connect_error', (error) => {\n          console.warn('Socket.IO connection error:', error.message);\n          this.isConnected = false;\n\n          if (this.connectionAttempts >= this.maxRetries) {\n            console.warn('Max connection attempts reached. Socket.IO features will be disabled.');\n          }\n        });\n\n        this.socket.on('admin-joined', (data) => {\n          console.log('Successfully joined admin room:', data);\n        });\n\n      } catch (error) {\n        console.error('Failed to initialize Socket.IO:', error);\n        this.isConnected = false;\n      }\n    }\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n    }\n  }\n\n  joinAdminRoom() {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('join-admin');\n    } else {\n      console.warn('Cannot join admin room: Socket.IO not connected');\n    }\n  }\n\n  onNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.on('new-submission', callback);\n    } else {\n      console.warn('Cannot listen for new submissions: Socket.IO not available');\n    }\n  }\n\n  offNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.off('new-submission', callback);\n    }\n  }\n\n  emitNewSubmission(data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('new-submission', data);\n    } else {\n      console.warn('Cannot emit new submission: Socket.IO not connected');\n    }\n  }\n\n  // Admin notification methods\n  notifyAdmins(type: string, data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.to('admin').emit('admin-notification', {\n        type,\n        data,\n        timestamp: new Date().toISOString(),\n      });\n    } else {\n      console.warn('Cannot notify admins: Socket.IO not connected');\n    }\n  }\n\n  onAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.on('admin-notification', callback);\n    } else {\n      console.warn('Cannot listen for admin notifications: Socket.IO not available');\n    }\n  }\n\n  offAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.off('admin-notification', callback);\n    }\n  }\n\n  // Check if service is available\n  isAvailable(): boolean {\n    return this.socket !== null && this.isConnected;\n  }\n\n  // Get connection status\n  getStatus(): string {\n    if (!this.socket) return 'not-initialized';\n    if (this.isConnected) return 'connected';\n    return 'disconnected';\n  }\n}\n\nexport const notificationService = new NotificationService();\n\n// Browser notification utilities\nexport const requestNotificationPermission = async (): Promise<boolean> => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return false;\n  }\n\n  if (Notification.permission === 'granted') {\n    return true;\n  }\n\n  if (Notification.permission === 'denied') {\n    return false;\n  }\n\n  const permission = await Notification.requestPermission();\n  return permission === 'granted';\n};\n\nexport const showBrowserNotification = (title: string, options?: NotificationOptions) => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return;\n  }\n\n  if (Notification.permission === 'granted') {\n    new Notification(title, {\n      icon: '/favicon.ico',\n      badge: '/favicon.ico',\n      ...options,\n    });\n  }\n};\n\nexport default NotificationService;\n"], "names": [], "mappings": ";;;;;;AAcyB;;AAdzB;AAAA;;;AAEA,MAAM;IAMJ,UAAU;QACR,IAAI,aAAkB,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,EAAE;YAC9F,IAAI;gBACF,IAAI,CAAC,kBAAkB;gBACvB,QAAQ,GAAG,CAAC,AAAC,sDAA6E,OAAxB,IAAI,CAAC,kBAAkB,EAAC;gBAE1F,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,2KAAA,CAAA,KAAE,AAAD,EAAE,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE;oBAC7E,MAAM;oBACN,YAAY;wBAAC;wBAAW;qBAAY;oBACpC,SAAS;oBACT,UAAU;gBACZ;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;oBACxB,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;oBACnB,IAAI,CAAC,kBAAkB,GAAG,GAAG,iCAAiC;gBAChE;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;gBACrB;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;oBAC/B,QAAQ,IAAI,CAAC,+BAA+B,MAAM,OAAO;oBACzD,IAAI,CAAC,WAAW,GAAG;oBAEnB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,EAAE;wBAC9C,QAAQ,IAAI,CAAC;oBACf;gBACF;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC;oBAC9B,QAAQ,GAAG,CAAC,mCAAmC;gBACjD;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,IAAI,CAAC,WAAW,GAAG;YACrB;QACF;IACF;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,gBAAgB;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,gBAAgB,QAA6B,EAAE;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB;QACnC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,iBAAiB,QAA6B,EAAE;QAC9C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB;QACpC;IACF;IAEA,kBAAkB,IAAS,EAAE;QAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;QACrC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,6BAA6B;IAC7B,aAAa,IAAY,EAAE,IAAS,EAAE;QACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,sBAAsB;gBACjD;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,oBAAoB,QAAqC,EAAE;QACzD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,sBAAsB;QACvC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,qBAAqB,QAAqC,EAAE;QAC1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB;QACxC;IACF;IAEA,gCAAgC;IAChC,cAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,WAAW;IACjD;IAEA,wBAAwB;IACxB,YAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO;QAC7B,OAAO;IACT;;QA5HA,wKAAQ,UAAwB;QAChC,wKAAQ,eAAc;QACtB,wKAAQ,sBAAqB;QAC7B,wKAAQ,cAAa;;AA0HvB;AAEO,MAAM,sBAAsB,IAAI;AAGhC,MAAM,gCAAgC;IAC3C,IAAI,aAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE,OAAO;IACT;IAEA,IAAI,aAAa,UAAU,KAAK,WAAW;QACzC,OAAO;IACT;IAEA,IAAI,aAAa,UAAU,KAAK,UAAU;QACxC,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,aAAa,iBAAiB;IACvD,OAAO,eAAe;AACxB;AAEO,MAAM,0BAA0B,CAAC,OAAe;IACrD,IAAI,aAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE;IACF;IAEA,IAAI,aAAa,UAAU,KAAK,WAAW;QACzC,IAAI,aAAa,OAAO;YACtB,MAAM;YACN,OAAO;YACP,GAAG,OAAO;QACZ;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/NotificationCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { BellIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { notificationService, showBrowserNotification, requestNotificationPermission } from '../../lib/notifications';\n\ninterface Notification {\n  id: string;\n  type: 'new-submission' | 'system' | 'warning';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n\nexport default function NotificationCenter() {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [connectionStatus, setConnectionStatus] = useState<string>('not-initialized');\n\n  useEffect(() => {\n    // Initialize notification service with error handling\n    try {\n      notificationService.connect();\n\n      // Check connection status periodically\n      const statusInterval = setInterval(() => {\n        setConnectionStatus(notificationService.getStatus());\n      }, 2000);\n\n      // Try to join admin room after a short delay\n      setTimeout(() => {\n        notificationService.joinAdminRoom();\n      }, 1000);\n\n      // Request browser notification permission\n      requestNotificationPermission();\n\n      return () => {\n        clearInterval(statusInterval);\n      };\n    } catch (error) {\n      console.error('Failed to initialize notification service:', error);\n      setConnectionStatus('error');\n    }\n\n    // Listen for new submissions\n    const handleNewSubmission = (data: any) => {\n      const notification: Notification = {\n        id: Date.now().toString(),\n        type: 'new-submission',\n        title: '新的客户咨询',\n        message: `${data.companyName} 提交了新的咨询`,\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n\n      setNotifications(prev => [notification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(notification.title, {\n        body: notification.message,\n        tag: 'new-submission',\n      });\n    };\n\n    // Listen for admin notifications\n    const handleAdminNotification = (notification: any) => {\n      const newNotification: Notification = {\n        id: Date.now().toString(),\n        type: notification.type,\n        title: notification.title || '系统通知',\n        message: notification.message,\n        timestamp: notification.timestamp,\n        read: false,\n      };\n\n      setNotifications(prev => [newNotification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(newNotification.title, {\n        body: newNotification.message,\n        tag: notification.type,\n      });\n    };\n\n    notificationService.onNewSubmission(handleNewSubmission);\n    notificationService.onAdminNotification(handleAdminNotification);\n\n    return () => {\n      notificationService.offNewSubmission(handleNewSubmission);\n      notificationService.offAdminNotification(handleAdminNotification);\n      notificationService.disconnect();\n    };\n  }, []);\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id\n          ? { ...notification, read: true }\n          : notification\n      )\n    );\n    setUnreadCount(prev => Math.max(0, prev - 1));\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n    setUnreadCount(0);\n  };\n\n  const removeNotification = (id: string) => {\n    const notification = notifications.find(n => n.id === id);\n    if (notification && !notification.read) {\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    }\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'new-submission':\n        return '📧';\n      case 'system':\n        return '⚙️';\n      case 'warning':\n        return '⚠️';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) {\n      return '刚刚';\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes}分钟前`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)}小时前`;\n    } else {\n      return date.toLocaleDateString('zh-CN');\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\"\n      >\n        <BellIcon className=\"h-6 w-6\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n        {/* Connection status indicator */}\n        <span className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full ${\n          connectionStatus === 'connected' ? 'bg-green-500' :\n          connectionStatus === 'disconnected' ? 'bg-yellow-500' :\n          'bg-gray-400'\n        }`} title={`Socket.IO: ${connectionStatus}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 max-h-96 overflow-hidden\">\n            <div className=\"p-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-medium text-gray-900\">通知</h3>\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-500\"\n                  >\n                    全部标记为已读\n                  </button>\n                )}\n              </div>\n              {/* Connection status */}\n              <div className=\"mt-2 text-xs text-gray-500\">\n                实时通知: {\n                  connectionStatus === 'connected' ? '🟢 已连接' :\n                  connectionStatus === 'disconnected' ? '🟡 连接中断' :\n                  connectionStatus === 'error' ? '🔴 连接错误' :\n                  '⚪ 未连接'\n                }\n              </div>\n            </div>\n            \n            <div className=\"max-h-64 overflow-y-auto\">\n              {notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  暂无通知\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${\n                      !notification.read ? 'bg-blue-50' : ''\n                    }`}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-start space-x-3 flex-1\">\n                        <span className=\"text-lg\">\n                          {getNotificationIcon(notification.type)}\n                        </span>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {notification.title}\n                          </p>\n                          <p className=\"text-sm text-gray-600 mt-1\">\n                            {notification.message}\n                          </p>\n                          <p className=\"text-xs text-gray-400 mt-1\">\n                            {formatTimestamp(notification.timestamp)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {!notification.read && (\n                          <button\n                            onClick={() => markAsRead(notification.id)}\n                            className=\"w-2 h-2 bg-blue-500 rounded-full\"\n                            title=\"标记为已读\"\n                          />\n                        )}\n                        <button\n                          onClick={() => removeNotification(notification.id)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAee,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;wCAAE;YACR,sDAAsD;YACtD,IAAI;gBACF,uHAAA,CAAA,sBAAmB,CAAC,OAAO;gBAE3B,uCAAuC;gBACvC,MAAM,iBAAiB;mEAAY;wBACjC,oBAAoB,uHAAA,CAAA,sBAAmB,CAAC,SAAS;oBACnD;kEAAG;gBAEH,6CAA6C;gBAC7C;oDAAW;wBACT,uHAAA,CAAA,sBAAmB,CAAC,aAAa;oBACnC;mDAAG;gBAEH,0CAA0C;gBAC1C,CAAA,GAAA,uHAAA,CAAA,gCAA6B,AAAD;gBAE5B;oDAAO;wBACL,cAAc;oBAChB;;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8CAA8C;gBAC5D,oBAAoB;YACtB;YAEA,6BAA6B;YAC7B,MAAM;oEAAsB,CAAC;oBAC3B,MAAM,eAA6B;wBACjC,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM;wBACN,OAAO;wBACP,SAAS,AAAC,GAAmB,OAAjB,KAAK,WAAW,EAAC;wBAC7B,WAAW,IAAI,OAAO,WAAW;wBACjC,MAAM;oBACR;oBAEA;4EAAiB,CAAA,OAAQ;gCAAC;mCAAiB;6BAAK;;oBAChD;4EAAe,CAAA,OAAQ,OAAO;;oBAE9B,4BAA4B;oBAC5B,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa,KAAK,EAAE;wBAC1C,MAAM,aAAa,OAAO;wBAC1B,KAAK;oBACP;gBACF;;YAEA,iCAAiC;YACjC,MAAM;wEAA0B,CAAC;oBAC/B,MAAM,kBAAgC;wBACpC,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM,aAAa,IAAI;wBACvB,OAAO,aAAa,KAAK,IAAI;wBAC7B,SAAS,aAAa,OAAO;wBAC7B,WAAW,aAAa,SAAS;wBACjC,MAAM;oBACR;oBAEA;gFAAiB,CAAA,OAAQ;gCAAC;mCAAoB;6BAAK;;oBACnD;gFAAe,CAAA,OAAQ,OAAO;;oBAE9B,4BAA4B;oBAC5B,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE,gBAAgB,KAAK,EAAE;wBAC7C,MAAM,gBAAgB,OAAO;wBAC7B,KAAK,aAAa,IAAI;oBACxB;gBACF;;YAEA,uHAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;YACpC,uHAAA,CAAA,sBAAmB,CAAC,mBAAmB,CAAC;YAExC;gDAAO;oBACL,uHAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC;oBACrC,uHAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;oBACzC,uHAAA,CAAA,sBAAmB,CAAC,UAAU;gBAChC;;QACF;uCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAC9B;QAGR,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IAC5C;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;QAE3D,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,gBAAgB,CAAC,aAAa,IAAI,EAAE;YACtC,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C;QACA,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,AAAC,GAAgB,OAAd,eAAc;QAC1B,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,AAAC,GAAiC,OAA/B,KAAK,KAAK,CAAC,gBAAgB,KAAI;QAC3C,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,0JAAC,2MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBACnB,cAAc,mBACb,0JAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;kCAIhC,0JAAC;wBAAK,WAAW,AAAC,oDAIjB,OAHC,qBAAqB,cAAc,iBACnC,qBAAqB,iBAAiB,kBACtC;wBACE,OAAO,AAAC,cAA8B,OAAjB;;;;;;;;;;;;YAG1B,wBACC;;kCACE,0JAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,cAAc,mBACb,0JAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAML,0JAAC;wCAAI,WAAU;;4CAA6B;4CAExC,qBAAqB,cAAc,WACnC,qBAAqB,iBAAiB,YACtC,qBAAqB,UAAU,YAC/B;;;;;;;;;;;;;0CAKN,0JAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,0JAAC;oCAAI,WAAU;8CAAgC;;;;;2CAI/C,cAAc,GAAG,CAAC,CAAC,6BACjB,0JAAC;wCAEC,WAAW,AAAC,iDAEX,OADC,CAAC,aAAa,IAAI,GAAG,eAAe;kDAGtC,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAK,WAAU;sEACb,oBAAoB,aAAa,IAAI;;;;;;sEAExC,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAE,WAAU;8EACV,aAAa,KAAK;;;;;;8EAErB,0JAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,0JAAC;oEAAE,WAAU;8EACV,gBAAgB,aAAa,SAAS;;;;;;;;;;;;;;;;;;8DAI7C,0JAAC;oDAAI,WAAU;;wDACZ,CAAC,aAAa,IAAI,kBACjB,0JAAC;4DACC,SAAS,IAAM,WAAW,aAAa,EAAE;4DACzC,WAAU;4DACV,OAAM;;;;;;sEAGV,0JAAC;4DACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,WAAU;sEAEV,cAAA,0JAAC,6MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAlCtB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AA+CxC;GApPwB;KAAA", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/dashboard.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport Link from 'next/link';\nimport {\n  ChartBarIcon,\n  EnvelopeIcon,\n  UsersIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  BellIcon,\n  DocumentTextIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ArrowTopRightOnSquareIcon\n} from '@heroicons/react/24/outline';\nimport NotificationCenter from '../../components/admin/NotificationCenter';\n\ninterface DashboardStats {\n  totalInquiries: number;\n  todayInquiries: number;\n  pendingInquiries: number;\n  totalUsers: number;\n}\n\ninterface ContactSubmission {\n  id: string;\n  companyName: string;\n  contactPerson: string;\n  email: string;\n  phone: string;\n  serviceType: string;\n  message: string;\n  submittedAt: string;\n  status: 'pending' | 'contacted' | 'closed';\n}\n\nexport default function AdminDashboard() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [stats, setStats] = useState<DashboardStats>({\n    totalInquiries: 0,\n    todayInquiries: 0,\n    pendingInquiries: 0,\n    totalUsers: 0,\n  });\n  const [recentSubmissions, setRecentSubmissions] = useState<ContactSubmission[]>([]);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n    if (isAuthenticated) {\n      fetchDashboardData();\n    }\n  }, [isAuthenticated]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    // Verify token with backend\n    fetch('/api/admin/verify', {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    })\n    .then(response => {\n      if (response.ok) {\n        setIsAuthenticated(true);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setStats(data.stats);\n        setRecentSubmissions(data.recentSubmissions);\n      }\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const getServiceTypeName = (serviceType: string) => {\n    const types: { [key: string]: string } = {\n      'foreign_trade_lines': '外贸网络线路',\n      'ecommerce_lines': '跨境电商外网线路',\n      'vpn_services': 'VPN服务',\n      'custom_solution': '定制解决方案',\n    };\n    return types[serviceType] || serviceType;\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },\n      contacted: { color: 'bg-blue-100 text-blue-800', text: '已联系' },\n      closed: { color: 'bg-green-100 text-green-800', text: '已完成' },\n    };\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>管理后台 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3\">\n                  <span className=\"text-white font-bold text-lg\">VPL</span>\n                </div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">管理后台</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <NotificationCenter />\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-700 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-1\" />\n                  退出登录\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content */}\n        <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <EnvelopeIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">总咨询数</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.totalInquiries}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <ChartBarIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">今日咨询</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.todayInquiries}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <BellIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">待处理</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.pendingInquiries}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <UsersIcon className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">总用户数</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.totalUsers}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Access */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-lg font-medium text-gray-900 mb-4\">快速访问</h2>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n              <Link\n                href=\"/admin/users\"\n                className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow\"\n              >\n                <div>\n                  <span className=\"rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white\">\n                    <UsersIcon className=\"h-6 w-6\" />\n                  </span>\n                </div>\n                <div className=\"mt-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">\n                    <span className=\"absolute inset-0\" />\n                    用户管理\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    管理管理员账户、角色权限和登录安全\n                  </p>\n                </div>\n                <span className=\"pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400\">\n                  <ArrowTopRightOnSquareIcon className=\"h-6 w-6\" />\n                </span>\n              </Link>\n\n              <Link\n                href=\"/admin/analytics\"\n                className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow\"\n              >\n                <div>\n                  <span className=\"rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white\">\n                    <ChartBarIcon className=\"h-6 w-6\" />\n                  </span>\n                </div>\n                <div className=\"mt-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">\n                    <span className=\"absolute inset-0\" />\n                    数据分析\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    查看咨询统计、转化率和业务报表\n                  </p>\n                </div>\n                <span className=\"pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400\">\n                  <ArrowTopRightOnSquareIcon className=\"h-6 w-6\" />\n                </span>\n              </Link>\n\n              <Link\n                href=\"/admin/system/config\"\n                className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow\"\n              >\n                <div>\n                  <span className=\"rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white\">\n                    <CogIcon className=\"h-6 w-6\" />\n                  </span>\n                </div>\n                <div className=\"mt-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">\n                    <span className=\"absolute inset-0\" />\n                    系统配置\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    配置网站基本信息、邮件服务和安全设置\n                  </p>\n                </div>\n                <span className=\"pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400\">\n                  <ArrowTopRightOnSquareIcon className=\"h-6 w-6\" />\n                </span>\n              </Link>\n\n              <Link\n                href=\"/admin/content/services\"\n                className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow\"\n              >\n                <div>\n                  <span className=\"rounded-lg inline-flex p-3 bg-orange-50 text-orange-700 ring-4 ring-white\">\n                    <DocumentTextIcon className=\"h-6 w-6\" />\n                  </span>\n                </div>\n                <div className=\"mt-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">\n                    <span className=\"absolute inset-0\" />\n                    内容管理\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    管理服务类型、页面内容和多语言文本\n                  </p>\n                </div>\n                <span className=\"pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400\">\n                  <ArrowTopRightOnSquareIcon className=\"h-6 w-6\" />\n                </span>\n              </Link>\n            </div>\n          </div>\n\n          {/* Recent Submissions */}\n          <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n            <div className=\"px-4 py-5 sm:px-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900\">最近的咨询</h3>\n              <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">最新提交的客户咨询信息</p>\n            </div>\n            <ul className=\"divide-y divide-gray-200\">\n              {recentSubmissions.map((submission) => (\n                <li key={submission.id}>\n                  <div className=\"px-4 py-4 sm:px-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-blue-600 font-medium text-sm\">\n                              {submission.companyName.charAt(0)}\n                            </span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"flex items-center\">\n                            <p className=\"text-sm font-medium text-gray-900\">\n                              {submission.companyName}\n                            </p>\n                            <span className=\"ml-2\">\n                              {getStatusBadge(submission.status)}\n                            </span>\n                          </div>\n                          <p className=\"text-sm text-gray-500\">\n                            {submission.contactPerson} • {submission.email}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {getServiceTypeName(submission.serviceType)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <p className=\"text-sm text-gray-500\">\n                          {new Date(submission.submittedAt).toLocaleDateString('zh-CN')}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-600 line-clamp-2\">\n                        {submission.message}\n                      </p>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n            {recentSubmissions.length === 0 && (\n              <div className=\"px-4 py-8 text-center\">\n                <p className=\"text-gray-500\">暂无咨询记录</p>\n              </div>\n            )}\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"mt-8 grid grid-cols-1 gap-5 sm:grid-cols-3\">\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <EnvelopeIcon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"ml-5\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">管理咨询</h3>\n                    <p className=\"text-sm text-gray-500\">查看和处理客户咨询</p>\n                  </div>\n                </div>\n                <div className=\"mt-3\">\n                  <button className=\"text-blue-600 hover:text-blue-500 text-sm font-medium\">\n                    查看全部 →\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <CogIcon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"ml-5\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">系统设置</h3>\n                    <p className=\"text-sm text-gray-500\">配置邮件和系统参数</p>\n                  </div>\n                </div>\n                <div className=\"mt-3\">\n                  <button className=\"text-blue-600 hover:text-blue-500 text-sm font-medium\">\n                    进入设置 →\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <ChartBarIcon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"ml-5\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">数据报告</h3>\n                    <p className=\"text-sm text-gray-500\">查看业务数据和统计</p>\n                  </div>\n                </div>\n                <div className=\"mt-3\">\n                  <button className=\"text-blue-600 hover:text-blue-500 text-sm font-medium\">\n                    查看报告 →\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"mt-8 bg-white shadow rounded-lg p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">快速操作</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <button\n                onClick={() => router.push('/admin/inquiries')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">客户咨询管理</div>\n                <div className=\"text-sm text-gray-500\">查看和管理所有客户咨询</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/analytics')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">数据分析</div>\n                <div className=\"text-sm text-gray-500\">查看统计数据和趋势</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/email-settings')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">邮件配置</div>\n                <div className=\"text-sm text-gray-500\">配置SMTP服务器设置</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/users')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">用户管理</div>\n                <div className=\"text-sm text-gray-500\">管理管理员账户</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/settings')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">系统设置</div>\n                <div className=\"text-sm text-gray-500\">网站和系统配置</div>\n              </button>\n              <button\n                onClick={() => router.push('/admin/logs')}\n                className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors\"\n              >\n                <div className=\"text-sm font-medium text-gray-900\">系统日志</div>\n                <div className=\"text-sm text-gray-500\">查看系统运行日志</div>\n              </button>\n            </div>\n          </div>\n        </main>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;;;;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,YAAY;IACd;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAClF,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;oCAAE;YACR;YACA,IAAI,iBAAiB;gBACnB;YACF;QACF;mCAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,4BAA4B;QAC5B,MAAM,qBAAqB;YACzB,SAAS;gBACP,iBAAiB,AAAC,UAAe,OAAN;YAC7B;QACF,GACC,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS,EAAE,EAAE;gBACf,mBAAmB;YACrB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;gBACnB,qBAAqB,KAAK,iBAAiB;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAmC;YACvC,uBAAuB;YACvB,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;QACrB;QACA,OAAO,KAAK,CAAC,YAAY,IAAI;IAC/B;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YAC/D,WAAW;gBAAE,OAAO;gBAA6B,MAAM;YAAM;YAC7D,QAAQ;gBAAE,OAAO;gBAA+B,MAAM;YAAM;QAC9D;QACA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBACE,0JAAC;YAAK,WAAW,AAAC,2EAAuF,OAAb,OAAO,KAAK;sBACrG,OAAO,IAAI;;;;;;IAGlB;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAO,WAAU;kCAChB,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,0JAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,6IAAA,CAAA,UAAkB;;;;;0DACnB,0JAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,0JAAC,6OAAA,CAAA,4BAAyB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShE,0JAAC;wBAAK,WAAU;;0CAEd,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC,mNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;;8EACC,0JAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,0JAAC;oEAAG,WAAU;8EAAqC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOjF,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC,mNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;;8EACC,0JAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,0JAAC;oEAAG,WAAU;8EAAqC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOjF,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC,2MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;;8EACC,0JAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,0JAAC;oEAAG,WAAU;8EAAqC,MAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOnF,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC,6MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;;8EACC,0JAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAC3D,0JAAC;oEAAG,WAAU;8EAAqC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS/E,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,0JAAC;kEACC,cAAA,0JAAC;4DAAK,WAAU;sEACd,cAAA,0JAAC,6MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGzB,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAG,WAAU;;kFACZ,0JAAC;wEAAK,WAAU;;;;;;oEAAqB;;;;;;;0EAGvC,0JAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAI5C,0JAAC;wDAAK,WAAU;kEACd,cAAA,0JAAC,6OAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIzC,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,0JAAC;kEACC,cAAA,0JAAC;4DAAK,WAAU;sEACd,cAAA,0JAAC,mNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG5B,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAG,WAAU;;kFACZ,0JAAC;wEAAK,WAAU;;;;;;oEAAqB;;;;;;;0EAGvC,0JAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAI5C,0JAAC;wDAAK,WAAU;kEACd,cAAA,0JAAC,6OAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIzC,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,0JAAC;kEACC,cAAA,0JAAC;4DAAK,WAAU;sEACd,cAAA,0JAAC,yMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGvB,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAG,WAAU;;kFACZ,0JAAC;wEAAK,WAAU;;;;;;oEAAqB;;;;;;;0EAGvC,0JAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAI5C,0JAAC;wDAAK,WAAU;kEACd,cAAA,0JAAC,6OAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIzC,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,0JAAC;kEACC,cAAA,0JAAC;4DAAK,WAAU;sEACd,cAAA,0JAAC,2NAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGhC,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAG,WAAU;;kFACZ,0JAAC;wEAAK,WAAU;;;;;;oEAAqB;;;;;;;0EAGvC,0JAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAI5C,0JAAC;wDAAK,WAAU;kEACd,cAAA,0JAAC,6OAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,0JAAC;gDAAE,WAAU;0DAAuC;;;;;;;;;;;;kDAEtD,0JAAC;wCAAG,WAAU;kDACX,kBAAkB,GAAG,CAAC,CAAC,2BACtB,0JAAC;0DACC,cAAA,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAI,WAAU;;sFACb,0JAAC;4EAAI,WAAU;sFACb,cAAA,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAK,WAAU;8FACb,WAAW,WAAW,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;sFAIrC,0JAAC;4EAAI,WAAU;;8FACb,0JAAC;oFAAI,WAAU;;sGACb,0JAAC;4FAAE,WAAU;sGACV,WAAW,WAAW;;;;;;sGAEzB,0JAAC;4FAAK,WAAU;sGACb,eAAe,WAAW,MAAM;;;;;;;;;;;;8FAGrC,0JAAC;oFAAE,WAAU;;wFACV,WAAW,aAAa;wFAAC;wFAAI,WAAW,KAAK;;;;;;;8FAEhD,0JAAC;oFAAE,WAAU;8FACV,mBAAmB,WAAW,WAAW;;;;;;;;;;;;;;;;;;8EAIhD,0JAAC;oEAAI,WAAU;8EACb,cAAA,0JAAC;wEAAE,WAAU;kFACV,IAAI,KAAK,WAAW,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;sEAI3D,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC;gEAAE,WAAU;0EACV,WAAW,OAAO;;;;;;;;;;;;;;;;;+CApClB,WAAW,EAAE;;;;;;;;;;oCA2CzB,kBAAkB,MAAM,KAAK,mBAC5B,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;0CAMnC,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC,mNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,0JAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;;;;;kDAOhF,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC,yMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAErB,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,0JAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;;;;;kDAOhF,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC,mNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,0JAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASlF,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,0JAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,0JAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,0JAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,0JAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,0JAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,0JAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,0JAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,0JAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,0JAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,0JAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,0JAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,0JAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GA9dwB;;QAUP,0HAAA,CAAA,YAAS;;;KAVF", "debugId": null}}, {"offset": {"line": 2571, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/dashboard\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}