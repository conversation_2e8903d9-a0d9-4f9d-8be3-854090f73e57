{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' },\n  { code: 'ru', name: 'Русский', flag: '🇷🇺' },\n];\n\nexport default function LanguageSwitcher() {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];\n\n  const handleLanguageChange = (langCode: string) => {\n    const { pathname, asPath, query } = router;\n    router.push({ pathname, query }, asPath, { locale: langCode });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n        aria-label={t('navigation.language')}\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.name}</span>\n        <span className=\"sm:hidden\">{currentLanguage.flag}</span>\n        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 ${\n                    language.code === router.locale\n                      ? 'bg-blue-50 text-blue-600'\n                      : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {language.code === router.locale && (\n                    <span className=\"ml-auto text-blue-600\">✓</span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;;;AALA;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC,EAAE;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QACpC,OAAO,IAAI,CAAC;YAAE;YAAU;QAAM,GAAG,QAAQ;YAAE,QAAQ;QAAS;QAC5D,UAAU;IACZ;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAY,EAAE;;kCAEd,0JAAC,mNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,0JAAC;wBAAK,WAAU;kCAAoB,gBAAgB,IAAI;;;;;;kCACxD,0JAAC;wBAAK,WAAU;kCAAa,gBAAgB,IAAI;;;;;;kCACjD,0JAAC,yNAAA,CAAA,kBAAe;wBAAC,WAAW,AAAC,6CAAuE,OAA3B,SAAS,eAAe;;;;;;;;;;;;YAGlG,wBACC;;kCACE,0JAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,0JAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,AAAC,+FAIX,OAHC,SAAS,IAAI,KAAK,OAAO,MAAM,GAC3B,6BACA;;sDAGN,0JAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,0JAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,SAAS,IAAI,KAAK,OAAO,MAAM,kBAC9B,0JAAC;4CAAK,WAAU;sDAAwB;;;;;;;mCAXrC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC;GAzDwB;;QAEP,0HAAA,CAAA,YAAS;QACV,4JAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport LanguageSwitcher from './LanguageSwitcher';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const navigation = [\n    { name: t('navigation.home'), href: '/' },\n    { name: t('navigation.services'), href: '/services' },\n    { name: t('navigation.features'), href: '/features' },\n    { name: t('navigation.contact'), href: '/contact' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return router.pathname === '/';\n    }\n    return router.pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <span className=\"text-xl font-bold text-gray-900\">{t('brand.name')}</span>\n                <p className=\"text-xs text-gray-500 max-w-xs truncate\">{t('brand.tagline')}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-blue-600 border-b-2 border-blue-600'\n                    : 'text-gray-700 hover:text-blue-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side - Language switcher and CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LanguageSwitcher />\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('buttons.contact_us')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-2\">\n            <LanguageSwitcher />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-2 border-t border-gray-200\">\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  {t('buttons.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAoB,MAAM;QAAI;QACxC;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;KACnD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,OAAO,QAAQ,KAAK;QAC7B;QACA,OAAO,OAAO,QAAQ,CAAC,UAAU,CAAC;IACpC;IAEA,qBACE,0JAAC;QAAO,WAAU;kBAChB,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAK,WAAU;0DAAmC,EAAE;;;;;;0DACrD,0JAAC;gDAAE,WAAU;0DAA2C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,0JAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC,wHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,gEAIX,OAHC,SAAS,KAAK,IAAI,IACd,6CACA;8CAGL,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,0JAAC;4BAAI,WAAU;;8CACb,0JAAC,4IAAA,CAAA,UAAgB;;;;;8CACjB,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,0JAAC;4BAAI,WAAU;;8CACb,0JAAC,4IAAA,CAAA,UAAgB;;;;;8CACjB,0JAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BACC,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC,wHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,AAAC,wEAIX,OAHC,SAAS,KAAK,IAAI,IACd,6BACA;8CAGL,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GAlHwB;;QAEP,0HAAA,CAAA,YAAS;QACV,4JAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const { t } = useTranslation('common');\n\n  const services = [\n    { name: '外贸网络线路', href: '/services/foreign-trade' },\n    { name: '跨境电商线路', href: '/services/ecommerce' },\n    { name: 'VPN服务', href: '/services/vpn' },\n    { name: '定制解决方案', href: '/services/custom' },\n  ];\n\n  const support = [\n    { name: '技术支持', href: '/support' },\n    { name: '服务条款', href: '/terms' },\n    { name: '隐私政策', href: '/privacy' },\n    { name: '常见问题', href: '/faq' },\n  ];\n\n  const company = [\n    { name: '关于我们', href: '/about' },\n    { name: '新闻动态', href: '/news' },\n    { name: '合作伙伴', href: '/partners' },\n    { name: '招聘信息', href: '/careers' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t('brand.name')}</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 text-sm leading-relaxed\">\n              {t('brand.tagline')}\n            </p>\n            \n            {/* Key features */}\n            <div className=\"space-y-2 mb-6\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ShieldCheckIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>AES/RSA/TLS加密</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <GlobeAltIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>全球网络覆盖</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ServerIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>7x24技术支持</span>\n              </div>\n            </div>\n\n            {/* Contact info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+86 400-xxx-xxxx</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>中国 · 深圳</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.services')}</h3>\n            <ul className=\"space-y-2\">\n              {services.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.support')}</h3>\n            <ul className=\"space-y-2\">\n              {support.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.company')}</h3>\n            <ul className=\"space-y-2\">\n              {company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('footer.copyright')}\n            </p>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                服务条款\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                隐私政策\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                网站地图\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAae,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM;QAA0B;QAClD;YAAE,MAAM;YAAU,MAAM;QAAsB;QAC9C;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAU,MAAM;QAAmB;KAC5C;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAO;KAC9B;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAY;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,0JAAC;QAAO,WAAU;kBAChB,cAAA,0JAAC;YAAI,WAAU;;8BAEb,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,0JAAC;4CAAK,WAAU;sDAAqB,EAAE;;;;;;;;;;;;8CAEzC,0JAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,yNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,mNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,+MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,0JAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,6MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,mNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,+MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,0JAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,qBACb,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,0JAAC;gCAAI,WAAU;;kDACb,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwE;;;;;;kDAGtG,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;kDAGxG,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStH;GApJwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function Layout({ children, className = '' }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,OAAO,KAAyC;QAAzC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAe,GAAzC;IAC7B,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC,kIAAA,CAAA,UAAM;;;;;0BACP,0JAAC;gBAAK,WAAW,AAAC,UAAmB,OAAV;0BACxB;;;;;;0BAEH,0JAAC,kIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KAVwB", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/services/foreign_trade_lines.tsx"], "sourcesContent": ["import { GetStaticProps } from 'next';\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\nimport { useTranslation } from 'next-i18next';\nimport Head from 'next/head';\nimport Link from 'next/link';\nimport Layout from '../../components/layout/Layout';\nimport { \n  GlobeAltIcon, \n  BoltIcon, \n  ShieldCheckIcon,\n  ClockIcon,\n  CpuChipIcon,\n  PhoneIcon\n} from '@heroicons/react/24/outline';\n\nexport default function ForeignTradeLines() {\n  const { t } = useTranslation(['common', 'services']);\n\n  const features = [\n    {\n      icon: CpuChipIcon,\n      title: '专用带宽',\n      description: '独享带宽资源，确保网络连接的稳定性和速度'\n    },\n    {\n      icon: GlobeAltIcon,\n      title: '全球覆盖',\n      description: '覆盖全球主要贸易市场的网络节点'\n    },\n    {\n      icon: ClockIcon,\n      title: '低延迟',\n      description: '优化的网络路由，确保最低的访问延迟'\n    },\n    {\n      icon: PhoneIcon,\n      title: '企业级支持',\n      description: '7x24小时专业技术支持服务'\n    },\n    {\n      icon: BoltIcon,\n      title: '高速稳定',\n      description: '高质量线路资源，保障业务连续性'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: '安全可靠',\n      description: '多重安全保障，确保数据传输安全'\n    }\n  ];\n\n  const regions = [\n    { name: '北美', countries: ['美国', '加拿大'], flag: '🇺🇸' },\n    { name: '欧洲', countries: ['德国', '英国', '法国'], flag: '🇪🇺' },\n    { name: '亚太', countries: ['日本', '韩国', '新加坡'], flag: '🌏' },\n    { name: '中东', countries: ['阿联酋', '沙特'], flag: '🕌' },\n    { name: '南美', countries: ['巴西', '阿根廷'], flag: '🌎' },\n    { name: '非洲', countries: ['南非', '埃及'], flag: '🌍' }\n  ];\n\n  return (\n    <>\n      <Head>\n        <title>外贸专线 - VPL专业网络解决方案</title>\n        <meta name=\"description\" content=\"VPL外贸专线服务，专为外贸企业设计的高速稳定网络线路，支持全球贸易业务，确保网络连接的可靠性。\" />\n        <meta name=\"keywords\" content=\"外贸专线,国际专线,外贸网络,全球贸易,网络连接\" />\n      </Head>\n      \n      <Layout>\n        {/* Hero Section */}\n        <section className=\"bg-gradient-to-br from-blue-50 to-cyan-100 py-20\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl\">\n                外贸专线\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\">\n                专为外贸企业设计的高速稳定网络线路，支持全球贸易业务，确保您的国际业务顺畅运行\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Global Coverage Section */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">全球网络覆盖</h2>\n              <p className=\"text-gray-600\">覆盖全球主要贸易市场，为您的外贸业务提供可靠保障</p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {regions.map((region, index) => (\n                <div key={index} className=\"text-center p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors\">\n                  <div className=\"text-4xl mb-3\">{region.flag}</div>\n                  <h3 className=\"font-semibold text-gray-900 mb-2\">{region.name}</h3>\n                  <p className=\"text-sm text-gray-600\">{region.countries.join('、')}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section className=\"py-24 bg-gray-50\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">服务特性</h2>\n              <p className=\"text-lg text-gray-600\">专业的外贸网络专线服务</p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {features.map((feature, index) => {\n                const IconComponent = feature.icon;\n                return (\n                  <div key={index} className=\"relative p-8 bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow\">\n                    <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-6\">\n                      <IconComponent className=\"w-6 h-6 text-blue-600\" />\n                    </div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-gray-600\">\n                      {feature.description}\n                    </p>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </section>\n\n        {/* Benefits Section */}\n        <section className=\"py-20 bg-white\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">为什么选择我们的外贸专线</h2>\n            </div>\n            \n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              <div>\n                <ul className=\"space-y-6\">\n                  <li className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1\">\n                      <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                    </div>\n                    <div className=\"ml-4\">\n                      <h3 className=\"font-semibold text-gray-900\">提升业务效率</h3>\n                      <p className=\"text-gray-600\">高速稳定的网络连接，显著提升外贸业务处理效率</p>\n                    </div>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1\">\n                      <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                    </div>\n                    <div className=\"ml-4\">\n                      <h3 className=\"font-semibold text-gray-900\">降低运营成本</h3>\n                      <p className=\"text-gray-600\">专业的网络解决方案，帮助企业降低IT运营成本</p>\n                    </div>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1\">\n                      <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                    </div>\n                    <div className=\"ml-4\">\n                      <h3 className=\"font-semibold text-gray-900\">增强竞争优势</h3>\n                      <p className=\"text-gray-600\">优质的网络服务，为企业在国际市场竞争中提供技术优势</p>\n                    </div>\n                  </li>\n                </ul>\n              </div>\n              <div className=\"bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl p-8 text-white\">\n                <h3 className=\"text-2xl font-bold mb-4\">专业服务保障</h3>\n                <p className=\"mb-6\">我们为外贸企业提供专业的网络专线服务，确保您的业务在全球范围内稳定运行。</p>\n                <ul className=\"space-y-3\">\n                  <li className=\"flex items-center\">\n                    <ShieldCheckIcon className=\"w-5 h-5 mr-3\" />\n                    <span>99.9%服务可用性保障</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <PhoneIcon className=\"w-5 h-5 mr-3\" />\n                    <span>7x24小时技术支持</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <GlobeAltIcon className=\"w-5 h-5 mr-3\" />\n                    <span>全球网络覆盖</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 bg-blue-600\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n                加速您的外贸业务\n              </h2>\n              <p className=\"mt-4 text-lg text-blue-100\">\n                联系我们获取专业的外贸网络专线解决方案\n              </p>\n              <div className=\"mt-8\">\n                <Link\n                  href=\"/contact\"\n                  className=\"inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-blue-600 shadow-sm hover:bg-gray-50 transition-colors duration-200\"\n                >\n                  联系我们\n                </Link>\n              </div>\n            </div>\n          </div>\n        </section>\n      </Layout>\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async ({ locale }) => {\n  return {\n    props: {\n      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'services'])),\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAW;IAEnD,MAAM,WAAW;QACf;YACE,MAAM,iNAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,mNAAA,CAAA,eAAY;YAClB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6MAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6MAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,2MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yNAAA,CAAA,kBAAe;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAM,WAAW;gBAAC;gBAAM;aAAM;YAAE,MAAM;QAAO;QACrD;YAAE,MAAM;YAAM,WAAW;gBAAC;gBAAM;gBAAM;aAAK;YAAE,MAAM;QAAO;QAC1D;YAAE,MAAM;YAAM,WAAW;gBAAC;gBAAM;gBAAM;aAAM;YAAE,MAAM;QAAK;QACzD;YAAE,MAAM;YAAM,WAAW;gBAAC;gBAAO;aAAK;YAAE,MAAM;QAAK;QACnD;YAAE,MAAM;YAAM,WAAW;gBAAC;gBAAM;aAAM;YAAE,MAAM;QAAK;QACnD;YAAE,MAAM;YAAM,WAAW;gBAAC;gBAAM;aAAK;YAAE,MAAM;QAAK;KACnD;IAED,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,0JAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAGhC,0JAAC,kIAAA,CAAA,UAAM;;kCAEL,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,0JAAC;wCAAE,WAAU;kDAAyD;;;;;;;;;;;;;;;;;;;;;;kCAQ5E,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,0JAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,0JAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,0JAAC;4CAAgB,WAAU;;8DACzB,0JAAC;oDAAI,WAAU;8DAAiB,OAAO,IAAI;;;;;;8DAC3C,0JAAC;oDAAG,WAAU;8DAAoC,OAAO,IAAI;;;;;;8DAC7D,0JAAC;oDAAE,WAAU;8DAAyB,OAAO,SAAS,CAAC,IAAI,CAAC;;;;;;;2CAHpD;;;;;;;;;;;;;;;;;;;;;kCAWlB,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,0JAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,0JAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wCACtB,MAAM,gBAAgB,QAAQ,IAAI;wCAClC,qBACE,0JAAC;4CAAgB,WAAU;;8DACzB,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAc,WAAU;;;;;;;;;;;8DAE3B,0JAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,0JAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;2CARd;;;;;oCAYd;;;;;;;;;;;;;;;;;kCAMN,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAG,WAAU;kDAAwC;;;;;;;;;;;8CAGxD,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;sDACC,cAAA,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;wDAAG,WAAU;;0EACZ,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,0JAAC;wEAAE,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;kEAGjC,0JAAC;wDAAG,WAAU;;0EACZ,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,0JAAC;wEAAE,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;kEAGjC,0JAAC;wDAAG,WAAU;;0EACZ,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,0JAAC;wEAAE,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKrC,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,0JAAC;oDAAE,WAAU;8DAAO;;;;;;8DACpB,0JAAC;oDAAG,WAAU;;sEACZ,0JAAC;4DAAG,WAAU;;8EACZ,0JAAC,yNAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;8EAC3B,0JAAC;8EAAK;;;;;;;;;;;;sEAER,0JAAC;4DAAG,WAAU;;8EACZ,0JAAC,6MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,0JAAC;8EAAK;;;;;;;;;;;;sEAER,0JAAC;4DAAG,WAAU;;8EACZ,0JAAC,mNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;8EACxB,0JAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASlB,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,0JAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA1MwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 2214, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/services/foreign_trade_lines\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}