{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/lib/notifications.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nclass NotificationService {\n  private socket: Socket | null = null;\n  private isConnected = false;\n  private connectionAttempts = 0;\n  private maxRetries = 3;\n\n  connect() {\n    if (typeof window !== 'undefined' && !this.socket && this.connectionAttempts < this.maxRetries) {\n      try {\n        this.connectionAttempts++;\n        console.log(`Attempting to connect to Socket.IO server (attempt ${this.connectionAttempts})`);\n\n        this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {\n          path: '/api/socket',\n          transports: ['polling', 'websocket'],\n          timeout: 5000,\n          forceNew: true,\n        });\n\n        this.socket.on('connect', () => {\n          console.log('Connected to notification service');\n          this.isConnected = true;\n          this.connectionAttempts = 0; // Reset on successful connection\n        });\n\n        this.socket.on('disconnect', () => {\n          console.log('Disconnected from notification service');\n          this.isConnected = false;\n        });\n\n        this.socket.on('connect_error', (error) => {\n          console.warn('Socket.IO connection error:', error.message);\n          this.isConnected = false;\n\n          if (this.connectionAttempts >= this.maxRetries) {\n            console.warn('Max connection attempts reached. Socket.IO features will be disabled.');\n          }\n        });\n\n        this.socket.on('admin-joined', (data) => {\n          console.log('Successfully joined admin room:', data);\n        });\n\n      } catch (error) {\n        console.error('Failed to initialize Socket.IO:', error);\n        this.isConnected = false;\n      }\n    }\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n    }\n  }\n\n  joinAdminRoom() {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('join-admin');\n    } else {\n      console.warn('Cannot join admin room: Socket.IO not connected');\n    }\n  }\n\n  onNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.on('new-submission', callback);\n    } else {\n      console.warn('Cannot listen for new submissions: Socket.IO not available');\n    }\n  }\n\n  offNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.off('new-submission', callback);\n    }\n  }\n\n  emitNewSubmission(data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('new-submission', data);\n    } else {\n      console.warn('Cannot emit new submission: Socket.IO not connected');\n    }\n  }\n\n  // Admin notification methods\n  notifyAdmins(type: string, data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.to('admin').emit('admin-notification', {\n        type,\n        data,\n        timestamp: new Date().toISOString(),\n      });\n    } else {\n      console.warn('Cannot notify admins: Socket.IO not connected');\n    }\n  }\n\n  onAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.on('admin-notification', callback);\n    } else {\n      console.warn('Cannot listen for admin notifications: Socket.IO not available');\n    }\n  }\n\n  offAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.off('admin-notification', callback);\n    }\n  }\n\n  // Check if service is available\n  isAvailable(): boolean {\n    return this.socket !== null && this.isConnected;\n  }\n\n  // Get connection status\n  getStatus(): string {\n    if (!this.socket) return 'not-initialized';\n    if (this.isConnected) return 'connected';\n    return 'disconnected';\n  }\n}\n\nexport const notificationService = new NotificationService();\n\n// Browser notification utilities\nexport const requestNotificationPermission = async (): Promise<boolean> => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return false;\n  }\n\n  if (Notification.permission === 'granted') {\n    return true;\n  }\n\n  if (Notification.permission === 'denied') {\n    return false;\n  }\n\n  const permission = await Notification.requestPermission();\n  return permission === 'granted';\n};\n\nexport const showBrowserNotification = (title: string, options?: NotificationOptions) => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return;\n  }\n\n  if (Notification.permission === 'granted') {\n    new Notification(title, {\n      icon: '/favicon.ico',\n      badge: '/favicon.ico',\n      ...options,\n    });\n  }\n};\n\nexport default NotificationService;\n"], "names": [], "mappings": ";;;;;;AAcyB;;AAdzB;AAAA;;;AAEA,MAAM;IAMJ,UAAU;QACR,IAAI,aAAkB,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,EAAE;YAC9F,IAAI;gBACF,IAAI,CAAC,kBAAkB;gBACvB,QAAQ,GAAG,CAAC,AAAC,sDAA6E,OAAxB,IAAI,CAAC,kBAAkB,EAAC;gBAE1F,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,2KAAA,CAAA,KAAE,AAAD,EAAE,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE;oBAC7E,MAAM;oBACN,YAAY;wBAAC;wBAAW;qBAAY;oBACpC,SAAS;oBACT,UAAU;gBACZ;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;oBACxB,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;oBACnB,IAAI,CAAC,kBAAkB,GAAG,GAAG,iCAAiC;gBAChE;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;gBACrB;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;oBAC/B,QAAQ,IAAI,CAAC,+BAA+B,MAAM,OAAO;oBACzD,IAAI,CAAC,WAAW,GAAG;oBAEnB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,EAAE;wBAC9C,QAAQ,IAAI,CAAC;oBACf;gBACF;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC;oBAC9B,QAAQ,GAAG,CAAC,mCAAmC;gBACjD;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,IAAI,CAAC,WAAW,GAAG;YACrB;QACF;IACF;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,gBAAgB;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,gBAAgB,QAA6B,EAAE;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB;QACnC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,iBAAiB,QAA6B,EAAE;QAC9C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB;QACpC;IACF;IAEA,kBAAkB,IAAS,EAAE;QAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;QACrC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,6BAA6B;IAC7B,aAAa,IAAY,EAAE,IAAS,EAAE;QACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,sBAAsB;gBACjD;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,oBAAoB,QAAqC,EAAE;QACzD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,sBAAsB;QACvC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,qBAAqB,QAAqC,EAAE;QAC1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB;QACxC;IACF;IAEA,gCAAgC;IAChC,cAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,WAAW;IACjD;IAEA,wBAAwB;IACxB,YAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO;QAC7B,OAAO;IACT;;QA5HA,wKAAQ,UAAwB;QAChC,wKAAQ,eAAc;QACtB,wKAAQ,sBAAqB;QAC7B,wKAAQ,cAAa;;AA0HvB;AAEO,MAAM,sBAAsB,IAAI;AAGhC,MAAM,gCAAgC;IAC3C,IAAI,aAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE,OAAO;IACT;IAEA,IAAI,aAAa,UAAU,KAAK,WAAW;QACzC,OAAO;IACT;IAEA,IAAI,aAAa,UAAU,KAAK,UAAU;QACxC,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,aAAa,iBAAiB;IACvD,OAAO,eAAe;AACxB;AAEO,MAAM,0BAA0B,CAAC,OAAe;IACrD,IAAI,aAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE;IACF;IAEA,IAAI,aAAa,UAAU,KAAK,WAAW;QACzC,IAAI,aAAa,OAAO;YACtB,MAAM;YACN,OAAO;YACP,GAAG,OAAO;QACZ;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/NotificationCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { BellIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { notificationService, showBrowserNotification, requestNotificationPermission } from '../../lib/notifications';\n\ninterface Notification {\n  id: string;\n  type: 'new-submission' | 'system' | 'warning';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n\nexport default function NotificationCenter() {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [connectionStatus, setConnectionStatus] = useState<string>('not-initialized');\n\n  useEffect(() => {\n    // Initialize notification service with error handling\n    try {\n      notificationService.connect();\n\n      // Check connection status periodically\n      const statusInterval = setInterval(() => {\n        setConnectionStatus(notificationService.getStatus());\n      }, 2000);\n\n      // Try to join admin room after a short delay\n      setTimeout(() => {\n        notificationService.joinAdminRoom();\n      }, 1000);\n\n      // Request browser notification permission\n      requestNotificationPermission();\n\n      return () => {\n        clearInterval(statusInterval);\n      };\n    } catch (error) {\n      console.error('Failed to initialize notification service:', error);\n      setConnectionStatus('error');\n    }\n\n    // Listen for new submissions\n    const handleNewSubmission = (data: any) => {\n      const notification: Notification = {\n        id: Date.now().toString(),\n        type: 'new-submission',\n        title: '新的客户咨询',\n        message: `${data.companyName} 提交了新的咨询`,\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n\n      setNotifications(prev => [notification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(notification.title, {\n        body: notification.message,\n        tag: 'new-submission',\n      });\n    };\n\n    // Listen for admin notifications\n    const handleAdminNotification = (notification: any) => {\n      const newNotification: Notification = {\n        id: Date.now().toString(),\n        type: notification.type,\n        title: notification.title || '系统通知',\n        message: notification.message,\n        timestamp: notification.timestamp,\n        read: false,\n      };\n\n      setNotifications(prev => [newNotification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(newNotification.title, {\n        body: newNotification.message,\n        tag: notification.type,\n      });\n    };\n\n    notificationService.onNewSubmission(handleNewSubmission);\n    notificationService.onAdminNotification(handleAdminNotification);\n\n    return () => {\n      notificationService.offNewSubmission(handleNewSubmission);\n      notificationService.offAdminNotification(handleAdminNotification);\n      notificationService.disconnect();\n    };\n  }, []);\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id\n          ? { ...notification, read: true }\n          : notification\n      )\n    );\n    setUnreadCount(prev => Math.max(0, prev - 1));\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n    setUnreadCount(0);\n  };\n\n  const removeNotification = (id: string) => {\n    const notification = notifications.find(n => n.id === id);\n    if (notification && !notification.read) {\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    }\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'new-submission':\n        return '📧';\n      case 'system':\n        return '⚙️';\n      case 'warning':\n        return '⚠️';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) {\n      return '刚刚';\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes}分钟前`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)}小时前`;\n    } else {\n      return date.toLocaleDateString('zh-CN');\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\"\n      >\n        <BellIcon className=\"h-6 w-6\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n        {/* Connection status indicator */}\n        <span className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full ${\n          connectionStatus === 'connected' ? 'bg-green-500' :\n          connectionStatus === 'disconnected' ? 'bg-yellow-500' :\n          'bg-gray-400'\n        }`} title={`Socket.IO: ${connectionStatus}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 max-h-96 overflow-hidden\">\n            <div className=\"p-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-medium text-gray-900\">通知</h3>\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-500\"\n                  >\n                    全部标记为已读\n                  </button>\n                )}\n              </div>\n              {/* Connection status */}\n              <div className=\"mt-2 text-xs text-gray-500\">\n                实时通知: {\n                  connectionStatus === 'connected' ? '🟢 已连接' :\n                  connectionStatus === 'disconnected' ? '🟡 连接中断' :\n                  connectionStatus === 'error' ? '🔴 连接错误' :\n                  '⚪ 未连接'\n                }\n              </div>\n            </div>\n            \n            <div className=\"max-h-64 overflow-y-auto\">\n              {notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  暂无通知\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${\n                      !notification.read ? 'bg-blue-50' : ''\n                    }`}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-start space-x-3 flex-1\">\n                        <span className=\"text-lg\">\n                          {getNotificationIcon(notification.type)}\n                        </span>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {notification.title}\n                          </p>\n                          <p className=\"text-sm text-gray-600 mt-1\">\n                            {notification.message}\n                          </p>\n                          <p className=\"text-xs text-gray-400 mt-1\">\n                            {formatTimestamp(notification.timestamp)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {!notification.read && (\n                          <button\n                            onClick={() => markAsRead(notification.id)}\n                            className=\"w-2 h-2 bg-blue-500 rounded-full\"\n                            title=\"标记为已读\"\n                          />\n                        )}\n                        <button\n                          onClick={() => removeNotification(notification.id)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAee,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;wCAAE;YACR,sDAAsD;YACtD,IAAI;gBACF,uHAAA,CAAA,sBAAmB,CAAC,OAAO;gBAE3B,uCAAuC;gBACvC,MAAM,iBAAiB;mEAAY;wBACjC,oBAAoB,uHAAA,CAAA,sBAAmB,CAAC,SAAS;oBACnD;kEAAG;gBAEH,6CAA6C;gBAC7C;oDAAW;wBACT,uHAAA,CAAA,sBAAmB,CAAC,aAAa;oBACnC;mDAAG;gBAEH,0CAA0C;gBAC1C,CAAA,GAAA,uHAAA,CAAA,gCAA6B,AAAD;gBAE5B;oDAAO;wBACL,cAAc;oBAChB;;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8CAA8C;gBAC5D,oBAAoB;YACtB;YAEA,6BAA6B;YAC7B,MAAM;oEAAsB,CAAC;oBAC3B,MAAM,eAA6B;wBACjC,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM;wBACN,OAAO;wBACP,SAAS,AAAC,GAAmB,OAAjB,KAAK,WAAW,EAAC;wBAC7B,WAAW,IAAI,OAAO,WAAW;wBACjC,MAAM;oBACR;oBAEA;4EAAiB,CAAA,OAAQ;gCAAC;mCAAiB;6BAAK;;oBAChD;4EAAe,CAAA,OAAQ,OAAO;;oBAE9B,4BAA4B;oBAC5B,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa,KAAK,EAAE;wBAC1C,MAAM,aAAa,OAAO;wBAC1B,KAAK;oBACP;gBACF;;YAEA,iCAAiC;YACjC,MAAM;wEAA0B,CAAC;oBAC/B,MAAM,kBAAgC;wBACpC,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM,aAAa,IAAI;wBACvB,OAAO,aAAa,KAAK,IAAI;wBAC7B,SAAS,aAAa,OAAO;wBAC7B,WAAW,aAAa,SAAS;wBACjC,MAAM;oBACR;oBAEA;gFAAiB,CAAA,OAAQ;gCAAC;mCAAoB;6BAAK;;oBACnD;gFAAe,CAAA,OAAQ,OAAO;;oBAE9B,4BAA4B;oBAC5B,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE,gBAAgB,KAAK,EAAE;wBAC7C,MAAM,gBAAgB,OAAO;wBAC7B,KAAK,aAAa,IAAI;oBACxB;gBACF;;YAEA,uHAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;YACpC,uHAAA,CAAA,sBAAmB,CAAC,mBAAmB,CAAC;YAExC;gDAAO;oBACL,uHAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC;oBACrC,uHAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;oBACzC,uHAAA,CAAA,sBAAmB,CAAC,UAAU;gBAChC;;QACF;uCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAC9B;QAGR,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IAC5C;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;QAE3D,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,gBAAgB,CAAC,aAAa,IAAI,EAAE;YACtC,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C;QACA,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,AAAC,GAAgB,OAAd,eAAc;QAC1B,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,AAAC,GAAiC,OAA/B,KAAK,KAAK,CAAC,gBAAgB,KAAI;QAC3C,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,0JAAC,2MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBACnB,cAAc,mBACb,0JAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;kCAIhC,0JAAC;wBAAK,WAAW,AAAC,oDAIjB,OAHC,qBAAqB,cAAc,iBACnC,qBAAqB,iBAAiB,kBACtC;wBACE,OAAO,AAAC,cAA8B,OAAjB;;;;;;;;;;;;YAG1B,wBACC;;kCACE,0JAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,cAAc,mBACb,0JAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAML,0JAAC;wCAAI,WAAU;;4CAA6B;4CAExC,qBAAqB,cAAc,WACnC,qBAAqB,iBAAiB,YACtC,qBAAqB,UAAU,YAC/B;;;;;;;;;;;;;0CAKN,0JAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,0JAAC;oCAAI,WAAU;8CAAgC;;;;;2CAI/C,cAAc,GAAG,CAAC,CAAC,6BACjB,0JAAC;wCAEC,WAAW,AAAC,iDAEX,OADC,CAAC,aAAa,IAAI,GAAG,eAAe;kDAGtC,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAK,WAAU;sEACb,oBAAoB,aAAa,IAAI;;;;;;sEAExC,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAE,WAAU;8EACV,aAAa,KAAK;;;;;;8EAErB,0JAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,0JAAC;oEAAE,WAAU;8EACV,gBAAgB,aAAa,SAAS;;;;;;;;;;;;;;;;;;8DAI7C,0JAAC;oDAAI,WAAU;;wDACZ,CAAC,aAAa,IAAI,kBACjB,0JAAC;4DACC,SAAS,IAAM,WAAW,aAAa,EAAE;4DACzC,WAAU;4DACV,OAAM;;;;;;sEAGV,0JAAC;4DACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,WAAU;sEAEV,cAAA,0JAAC,6MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAlCtB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AA+CxC;GApPwB;KAAA", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Link from 'next/link';\nimport { \n  HomeIcon,\n  UsersIcon,\n  EnvelopeIcon,\n  ChartBarIcon,\n  CogIcon,\n  DocumentTextIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport NotificationCenter from './NotificationCenter';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: '仪表板', href: '/admin/dashboard', icon: HomeIcon },\n  { name: '咨询管理', href: '/admin/inquiries', icon: EnvelopeIcon },\n  { name: '用户管理', href: '/admin/users', icon: UsersIcon },\n  { name: '数据分析', href: '/admin/analytics', icon: ChartBarIcon },\n  { \n    name: '系统设置', \n    icon: CogIcon,\n    children: [\n      { name: '基本配置', href: '/admin/system/config', icon: CogIcon },\n      { name: '邮件设置', href: '/admin/email-settings', icon: EnvelopeIcon },\n      { name: '多语言管理', href: '/admin/system/localization', icon: GlobeAltIcon },\n      { name: '安全设置', href: '/admin/system/security', icon: ShieldCheckIcon },\n    ]\n  },\n  { \n    name: '内容管理', \n    icon: DocumentTextIcon,\n    children: [\n      { name: '页面内容', href: '/admin/content/pages', icon: DocumentTextIcon },\n      { name: '服务类型', href: '/admin/content/services', icon: ServerIcon },\n    ]\n  },\n];\n\nexport default function AdminLayout({ children, title }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedItems, setExpandedItems] = useState<string[]>([]);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [userInfo, setUserInfo] = useState<any>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    fetch('/api/admin/verify', {\n      headers: { 'Authorization': `Bearer ${token}` }\n    })\n    .then(response => response.json())\n    .then(result => {\n      if (result.success) {\n        setIsAuthenticated(true);\n        setUserInfo(result.user);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const toggleExpanded = (itemName: string) => {\n    setExpandedItems(prev => \n      prev.includes(itemName) \n        ? prev.filter(name => name !== itemName)\n        : [...prev, itemName]\n    );\n  };\n\n  const isCurrentPath = (href: string) => {\n    return router.pathname === href || router.pathname.startsWith(href + '/');\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XMarkIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <SidebarContent \n            navigation={navigation} \n            expandedItems={expandedItems}\n            toggleExpanded={toggleExpanded}\n            isCurrentPath={isCurrentPath}\n          />\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <SidebarContent \n            navigation={navigation} \n            expandedItems={expandedItems}\n            toggleExpanded={toggleExpanded}\n            isCurrentPath={isCurrentPath}\n          />\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Top bar */}\n        <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Bars3Icon className=\"h-6 w-6\" />\n          </button>\n          \n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex items-center\">\n              {title && (\n                <h1 className=\"text-2xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            \n            <div className=\"ml-4 flex items-center md:ml-6 space-x-4\">\n              <NotificationCenter />\n              \n              {/* User menu */}\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-700\">{userInfo?.fullName || userInfo?.username}</div>\n                  <div className=\"text-gray-500\">{userInfo?.role}</div>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-400 hover:text-gray-600\"\n                  title=\"退出登录\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n\nfunction SidebarContent({ \n  navigation, \n  expandedItems, \n  toggleExpanded, \n  isCurrentPath \n}: {\n  navigation: any[];\n  expandedItems: string[];\n  toggleExpanded: (name: string) => void;\n  isCurrentPath: (href: string) => boolean;\n}) {\n  return (\n    <div className=\"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\">\n      {/* Logo */}\n      <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n        <div className=\"flex items-center flex-shrink-0 px-4\">\n          <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg mr-3\">\n            <span className=\"text-white font-bold\">VPL</span>\n          </div>\n          <span className=\"text-xl font-semibold text-gray-900\">管理后台</span>\n        </div>\n        \n        {/* Navigation */}\n        <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n          {navigation.map((item) => (\n            <div key={item.name}>\n              {item.children ? (\n                <div>\n                  <button\n                    onClick={() => toggleExpanded(item.name)}\n                    className={`group w-full flex items-center pl-2 pr-1 py-2 text-left text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      expandedItems.includes(item.name)\n                        ? 'bg-gray-100 text-gray-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                    {item.name}\n                    <svg\n                      className={`ml-auto h-5 w-5 transform transition-colors duration-150 ${\n                        expandedItems.includes(item.name) ? 'rotate-90 text-gray-400' : 'text-gray-300'\n                      }`}\n                      viewBox=\"0 0 20 20\"\n                      fill=\"currentColor\"\n                    >\n                      <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </button>\n                  {expandedItems.includes(item.name) && (\n                    <div className=\"mt-1 space-y-1\">\n                      {item.children.map((subItem: any) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          className={`group w-full flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md ${\n                            isCurrentPath(subItem.href)\n                              ? 'bg-blue-100 text-blue-700'\n                              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                          }`}\n                        >\n                          <subItem.icon className=\"mr-3 h-4 w-4\" />\n                          {subItem.name}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <Link\n                  href={item.href}\n                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                    isCurrentPath(item.href)\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                >\n                  <item.icon className={`mr-3 h-5 w-5 ${\n                    isCurrentPath(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'\n                  }`} />\n                  {item.name}\n                </Link>\n              )}\n            </div>\n          ))}\n        </nav>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAoB,MAAM,2MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,mNAAA,CAAA,eAAY;IAAC;IAC7D;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,6MAAA,CAAA,YAAS;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,mNAAA,CAAA,eAAY;IAAC;IAC7D;QACE,MAAM;QACN,MAAM,yMAAA,CAAA,UAAO;QACb,UAAU;YACR;gBAAE,MAAM;gBAAQ,MAAM;gBAAwB,MAAM,yMAAA,CAAA,UAAO;YAAC;YAC5D;gBAAE,MAAM;gBAAQ,MAAM;gBAAyB,MAAM,mNAAA,CAAA,eAAY;YAAC;YAClE;gBAAE,MAAM;gBAAS,MAAM;gBAA8B,MAAM,mNAAA,CAAA,eAAY;YAAC;YACxE;gBAAE,MAAM;gBAAQ,MAAM;gBAA0B,MAAM,yNAAA,CAAA,kBAAe;YAAC;SACvE;IACH;IACA;QACE,MAAM;QACN,MAAM,2NAAA,CAAA,mBAAgB;QACtB,UAAU;YACR;gBAAE,MAAM;gBAAQ,MAAM;gBAAwB,MAAM,2NAAA,CAAA,mBAAgB;YAAC;YACrE;gBAAE,MAAM;gBAAQ,MAAM;gBAA2B,MAAM,+MAAA,CAAA,aAAU;YAAC;SACnE;IACH;CACD;AAEc,SAAS,YAAY,KAAqC;QAArC,EAAE,QAAQ,EAAE,KAAK,EAAoB,GAArC;;IAClC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,qBAAqB;YACzB,SAAS;gBAAE,iBAAiB,AAAC,UAAe,OAAN;YAAQ;QAChD,GACC,IAAI,CAAC,CAAA,WAAY,SAAS,IAAI,IAC9B,IAAI,CAAC,CAAA;YACJ,IAAI,OAAO,OAAO,EAAE;gBAClB,mBAAmB;gBACnB,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO;IACvE;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BAEb,0JAAC;gBAAI,WAAW,AAAC,qCAAgE,OAA5B,cAAc,KAAK;;kCACtE,0JAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,0JAAC;gCACC,YAAY;gCACZ,eAAe;gCACf,gBAAgB;gCAChB,eAAe;;;;;;;;;;;;;;;;;;0BAMrB,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBACC,YAAY;wBACZ,eAAe;wBACf,gBAAgB;wBAChB,eAAe;;;;;;;;;;;;;;;;0BAMrB,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,0JAAC,6MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAGvB,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACZ,uBACC,0JAAC;4CAAG,WAAU;sDAAwC;;;;;;;;;;;kDAI1D,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,6IAAA,CAAA,UAAkB;;;;;0DAGnB,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAA6B,CAAA,qBAAA,+BAAA,SAAU,QAAQ,MAAI,qBAAA,+BAAA,SAAU,QAAQ;;;;;;0EACpF,0JAAC;gEAAI,WAAU;0EAAiB,qBAAA,+BAAA,SAAU,IAAI;;;;;;;;;;;;kEAEhD,0JAAC;wDACC,SAAS;wDACT,WAAU;wDACV,OAAM;kEAEN,cAAA,0JAAC,6OAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,0JAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GA1JwB;;QAMP,0HAAA,CAAA,YAAS;;;KANF;AA4JxB,SAAS,eAAe,KAUvB;QAVuB,EACtB,UAAU,EACV,aAAa,EACb,cAAc,EACd,aAAa,EAMd,GAVuB;IAWtB,qBACE,0JAAC;QAAI,WAAU;kBAEb,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;sCAEzC,0JAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;8BAIxD,0JAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC;sCACE,KAAK,QAAQ,iBACZ,0JAAC;;kDACC,0JAAC;wCACC,SAAS,IAAM,eAAe,KAAK,IAAI;wCACvC,WAAW,AAAC,8IAIX,OAHC,cAAc,QAAQ,CAAC,KAAK,IAAI,IAC5B,8BACA;;0DAGN,0JAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;0DACV,0JAAC;gDACC,WAAW,AAAC,4DAEX,OADC,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,4BAA4B;gDAElE,SAAQ;gDACR,MAAK;0DAEL,cAAA,0JAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;oCAG5J,cAAc,QAAQ,CAAC,KAAK,IAAI,mBAC/B,0JAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,WAAW,AAAC,iFAIX,OAHC,cAAc,QAAQ,IAAI,IACtB,8BACA;;kEAGN,0JAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;oDACvB,QAAQ,IAAI;;+CATR,QAAQ,IAAI;;;;;;;;;;;;;;;qDAgB3B,0JAAC,wHAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,AAAC,oEAIX,OAHC,cAAc,KAAK,IAAI,IACnB,8BACA;;kDAGN,0JAAC,KAAK,IAAI;wCAAC,WAAW,AAAC,gBAEtB,OADC,cAAc,KAAK,IAAI,IAAI,kBAAkB;;;;;;oCAE9C,KAAK,IAAI;;;;;;;2BAtDN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AA+D/B;MAxFS", "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/users/create.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport AdminLayout from '../../../components/admin/AdminLayout';\nimport { \n  UserPlusIcon,\n  EyeIcon,\n  EyeSlashIcon,\n  ArrowLeftIcon\n} from '@heroicons/react/24/outline';\n\ninterface CreateUserForm {\n  username: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  role: 'admin' | 'user';\n  firstName: string;\n  lastName: string;\n}\n\nexport default function CreateUser() {\n  const router = useRouter();\n  const [formData, setFormData] = useState<CreateUserForm>({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'user',\n    firstName: '',\n    lastName: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [errors, setErrors] = useState<Partial<CreateUserForm>>({});\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name as keyof CreateUserForm]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: undefined\n      }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Partial<CreateUserForm> = {};\n\n    if (!formData.username.trim()) {\n      newErrors.username = '用户名不能为空';\n    } else if (formData.username.length < 3) {\n      newErrors.username = '用户名至少需要3个字符';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = '邮箱不能为空';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = '请输入有效的邮箱地址';\n    }\n\n    if (!formData.password) {\n      newErrors.password = '密码不能为空';\n    } else if (formData.password.length < 6) {\n      newErrors.password = '密码至少需要6个字符';\n    }\n\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = '请确认密码';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = '两次输入的密码不一致';\n    }\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = '姓名不能为空';\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = '姓氏不能为空';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    \n    try {\n      const response = await fetch('/api/admin/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          username: formData.username,\n          email: formData.email,\n          password: formData.password,\n          role: formData.role,\n          firstName: formData.firstName,\n          lastName: formData.lastName\n        }),\n      });\n\n      if (response.ok) {\n        router.push('/admin/users?created=true');\n      } else {\n        const errorData = await response.json();\n        alert(`创建用户失败: ${errorData.message || '未知错误'}`);\n      }\n    } catch (error) {\n      console.error('Error creating user:', error);\n      alert('创建用户时发生错误，请稍后重试');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Head>\n        <title>创建用户 - VPL后台管理系统</title>\n        <meta name=\"description\" content=\"创建新用户账户\" />\n      </Head>\n      \n      <AdminLayout>\n        <div className=\"max-w-2xl mx-auto\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <button\n              onClick={() => router.back()}\n              className=\"flex items-center text-gray-600 hover:text-gray-900 mb-4\"\n            >\n              <ArrowLeftIcon className=\"h-5 w-5 mr-2\" />\n              返回\n            </button>\n            <div className=\"flex items-center\">\n              <UserPlusIcon className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">创建用户</h1>\n                <p className=\"text-gray-600\">添加新的用户账户到系统</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Form */}\n          <div className=\"bg-white shadow-sm rounded-lg border border-gray-200\">\n            <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n              {/* Basic Information */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">基本信息</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      姓名 *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"firstName\"\n                      name=\"firstName\"\n                      value={formData.firstName}\n                      onChange={handleInputChange}\n                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                        errors.firstName ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请输入姓名\"\n                    />\n                    {errors.firstName && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.firstName}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      姓氏 *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"lastName\"\n                      name=\"lastName\"\n                      value={formData.lastName}\n                      onChange={handleInputChange}\n                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                        errors.lastName ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请输入姓氏\"\n                    />\n                    {errors.lastName && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.lastName}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Account Information */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">账户信息</h3>\n                <div className=\"space-y-4\">\n                  <div>\n                    <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      用户名 *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"username\"\n                      name=\"username\"\n                      value={formData.username}\n                      onChange={handleInputChange}\n                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                        errors.username ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请输入用户名\"\n                    />\n                    {errors.username && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.username}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      邮箱 *\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                        errors.email ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请输入邮箱地址\"\n                    />\n                    {errors.email && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"role\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      角色 *\n                    </label>\n                    <select\n                      id=\"role\"\n                      name=\"role\"\n                      value={formData.role}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"user\">普通用户</option>\n                      <option value=\"admin\">管理员</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              {/* Password */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">密码设置</h3>\n                <div className=\"space-y-4\">\n                  <div>\n                    <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      密码 *\n                    </label>\n                    <div className=\"relative\">\n                      <input\n                        type={showPassword ? 'text' : 'password'}\n                        id=\"password\"\n                        name=\"password\"\n                        value={formData.password}\n                        onChange={handleInputChange}\n                        className={`w-full px-3 py-2 pr-10 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                          errors.password ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                        placeholder=\"请输入密码\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowPassword(!showPassword)}\n                        className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                      >\n                        {showPassword ? (\n                          <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                        ) : (\n                          <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                        )}\n                      </button>\n                    </div>\n                    {errors.password && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      确认密码 *\n                    </label>\n                    <div className=\"relative\">\n                      <input\n                        type={showConfirmPassword ? 'text' : 'password'}\n                        id=\"confirmPassword\"\n                        name=\"confirmPassword\"\n                        value={formData.confirmPassword}\n                        onChange={handleInputChange}\n                        className={`w-full px-3 py-2 pr-10 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                          errors.confirmPassword ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                        placeholder=\"请再次输入密码\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                        className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                      >\n                        {showConfirmPassword ? (\n                          <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                        ) : (\n                          <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                        )}\n                      </button>\n                    </div>\n                    {errors.confirmPassword && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.confirmPassword}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Submit Buttons */}\n              <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n                <button\n                  type=\"button\"\n                  onClick={() => router.back()}\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                >\n                  取消\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isSubmitting ? '创建中...' : '创建用户'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </AdminLayout>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,UAAU;QACV,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,MAAM;QACN,WAAW;QACX,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE/D,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAA6B,EAAE;YACxC,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAqC,CAAC;QAE5C,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,UAAU,eAAe,GAAG;QAC9B,OAAO,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YACzD,UAAU,eAAe,GAAG;QAC9B;QAEA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,UAAU,SAAS,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;oBACnB,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,AAAC,WAAsC,OAA5B,UAAU,OAAO,IAAI;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAGnC,0JAAC,sIAAA,CAAA,UAAW;0BACV,cAAA,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCACC,SAAS,IAAM,OAAO,IAAI;oCAC1B,WAAU;;sDAEV,0JAAC,qNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAG5C,0JAAC;oCAAI,WAAU;;sDACb,0JAAC,mNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,0JAAC;;8DACC,0JAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,0JAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMnC,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;;0EACC,0JAAC;gEAAM,SAAQ;gEAAY,WAAU;0EAA+C;;;;;;0EAGpF,0JAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,SAAS;gEACzB,UAAU;gEACV,WAAW,AAAC,oGAEX,OADC,OAAO,SAAS,GAAG,mBAAmB;gEAExC,aAAY;;;;;;4DAEb,OAAO,SAAS,kBACf,0JAAC;gEAAE,WAAU;0EAA6B,OAAO,SAAS;;;;;;;;;;;;kEAI9D,0JAAC;;0EACC,0JAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAA+C;;;;;;0EAGnF,0JAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,WAAW,AAAC,oGAEX,OADC,OAAO,QAAQ,GAAG,mBAAmB;gEAEvC,aAAY;;;;;;4DAEb,OAAO,QAAQ,kBACd,0JAAC;gEAAE,WAAU;0EAA6B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kDAOjE,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;;0EACC,0JAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAA+C;;;;;;0EAGnF,0JAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,WAAW,AAAC,oGAEX,OADC,OAAO,QAAQ,GAAG,mBAAmB;gEAEvC,aAAY;;;;;;4DAEb,OAAO,QAAQ,kBACd,0JAAC;gEAAE,WAAU;0EAA6B,OAAO,QAAQ;;;;;;;;;;;;kEAI7D,0JAAC;;0EACC,0JAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAA+C;;;;;;0EAGhF,0JAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAW,AAAC,oGAEX,OADC,OAAO,KAAK,GAAG,mBAAmB;gEAEpC,aAAY;;;;;;4DAEb,OAAO,KAAK,kBACX,0JAAC;gEAAE,WAAU;0EAA6B,OAAO,KAAK;;;;;;;;;;;;kEAI1D,0JAAC;;0EACC,0JAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAA+C;;;;;;0EAG/E,0JAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAU;;kFAEV,0JAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,0JAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO9B,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;;0EACC,0JAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAA+C;;;;;;0EAGnF,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEACC,MAAM,eAAe,SAAS;wEAC9B,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,QAAQ;wEACxB,UAAU;wEACV,WAAW,AAAC,0GAEX,OADC,OAAO,QAAQ,GAAG,mBAAmB;wEAEvC,aAAY;;;;;;kFAEd,0JAAC;wEACC,MAAK;wEACL,SAAS,IAAM,gBAAgB,CAAC;wEAChC,WAAU;kFAET,6BACC,0JAAC,mNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;iGAExB,0JAAC,yMAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;;;;;;;4DAIxB,OAAO,QAAQ,kBACd,0JAAC;gEAAE,WAAU;0EAA6B,OAAO,QAAQ;;;;;;;;;;;;kEAI7D,0JAAC;;0EACC,0JAAC;gEAAM,SAAQ;gEAAkB,WAAU;0EAA+C;;;;;;0EAG1F,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEACC,MAAM,sBAAsB,SAAS;wEACrC,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,eAAe;wEAC/B,UAAU;wEACV,WAAW,AAAC,0GAEX,OADC,OAAO,eAAe,GAAG,mBAAmB;wEAE9C,aAAY;;;;;;kFAEd,0JAAC;wEACC,MAAK;wEACL,SAAS,IAAM,uBAAuB,CAAC;wEACvC,WAAU;kFAET,oCACC,0JAAC,mNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;iGAExB,0JAAC,yMAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;;;;;;;4DAIxB,OAAO,eAAe,kBACrB,0JAAC;gEAAE,WAAU;0EAA6B,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;kDAOxE,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI;gDAC1B,WAAU;0DACX;;;;;;0DAGD,0JAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7C;GAtVwB;;QACP,0HAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 2300, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/users/create\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}