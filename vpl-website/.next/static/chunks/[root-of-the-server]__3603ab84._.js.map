{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/logs.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  DocumentTextIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n  XCircleIcon,\n  CheckCircleIcon,\n  FunnelIcon,\n  ArrowPathIcon,\n  TrashIcon\n} from '@heroicons/react/24/outline';\n\ninterface LogEntry {\n  id: string;\n  timestamp: string;\n  level: 'info' | 'warning' | 'error' | 'debug';\n  category: 'system' | 'auth' | 'email' | 'api' | 'database';\n  message: string;\n  details?: any;\n  userId?: string;\n  ipAddress?: string;\n}\n\nexport default function LogsPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [logs, setLogs] = useState<LogEntry[]>([]);\n  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);\n  const [filters, setFilters] = useState({\n    level: '',\n    category: '',\n    dateRange: '',\n    search: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 50,\n    total: 0\n  });\n  const [autoRefresh, setAutoRefresh] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchLogs();\n    }\n  }, [isAuthenticated, pagination.page]);\n\n  useEffect(() => {\n    applyFilters();\n  }, [logs, filters]);\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    if (autoRefresh) {\n      interval = setInterval(() => {\n        fetchLogs();\n      }, 5000); // Refresh every 5 seconds\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [autoRefresh]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchLogs = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/logs?page=${pagination.page}&limit=${pagination.limit}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setLogs(data.data || []);\n        setPagination(prev => ({\n          ...prev,\n          total: data.pagination?.total || 0\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to fetch logs:', error);\n    }\n  };\n\n  const applyFilters = () => {\n    let filtered = [...logs];\n\n    if (filters.level) {\n      filtered = filtered.filter(log => log.level === filters.level);\n    }\n\n    if (filters.category) {\n      filtered = filtered.filter(log => log.category === filters.category);\n    }\n\n    if (filters.search) {\n      const searchTerm = filters.search.toLowerCase();\n      filtered = filtered.filter(log => \n        log.message.toLowerCase().includes(searchTerm) ||\n        log.category.toLowerCase().includes(searchTerm)\n      );\n    }\n\n    if (filters.dateRange) {\n      const now = new Date();\n      let cutoffDate = new Date();\n      \n      switch (filters.dateRange) {\n        case '1h':\n          cutoffDate.setHours(now.getHours() - 1);\n          break;\n        case '24h':\n          cutoffDate.setDate(now.getDate() - 1);\n          break;\n        case '7d':\n          cutoffDate.setDate(now.getDate() - 7);\n          break;\n        case '30d':\n          cutoffDate.setDate(now.getDate() - 30);\n          break;\n      }\n      \n      if (filters.dateRange !== '') {\n        filtered = filtered.filter(log => new Date(log.timestamp) >= cutoffDate);\n      }\n    }\n\n    setFilteredLogs(filtered);\n  };\n\n  const clearLogs = async () => {\n    if (!confirm('确定要清空所有日志吗？此操作不可撤销。')) return;\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/logs', {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        fetchLogs();\n      }\n    } catch (error) {\n      console.error('Failed to clear logs:', error);\n    }\n  };\n\n  const getLevelIcon = (level: string) => {\n    switch (level) {\n      case 'error':\n        return <XCircleIcon className=\"h-5 w-5 text-red-500\" />;\n      case 'warning':\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-500\" />;\n      case 'info':\n        return <InformationCircleIcon className=\"h-5 w-5 text-blue-500\" />;\n      case 'debug':\n        return <CheckCircleIcon className=\"h-5 w-5 text-gray-500\" />;\n      default:\n        return <InformationCircleIcon className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getLevelBadge = (level: string) => {\n    const levelConfig = {\n      error: { color: 'bg-red-100 text-red-800', text: '错误' },\n      warning: { color: 'bg-yellow-100 text-yellow-800', text: '警告' },\n      info: { color: 'bg-blue-100 text-blue-800', text: '信息' },\n      debug: { color: 'bg-gray-100 text-gray-800', text: '调试' },\n    };\n    const config = levelConfig[level as keyof typeof levelConfig] || levelConfig.info;\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getCategoryName = (category: string) => {\n    const categories: { [key: string]: string } = {\n      system: '系统',\n      auth: '认证',\n      email: '邮件',\n      api: 'API',\n      database: '数据库',\n    };\n    return categories[category] || category;\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>系统日志 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">系统日志</h1>\n                <p className=\"text-gray-600\">查看系统运行日志和错误信息</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={autoRefresh}\n                    onChange={(e) => setAutoRefresh(e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">自动刷新</span>\n                </label>\n                <button\n                  onClick={fetchLogs}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <ArrowPathIcon className=\"h-4 w-4 mr-2\" />\n                  刷新\n                </button>\n                <button\n                  onClick={clearLogs}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <TrashIcon className=\"h-4 w-4 mr-2\" />\n                  清空日志\n                </button>\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center\">\n                  <FunnelIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n                  <span className=\"text-sm font-medium text-gray-700\">筛选：</span>\n                </div>\n                \n                <select\n                  value={filters.level}\n                  onChange={(e) => setFilters({ ...filters, level: e.target.value })}\n                  className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">所有级别</option>\n                  <option value=\"error\">错误</option>\n                  <option value=\"warning\">警告</option>\n                  <option value=\"info\">信息</option>\n                  <option value=\"debug\">调试</option>\n                </select>\n                \n                <select\n                  value={filters.category}\n                  onChange={(e) => setFilters({ ...filters, category: e.target.value })}\n                  className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">所有分类</option>\n                  <option value=\"system\">系统</option>\n                  <option value=\"auth\">认证</option>\n                  <option value=\"email\">邮件</option>\n                  <option value=\"api\">API</option>\n                  <option value=\"database\">数据库</option>\n                </select>\n                \n                <select\n                  value={filters.dateRange}\n                  onChange={(e) => setFilters({ ...filters, dateRange: e.target.value })}\n                  className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">所有时间</option>\n                  <option value=\"1h\">最近1小时</option>\n                  <option value=\"24h\">最近24小时</option>\n                  <option value=\"7d\">最近7天</option>\n                  <option value=\"30d\">最近30天</option>\n                </select>\n                \n                <input\n                  type=\"text\"\n                  placeholder=\"搜索日志...\"\n                  value={filters.search}\n                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}\n                  className=\"flex-1 max-w-xs border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Logs Table */}\n          <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      时间\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      级别\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      分类\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      消息\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      详情\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {filteredLogs.map((log) => (\n                    <tr key={log.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {new Date(log.timestamp).toLocaleString('zh-CN')}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          {getLevelIcon(log.level)}\n                          <span className=\"ml-2\">\n                            {getLevelBadge(log.level)}\n                          </span>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {getCategoryName(log.category)}\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-900\">\n                        <div className=\"max-w-xs truncate\" title={log.message}>\n                          {log.message}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\n                        {log.details && (\n                          <details className=\"cursor-pointer\">\n                            <summary className=\"text-blue-600 hover:text-blue-800\">查看详情</summary>\n                            <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto\">\n                              {JSON.stringify(log.details, null, 2)}\n                            </pre>\n                          </details>\n                        )}\n                        {log.ipAddress && (\n                          <div className=\"text-xs text-gray-400\">IP: {log.ipAddress}</div>\n                        )}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Pagination */}\n            <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n              <div className=\"flex-1 flex justify-between sm:hidden\">\n                <button\n                  onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n                  disabled={pagination.page === 1}\n                  className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  上一页\n                </button>\n                <button\n                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                  disabled={pagination.page * pagination.limit >= pagination.total}\n                  className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  下一页\n                </button>\n              </div>\n              <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                <div>\n                  <p className=\"text-sm text-gray-700\">\n                    显示 <span className=\"font-medium\">{(pagination.page - 1) * pagination.limit + 1}</span> 到{' '}\n                    <span className=\"font-medium\">\n                      {Math.min(pagination.page * pagination.limit, pagination.total)}\n                    </span>{' '}\n                    条，共 <span className=\"font-medium\">{pagination.total}</span> 条记录\n                  </p>\n                </div>\n                <div>\n                  <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                    <button\n                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n                      disabled={pagination.page === 1}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      上一页\n                    </button>\n                    <span className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\">\n                      第 {pagination.page} 页\n                    </span>\n                    <button\n                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                      disabled={pagination.page * pagination.limit >= pagination.total}\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                    >\n                      下一页\n                    </button>\n                  </nav>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,iBAAiB;gBACnB;YACF;QACF;6BAAG;QAAC;QAAiB,WAAW,IAAI;KAAC;IAErC,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;QAAM;KAAQ;IAElB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI;YACJ,IAAI,aAAa;gBACf,WAAW;0CAAY;wBACrB;oBACF;yCAAG,OAAO,0BAA0B;YACtC;YACA;sCAAO;oBACL,IAAI,UAAU,cAAc;gBAC9B;;QACF;6BAAG;QAAC;KAAY;IAEhB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,wBAAgD,OAAzB,WAAW,IAAI,EAAC,WAA0B,OAAjB,WAAW,KAAK,GAAI;gBAChG,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI,IAAI,EAAE;gBACvB,cAAc,CAAA;wBAEL;2BAFc;wBACrB,GAAG,IAAI;wBACP,OAAO,EAAA,mBAAA,KAAK,UAAU,cAAf,uCAAA,iBAAiB,KAAK,KAAI;oBACnC;;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW;eAAI;SAAK;QAExB,IAAI,QAAQ,KAAK,EAAE;YACjB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK,QAAQ,KAAK;QAC/D;QAEA,IAAI,QAAQ,QAAQ,EAAE;YACpB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,QAAQ,QAAQ;QACrE;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,aAAa,QAAQ,MAAM,CAAC,WAAW;YAC7C,WAAW,SAAS,MAAM,CAAC,CAAA,MACzB,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,eACnC,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;QAExC;QAEA,IAAI,QAAQ,SAAS,EAAE;YACrB,MAAM,MAAM,IAAI;YAChB,IAAI,aAAa,IAAI;YAErB,OAAQ,QAAQ,SAAS;gBACvB,KAAK;oBACH,WAAW,QAAQ,CAAC,IAAI,QAAQ,KAAK;oBACrC;gBACF,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;gBACF,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;gBACF,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;YACJ;YAEA,IAAI,QAAQ,SAAS,KAAK,IAAI;gBAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,IAAI,SAAS,KAAK;YAC/D;QACF;QAEA,gBAAgB;IAClB;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,QAAQ,wBAAwB;QAErC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,0JAAC,iNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,0JAAC,yOAAA,CAAA,0BAAuB;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBACH,qBAAO,0JAAC,qOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;YAC1C,KAAK;gBACH,qBAAO,0JAAC,yNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC;gBACE,qBAAO,0JAAC,qOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;QAC5C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc;YAClB,OAAO;gBAAE,OAAO;gBAA2B,MAAM;YAAK;YACtD,SAAS;gBAAE,OAAO;gBAAiC,MAAM;YAAK;YAC9D,MAAM;gBAAE,OAAO;gBAA6B,MAAM;YAAK;YACvD,OAAO;gBAAE,OAAO;gBAA6B,MAAM;YAAK;QAC1D;QACA,MAAM,SAAS,WAAW,CAAC,MAAkC,IAAI,YAAY,IAAI;QACjF,qBACE,0JAAC;YAAK,WAAW,AAAC,2EAAuF,OAAb,OAAO,KAAK;sBACrG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,aAAwC;YAC5C,QAAQ;YACR,MAAM;YACN,OAAO;YACP,KAAK;YACL,UAAU;QACZ;QACA,OAAO,UAAU,CAAC,SAAS,IAAI;IACjC;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,0JAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAM,WAAU;;kEACf,0JAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;wDAChD,WAAU;;;;;;kEAEZ,0JAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,0JAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,0JAAC,qNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG5C,0JAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,0JAAC,6MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;;kEACb,0JAAC,+MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,0JAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAGtD,0JAAC;gDACC,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,WAAU;;kEAEV,0JAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,0JAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,0JAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,0JAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,0JAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;0DAGxB,0JAAC;gDACC,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACnE,WAAU;;kEAEV,0JAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,0JAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,0JAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,0JAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,0JAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,0JAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;0DAG3B,0JAAC;gDACC,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACpE,WAAU;;kEAEV,0JAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,0JAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,0JAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,0JAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,0JAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;0DAGtB,0JAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,MAAM;gDACrB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAOlB,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAM,WAAU;;8DACf,0JAAC;oDAAM,WAAU;8DACf,cAAA,0JAAC;;0EACC,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,0JAAC;oDAAM,WAAU;8DACd,aAAa,GAAG,CAAC,CAAC,oBACjB,0JAAC;4DAAgB,WAAU;;8EACzB,0JAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc,CAAC;;;;;;8EAE1C,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAI,WAAU;;4EACZ,aAAa,IAAI,KAAK;0FACvB,0JAAC;gFAAK,WAAU;0FACb,cAAc,IAAI,KAAK;;;;;;;;;;;;;;;;;8EAI9B,0JAAC;oEAAG,WAAU;8EACX,gBAAgB,IAAI,QAAQ;;;;;;8EAE/B,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAI,WAAU;wEAAoB,OAAO,IAAI,OAAO;kFAClD,IAAI,OAAO;;;;;;;;;;;8EAGhB,0JAAC;oEAAG,WAAU;;wEACX,IAAI,OAAO,kBACV,0JAAC;4EAAQ,WAAU;;8FACjB,0JAAC;oFAAQ,WAAU;8FAAoC;;;;;;8FACvD,0JAAC;oFAAI,WAAU;8FACZ,KAAK,SAAS,CAAC,IAAI,OAAO,EAAE,MAAM;;;;;;;;;;;;wEAIxC,IAAI,SAAS,kBACZ,0JAAC;4EAAI,WAAU;;gFAAwB;gFAAK,IAAI,SAAS;;;;;;;;;;;;;;2DA9BtD,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;kDAwCvB,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;gEAAG,CAAC;wDACnF,UAAU,WAAW,IAAI,KAAK;wDAC9B,WAAU;kEACX;;;;;;kEAGD,0JAAC;wDACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,KAAK,IAAI,GAAG;gEAAE,CAAC;wDACtE,UAAU,WAAW,IAAI,GAAG,WAAW,KAAK,IAAI,WAAW,KAAK;wDAChE,WAAU;kEACX;;;;;;;;;;;;0DAIH,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;kEACC,cAAA,0JAAC;4DAAE,WAAU;;gEAAwB;8EAChC,0JAAC;oEAAK,WAAU;8EAAe,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAG;;;;;;gEAAS;gEAAG;8EACzF,0JAAC;oEAAK,WAAU;8EACb,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;;;;;;gEACxD;gEAAI;8EACR,0JAAC;oEAAK,WAAU;8EAAe,WAAW,KAAK;;;;;;gEAAQ;;;;;;;;;;;;kEAG/D,0JAAC;kEACC,cAAA,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;4EAAG,CAAC;oEACnF,UAAU,WAAW,IAAI,KAAK;oEAC9B,WAAU;8EACX;;;;;;8EAGD,0JAAC;oEAAK,WAAU;;wEAAgH;wEAC3H,WAAW,IAAI;wEAAC;;;;;;;8EAErB,0JAAC;oEACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,MAAM,KAAK,IAAI,GAAG;4EAAE,CAAC;oEACtE,UAAU,WAAW,IAAI,GAAG,WAAW,KAAK,IAAI,WAAW,KAAK;oEAChE,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GAzawB;;QAiBP,0HAAA,CAAA,YAAS;;;KAjBF", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/logs\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}