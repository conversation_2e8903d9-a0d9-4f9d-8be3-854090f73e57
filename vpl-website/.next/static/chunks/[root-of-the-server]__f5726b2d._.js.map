{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/email-settings.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport {\n  EnvelopeIcon,\n  CogIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  EyeSlashIcon\n} from '@heroicons/react/24/outline';\n\ninterface EmailConfig {\n  id: string;\n  name: string;\n  smtpHost: string;\n  smtpPort: number;\n  smtpUser: string;\n  smtpPass: string;\n  smtpFrom: string;\n  smtpFromName: string;\n  isDefault: boolean;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport default function EmailSettingsPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);\n  const [showForm, setShowForm] = useState(false);\n  const [editingConfig, setEditingConfig] = useState<EmailConfig | null>(null);\n  const [testEmail, setTestEmail] = useState('');\n  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);\n  const [showPassword, setShowPassword] = useState<{ [key: string]: boolean }>({});\n  const [formData, setFormData] = useState({\n    name: '',\n    smtpHost: '',\n    smtpPort: 587,\n    smtpUser: '',\n    smtpPass: '',\n    smtpFrom: '',\n    smtpFromName: '',\n    isDefault: false,\n    isActive: true,\n  });\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchEmailConfigs();\n    }\n  }, [isAuthenticated]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchEmailConfigs = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/email-config', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setEmailConfigs(data.data || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch email configs:', error);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      const token = localStorage.getItem('adminToken');\n      const url = editingConfig \n        ? `/api/admin/email-config/${editingConfig.id}`\n        : '/api/admin/email-config';\n      const method = editingConfig ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n\n      if (response.ok) {\n        setShowForm(false);\n        setEditingConfig(null);\n        resetForm();\n        fetchEmailConfigs();\n      }\n    } catch (error) {\n      console.error('Failed to save email config:', error);\n    }\n  };\n\n  const handleEdit = (config: EmailConfig) => {\n    setEditingConfig(config);\n    setFormData({\n      name: config.name,\n      smtpHost: config.smtpHost,\n      smtpPort: config.smtpPort,\n      smtpUser: config.smtpUser,\n      smtpPass: config.smtpPass,\n      smtpFrom: config.smtpFrom,\n      smtpFromName: config.smtpFromName,\n      isDefault: config.isDefault,\n      isActive: config.isActive,\n    });\n    setShowForm(true);\n  };\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('确定要删除这个邮件配置吗？')) return;\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/email-config/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        fetchEmailConfigs();\n      }\n    } catch (error) {\n      console.error('Failed to delete email config:', error);\n    }\n  };\n\n  const handleSetDefault = async (id: string) => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/email-config/${id}/set-default`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        fetchEmailConfigs();\n      }\n    } catch (error) {\n      console.error('Failed to set default config:', error);\n    }\n  };\n\n  const handleTestEmail = async (configId: string) => {\n    if (!testEmail) {\n      alert('请输入测试邮箱地址');\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/email-test', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          configId,\n          testEmail\n        })\n      });\n\n      const result = await response.json();\n      setTestResult(result);\n    } catch (error) {\n      setTestResult({\n        success: false,\n        message: '测试失败：网络错误'\n      });\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      smtpHost: '',\n      smtpPort: 587,\n      smtpUser: '',\n      smtpPass: '',\n      smtpFrom: '',\n      smtpFromName: '',\n      isDefault: false,\n      isActive: true,\n    });\n  };\n\n  const togglePasswordVisibility = (configId: string) => {\n    setShowPassword(prev => ({\n      ...prev,\n      [configId]: !prev[configId]\n    }));\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>邮件配置 - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">邮件配置</h1>\n                <p className=\"text-gray-600\">管理SMTP邮件服务器配置</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => {\n                    resetForm();\n                    setEditingConfig(null);\n                    setShowForm(true);\n                  }}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  添加配置\n                </button>\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Email Configurations List */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">邮件服务器配置</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      配置名称\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      SMTP服务器\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      发件人\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      操作\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {emailConfigs.map((config) => (\n                    <tr key={config.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {config.name}\n                              {config.isDefault && (\n                                <span className=\"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                                  默认\n                                </span>\n                              )}\n                            </div>\n                            <div className=\"text-sm text-gray-500\">\n                              用户: {config.smtpUser}\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm text-gray-900\">{config.smtpHost}:{config.smtpPort}</div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm text-gray-900\">{config.smtpFromName}</div>\n                        <div className=\"text-sm text-gray-500\">{config.smtpFrom}</div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          config.isActive \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {config.isActive ? '启用' : '禁用'}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-2\">\n                          <button\n                            onClick={() => handleEdit(config)}\n                            className=\"text-blue-600 hover:text-blue-900\"\n                            title=\"编辑\"\n                          >\n                            <PencilIcon className=\"h-4 w-4\" />\n                          </button>\n                          {!config.isDefault && (\n                            <button\n                              onClick={() => handleSetDefault(config.id)}\n                              className=\"text-green-600 hover:text-green-900\"\n                              title=\"设为默认\"\n                            >\n                              <CheckCircleIcon className=\"h-4 w-4\" />\n                            </button>\n                          )}\n                          <button\n                            onClick={() => handleDelete(config.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                            title=\"删除\"\n                          >\n                            <TrashIcon className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Email Test Section */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">邮件测试</h3>\n            </div>\n            <div className=\"px-6 py-4\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex-1\">\n                  <input\n                    type=\"email\"\n                    value={testEmail}\n                    onChange={(e) => setTestEmail(e.target.value)}\n                    placeholder=\"输入测试邮箱地址\"\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <select className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\">\n                  <option value=\"\">选择配置</option>\n                  {emailConfigs.filter(c => c.isActive).map(config => (\n                    <option key={config.id} value={config.id}>{config.name}</option>\n                  ))}\n                </select>\n                <button\n                  onClick={() => {\n                    const select = document.querySelector('select') as HTMLSelectElement;\n                    if (select?.value) {\n                      handleTestEmail(select.value);\n                    }\n                  }}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  <EnvelopeIcon className=\"h-4 w-4 mr-2\" />\n                  发送测试邮件\n                </button>\n              </div>\n              \n              {testResult && (\n                <div className={`mt-4 p-4 rounded-md ${\n                  testResult.success \n                    ? 'bg-green-50 border border-green-200' \n                    : 'bg-red-50 border border-red-200'\n                }`}>\n                  <div className=\"flex\">\n                    {testResult.success ? (\n                      <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                    ) : (\n                      <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                    )}\n                    <div className=\"ml-3\">\n                      <p className={`text-sm ${\n                        testResult.success ? 'text-green-800' : 'text-red-800'\n                      }`}>\n                        {testResult.message}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Configuration Form Modal */}\n          {showForm && (\n            <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n              <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n                <div className=\"mt-3\">\n                  <div className=\"flex justify-between items-center mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">\n                      {editingConfig ? '编辑邮件配置' : '添加邮件配置'}\n                    </h3>\n                    <button\n                      onClick={() => {\n                        setShowForm(false);\n                        setEditingConfig(null);\n                        resetForm();\n                      }}\n                      className=\"text-gray-400 hover:text-gray-600\"\n                    >\n                      ×\n                    </button>\n                  </div>\n                  \n                  <form onSubmit={handleSubmit} className=\"space-y-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          配置名称\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.name}\n                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          SMTP服务器\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.smtpHost}\n                          onChange={(e) => setFormData({ ...formData, smtpHost: e.target.value })}\n                          placeholder=\"smtp.example.com\"\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          端口\n                        </label>\n                        <input\n                          type=\"number\"\n                          required\n                          value={formData.smtpPort}\n                          onChange={(e) => setFormData({ ...formData, smtpPort: parseInt(e.target.value) })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          用户名\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.smtpUser}\n                          onChange={(e) => setFormData({ ...formData, smtpUser: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          密码\n                        </label>\n                        <input\n                          type=\"password\"\n                          required\n                          value={formData.smtpPass}\n                          onChange={(e) => setFormData({ ...formData, smtpPass: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          发件人邮箱\n                        </label>\n                        <input\n                          type=\"email\"\n                          required\n                          value={formData.smtpFrom}\n                          onChange={(e) => setFormData({ ...formData, smtpFrom: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                      \n                      <div className=\"md:col-span-2\">\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          发件人名称\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={formData.smtpFromName}\n                          onChange={(e) => setFormData({ ...formData, smtpFromName: e.target.value })}\n                          className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        />\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-4\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.isDefault}\n                          onChange={(e) => setFormData({ ...formData, isDefault: e.target.checked })}\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">设为默认配置</span>\n                      </label>\n                      \n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.isActive}\n                          onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">启用配置</span>\n                      </label>\n                    </div>\n                    \n                    <div className=\"flex justify-end space-x-3 pt-4\">\n                      <button\n                        type=\"button\"\n                        onClick={() => {\n                          setShowForm(false);\n                          setEditingConfig(null);\n                          resetForm();\n                        }}\n                        className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                      >\n                        取消\n                      </button>\n                      <button\n                        type=\"submit\"\n                        className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700\"\n                      >\n                        {editingConfig ? '更新' : '保存'}\n                      </button>\n                    </div>\n                  </form>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AA2Be,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAsB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAgD;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,cAAc;QACd,WAAW;QACX,UAAU;IACZ;IACA,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG,EAAE;IAEL,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,iBAAiB;gBACnB;YACF;QACF;sCAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB,KAAK,IAAI,IAAI,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,MAAM,gBACR,AAAC,2BAA2C,OAAjB,cAAc,EAAE,IAC3C;YACJ,MAAM,SAAS,gBAAgB,QAAQ;YAEvC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;oBAC3B,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY;gBACZ,iBAAiB;gBACjB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB;QACjB,YAAY;YACV,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,cAAc,OAAO,YAAY;YACjC,WAAW,OAAO,SAAS;YAC3B,UAAU,OAAO,QAAQ;QAC3B;QACA,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,kBAAkB;QAE/B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,2BAA6B,OAAH,KAAM;gBAC5D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,2BAA6B,OAAH,IAAG,iBAAe;gBACxE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,WAAW;YACd,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;oBAC3B,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,cAAc;gBACZ,SAAS;gBACT,SAAS;YACX;QACF;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,WAAW;YACX,UAAU;QACZ;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAC7B,CAAC;IACH;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,0JAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,SAAS;oDACP;oDACA,iBAAiB;oDACjB,YAAY;gDACd;gDACA,WAAU;;kEAEV,0JAAC,2MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAM,WAAU;;8DACf,0JAAC;oDAAM,WAAU;8DACf,cAAA,0JAAC;;0EACC,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,0JAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,0JAAC;oDAAM,WAAU;8DACd,aAAa,GAAG,CAAC,CAAC,uBACjB,0JAAC;4DAAmB,WAAU;;8EAC5B,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAI,WAAU;kFACb,cAAA,0JAAC;;8FACC,0JAAC;oFAAI,WAAU;;wFACZ,OAAO,IAAI;wFACX,OAAO,SAAS,kBACf,0JAAC;4FAAK,WAAU;sGAA2G;;;;;;;;;;;;8FAK/H,0JAAC;oFAAI,WAAU;;wFAAwB;wFAChC,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8EAK5B,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAI,WAAU;;4EAAyB,OAAO,QAAQ;4EAAC;4EAAE,OAAO,QAAQ;;;;;;;;;;;;8EAE3E,0JAAC;oEAAG,WAAU;;sFACZ,0JAAC;4EAAI,WAAU;sFAAyB,OAAO,YAAY;;;;;;sFAC3D,0JAAC;4EAAI,WAAU;sFAAyB,OAAO,QAAQ;;;;;;;;;;;;8EAEzD,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAK,WAAW,AAAC,2EAIjB,OAHC,OAAO,QAAQ,GACX,gCACA;kFAEH,OAAO,QAAQ,GAAG,OAAO;;;;;;;;;;;8EAG9B,0JAAC;oEAAG,WAAU;8EACZ,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFACC,SAAS,IAAM,WAAW;gFAC1B,WAAU;gFACV,OAAM;0FAEN,cAAA,0JAAC,+MAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;;;;;;4EAEvB,CAAC,OAAO,SAAS,kBAChB,0JAAC;gFACC,SAAS,IAAM,iBAAiB,OAAO,EAAE;gFACzC,WAAU;gFACV,OAAM;0FAEN,cAAA,0JAAC,yNAAA,CAAA,kBAAe;oFAAC,WAAU;;;;;;;;;;;0FAG/B,0JAAC;gFACC,SAAS,IAAM,aAAa,OAAO,EAAE;gFACrC,WAAU;gFACV,OAAM;0FAEN,cAAA,0JAAC,6MAAA,CAAA,YAAS;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2DAzDpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAqE5B,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,aAAY;4DACZ,WAAU;;;;;;;;;;;kEAGd,0JAAC;wDAAO,WAAU;;0EAChB,0JAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA,uBACxC,0JAAC;oEAAuB,OAAO,OAAO,EAAE;8EAAG,OAAO,IAAI;mEAAzC,OAAO,EAAE;;;;;;;;;;;kEAG1B,0JAAC;wDACC,SAAS;4DACP,MAAM,SAAS,SAAS,aAAa,CAAC;4DACtC,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE;gEACjB,gBAAgB,OAAO,KAAK;4DAC9B;wDACF;wDACA,WAAU;;0EAEV,0JAAC,mNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;4CAK5C,4BACC,0JAAC;gDAAI,WAAW,AAAC,uBAIhB,OAHC,WAAW,OAAO,GACd,wCACA;0DAEJ,cAAA,0JAAC;oDAAI,WAAU;;wDACZ,WAAW,OAAO,iBACjB,0JAAC,yNAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;iFAE3B,0JAAC,yOAAA,CAAA,0BAAuB;4DAAC,WAAU;;;;;;sEAErC,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC;gEAAE,WAAW,AAAC,WAEd,OADC,WAAW,OAAO,GAAG,mBAAmB;0EAEvC,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUhC,0BACC,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAG,WAAU;kEACX,gBAAgB,WAAW;;;;;;kEAE9B,0JAAC;wDACC,SAAS;4DACP,YAAY;4DACZ,iBAAiB;4DACjB;wDACF;wDACA,WAAU;kEACX;;;;;;;;;;;;0DAKH,0JAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;;kFACC,0JAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,0JAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACjE,WAAU;;;;;;;;;;;;0EAId,0JAAC;;kFACC,0JAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,0JAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,aAAY;wEACZ,WAAU;;;;;;;;;;;;0EAId,0JAAC;;kFACC,0JAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,0JAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK;4EAAE;wEAC/E,WAAU;;;;;;;;;;;;0EAId,0JAAC;;kFACC,0JAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,0JAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,0JAAC;;kFACC,0JAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,0JAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,0JAAC;;kFACC,0JAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,0JAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACrE,WAAU;;;;;;;;;;;;0EAId,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,0JAAC;wEACC,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,YAAY;wEAC5B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACzE,WAAU;;;;;;;;;;;;;;;;;;kEAKhB,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAM,WAAU;;kFACf,0JAAC;wEACC,MAAK;wEACL,SAAS,SAAS,SAAS;wEAC3B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,WAAW,EAAE,MAAM,CAAC,OAAO;4EAAC;wEACxE,WAAU;;;;;;kFAEZ,0JAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAG/C,0JAAC;gEAAM,WAAU;;kFACf,0JAAC;wEACC,MAAK;wEACL,SAAS,SAAS,QAAQ;wEAC1B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,OAAO;4EAAC;wEACvE,WAAU;;;;;;kFAEZ,0JAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;kEAIjD,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEACC,MAAK;gEACL,SAAS;oEACP,YAAY;oEACZ,iBAAiB;oEACjB;gEACF;gEACA,WAAU;0EACX;;;;;;0EAGD,0JAAC;gEACC,MAAK;gEACL,WAAU;0EAET,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhD;GA1jBwB;;QAoBP,0HAAA,CAAA,YAAS;;;KApBF", "debugId": null}}, {"offset": {"line": 1664, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/email-settings\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}