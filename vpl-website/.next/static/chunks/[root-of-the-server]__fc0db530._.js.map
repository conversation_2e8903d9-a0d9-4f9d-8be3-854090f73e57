{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/inquiries/%5Bid%5D.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport { \n  ArrowLeftIcon,\n  PencilIcon,\n  TrashIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  ChatBubbleLeftRightIcon,\n  UserIcon,\n  CalendarIcon,\n  ClockIcon,\n  TagIcon\n} from '@heroicons/react/24/outline';\nimport { ContactSubmission } from '../../../types/admin';\n\nexport default function InquiryDetailPage() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [submission, setSubmission] = useState<ContactSubmission | null>(null);\n  const [notes, setNotes] = useState('');\n  const [isEditingNotes, setIsEditingNotes] = useState(false);\n  const router = useRouter();\n  const { id } = router.query;\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated && id) {\n      fetchSubmission();\n    }\n  }, [isAuthenticated, id]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n    setIsAuthenticated(true);\n    setIsLoading(false);\n  };\n\n  const fetchSubmission = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/submissions/${id}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSubmission(data.data);\n        setNotes(data.data.notes || '');\n      } else {\n        router.push('/admin/inquiries');\n      }\n    } catch (error) {\n      console.error('Failed to fetch submission:', error);\n      router.push('/admin/inquiries');\n    }\n  };\n\n  const updateSubmission = async (updates: Partial<ContactSubmission>) => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/submissions/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(updates)\n      });\n\n      if (response.ok) {\n        fetchSubmission();\n      }\n    } catch (error) {\n      console.error('Failed to update submission:', error);\n    }\n  };\n\n  const handleStatusChange = (status: string) => {\n    updateSubmission({ status: status as any });\n  };\n\n  const handlePriorityChange = (priority: string) => {\n    updateSubmission({ priority: priority as any });\n  };\n\n  const handleNotesUpdate = () => {\n    updateSubmission({ notes });\n    setIsEditingNotes(false);\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },\n      contacted: { color: 'bg-blue-100 text-blue-800', text: '已联系' },\n      'in-progress': { color: 'bg-purple-100 text-purple-800', text: '处理中' },\n      completed: { color: 'bg-green-100 text-green-800', text: '已完成' },\n      closed: { color: 'bg-gray-100 text-gray-800', text: '已关闭' },\n    };\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;\n    return (\n      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      low: { color: 'bg-gray-100 text-gray-800', text: '低' },\n      medium: { color: 'bg-blue-100 text-blue-800', text: '中' },\n      high: { color: 'bg-orange-100 text-orange-800', text: '高' },\n      urgent: { color: 'bg-red-100 text-red-800', text: '紧急' },\n    };\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;\n    return (\n      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>\n        {config.text}\n      </span>\n    );\n  };\n\n  const getServiceTypeName = (serviceType: string) => {\n    const types: { [key: string]: string } = {\n      'foreign_trade_lines': '外贸网络线路',\n      'ecommerce_lines': '跨境电商线路',\n      'vpn_services': 'VPN服务',\n      'custom_solution': '定制解决方案',\n    };\n    return types[serviceType] || serviceType;\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated || !submission) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>咨询详情 - {submission.companyName} - VPL后台管理系统</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div className=\"flex items-center\">\n                <button\n                  onClick={() => router.push('/admin/inquiries')}\n                  className=\"mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-md\"\n                >\n                  <ArrowLeftIcon className=\"h-5 w-5\" />\n                </button>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900\">咨询详情</h1>\n                  <p className=\"text-gray-600\">{submission.companyName}</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => router.push(`/admin/inquiries/${id}/edit`)}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <PencilIcon className=\"h-4 w-4 mr-2\" />\n                  编辑\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Main Info */}\n            <div className=\"lg:col-span-2 space-y-6\">\n              {/* Basic Information */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">基本信息</h3>\n                </div>\n                <div className=\"px-6 py-4\">\n                  <dl className=\"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2\">\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">公司名称</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">{submission.companyName}</dd>\n                    </div>\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">联系人</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">{submission.contactPerson}</dd>\n                    </div>\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">服务类型</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">{getServiceTypeName(submission.serviceType)}</dd>\n                    </div>\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">提交时间</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">\n                        {new Date(submission.submittedAt).toLocaleString('zh-CN')}\n                      </dd>\n                    </div>\n                  </dl>\n                </div>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">联系方式</h3>\n                </div>\n                <div className=\"px-6 py-4\">\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center\">\n                      <PhoneIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                      <span className=\"text-sm text-gray-900\">{submission.phone}</span>\n                      <a\n                        href={`tel:${submission.phone}`}\n                        className=\"ml-auto text-blue-600 hover:text-blue-800 text-sm\"\n                      >\n                        拨打电话\n                      </a>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <EnvelopeIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                      <span className=\"text-sm text-gray-900\">{submission.email}</span>\n                      <a\n                        href={`mailto:${submission.email}`}\n                        className=\"ml-auto text-blue-600 hover:text-blue-800 text-sm\"\n                      >\n                        发送邮件\n                      </a>\n                    </div>\n                    {submission.wechat && (\n                      <div className=\"flex items-center\">\n                        <ChatBubbleLeftRightIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                        <span className=\"text-sm text-gray-500\">微信：</span>\n                        <span className=\"text-sm text-gray-900 ml-1\">{submission.wechat}</span>\n                      </div>\n                    )}\n                    {submission.qq && (\n                      <div className=\"flex items-center\">\n                        <UserIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                        <span className=\"text-sm text-gray-500\">QQ：</span>\n                        <span className=\"text-sm text-gray-900 ml-1\">{submission.qq}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Message */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">详细需求</h3>\n                </div>\n                <div className=\"px-6 py-4\">\n                  <p className=\"text-sm text-gray-900 whitespace-pre-wrap\">{submission.message}</p>\n                </div>\n              </div>\n\n              {/* Notes */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                  <div className=\"flex justify-between items-center\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">备注</h3>\n                    {!isEditingNotes && (\n                      <button\n                        onClick={() => setIsEditingNotes(true)}\n                        className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                      >\n                        编辑\n                      </button>\n                    )}\n                  </div>\n                </div>\n                <div className=\"px-6 py-4\">\n                  {isEditingNotes ? (\n                    <div>\n                      <textarea\n                        value={notes}\n                        onChange={(e) => setNotes(e.target.value)}\n                        rows={4}\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        placeholder=\"添加备注...\"\n                      />\n                      <div className=\"mt-3 flex justify-end space-x-3\">\n                        <button\n                          onClick={() => {\n                            setNotes(submission.notes || '');\n                            setIsEditingNotes(false);\n                          }}\n                          className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                        >\n                          取消\n                        </button>\n                        <button\n                          onClick={handleNotesUpdate}\n                          className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700\"\n                        >\n                          保存\n                        </button>\n                      </div>\n                    </div>\n                  ) : (\n                    <p className=\"text-sm text-gray-900 whitespace-pre-wrap\">\n                      {submission.notes || '暂无备注'}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"space-y-6\">\n              {/* Status and Priority */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">状态管理</h3>\n                </div>\n                <div className=\"px-6 py-4 space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">状态</label>\n                    <select\n                      value={submission.status}\n                      onChange={(e) => handleStatusChange(e.target.value)}\n                      className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"pending\">待处理</option>\n                      <option value=\"contacted\">已联系</option>\n                      <option value=\"in-progress\">处理中</option>\n                      <option value=\"completed\">已完成</option>\n                      <option value=\"closed\">已关闭</option>\n                    </select>\n                    <div className=\"mt-2\">\n                      {getStatusBadge(submission.status)}\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">优先级</label>\n                    <select\n                      value={submission.priority}\n                      onChange={(e) => handlePriorityChange(e.target.value)}\n                      className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"low\">低</option>\n                      <option value=\"medium\">中</option>\n                      <option value=\"high\">高</option>\n                      <option value=\"urgent\">紧急</option>\n                    </select>\n                    <div className=\"mt-2\">\n                      {getPriorityBadge(submission.priority)}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Timeline */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">时间线</h3>\n                </div>\n                <div className=\"px-6 py-4\">\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center text-sm\">\n                      <CalendarIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                      <span className=\"text-gray-500\">提交时间：</span>\n                      <span className=\"text-gray-900 ml-1\">\n                        {new Date(submission.submittedAt).toLocaleString('zh-CN')}\n                      </span>\n                    </div>\n                    {submission.lastContactedAt && (\n                      <div className=\"flex items-center text-sm\">\n                        <ClockIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                        <span className=\"text-gray-500\">最后联系：</span>\n                        <span className=\"text-gray-900 ml-1\">\n                          {new Date(submission.lastContactedAt).toLocaleString('zh-CN')}\n                        </span>\n                      </div>\n                    )}\n                    {submission.followUpDate && (\n                      <div className=\"flex items-center text-sm\">\n                        <TagIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                        <span className=\"text-gray-500\">跟进日期：</span>\n                        <span className=\"text-gray-900 ml-1\">\n                          {new Date(submission.followUpDate).toLocaleDateString('zh-CN')}\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Quick Actions */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">快速操作</h3>\n                </div>\n                <div className=\"px-6 py-4 space-y-3\">\n                  <button\n                    onClick={() => handleStatusChange('contacted')}\n                    className=\"w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md\"\n                  >\n                    标记为已联系\n                  </button>\n                  <button\n                    onClick={() => handleStatusChange('completed')}\n                    className=\"w-full text-left px-3 py-2 text-sm text-green-600 hover:bg-green-50 rounded-md\"\n                  >\n                    标记为已完成\n                  </button>\n                  <button\n                    onClick={() => updateSubmission({ followUpDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() })}\n                    className=\"w-full text-left px-3 py-2 text-sm text-orange-600 hover:bg-orange-50 rounded-md\"\n                  >\n                    设置7天后跟进\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAA4B;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,KAAK;IAE3B,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG,EAAE;IAEL,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,mBAAmB,IAAI;gBACzB;YACF;QACF;sCAAG;QAAC;QAAiB;KAAG;IAExB,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,0BAA4B,OAAH,KAAM;gBAC3D,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc,KAAK,IAAI;gBACvB,SAAS,KAAK,IAAI,CAAC,KAAK,IAAI;YAC9B,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,0BAA4B,OAAH,KAAM;gBAC3D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;oBAC3B,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;YAAE,QAAQ;QAAc;IAC3C;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB;YAAE,UAAU;QAAgB;IAC/C;IAEA,MAAM,oBAAoB;QACxB,iBAAiB;YAAE;QAAM;QACzB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YAC/D,WAAW;gBAAE,OAAO;gBAA6B,MAAM;YAAM;YAC7D,eAAe;gBAAE,OAAO;gBAAiC,MAAM;YAAM;YACrE,WAAW;gBAAE,OAAO;gBAA+B,MAAM;YAAM;YAC/D,QAAQ;gBAAE,OAAO;gBAA6B,MAAM;YAAM;QAC5D;QACA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBACE,0JAAC;YAAK,WAAW,AAAC,uEAAmF,OAAb,OAAO,KAAK;sBACjG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAAiB;YACrB,KAAK;gBAAE,OAAO;gBAA6B,MAAM;YAAI;YACrD,QAAQ;gBAAE,OAAO;gBAA6B,MAAM;YAAI;YACxD,MAAM;gBAAE,OAAO;gBAAiC,MAAM;YAAI;YAC1D,QAAQ;gBAAE,OAAO;gBAA2B,MAAM;YAAK;QACzD;QACA,MAAM,SAAS,cAAc,CAAC,SAAwC,IAAI,eAAe,MAAM;QAC/F,qBACE,0JAAC;YAAK,WAAW,AAAC,uEAAmF,OAAb,OAAO,KAAK;sBACjG,OAAO,IAAI;;;;;;IAGlB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAmC;YACvC,uBAAuB;YACvB,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;QACrB;QACA,OAAO,KAAK,CAAC,YAAY,IAAI;IAC/B;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,mBAAmB,CAAC,YAAY;QACnC,OAAO;IACT;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;;4BAAM;4BAAQ,WAAW,WAAW;4BAAC;;;;;;;kCACtC,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DAEV,cAAA,0JAAC,qNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,0JAAC;;kEACC,0JAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,0JAAC;wDAAE,WAAU;kEAAiB,WAAW,WAAW;;;;;;;;;;;;;;;;;;kDAGxD,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,oBAAsB,OAAH,IAAG;4CAClD,WAAU;;8DAEV,0JAAC,+MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjD,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;8CAEb,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;8DAEpD,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAG,WAAU;;0EACZ,0JAAC;;kFACC,0JAAC;wEAAG,WAAU;kFAAoC;;;;;;kFAClD,0JAAC;wEAAG,WAAU;kFAA8B,WAAW,WAAW;;;;;;;;;;;;0EAEpE,0JAAC;;kFACC,0JAAC;wEAAG,WAAU;kFAAoC;;;;;;kFAClD,0JAAC;wEAAG,WAAU;kFAA8B,WAAW,aAAa;;;;;;;;;;;;0EAEtE,0JAAC;;kFACC,0JAAC;wEAAG,WAAU;kFAAoC;;;;;;kFAClD,0JAAC;wEAAG,WAAU;kFAA8B,mBAAmB,WAAW,WAAW;;;;;;;;;;;;0EAEvF,0JAAC;;kFACC,0JAAC;wEAAG,WAAU;kFAAoC;;;;;;kFAClD,0JAAC;wEAAG,WAAU;kFACX,IAAI,KAAK,WAAW,WAAW,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ3D,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;8DAEpD,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,6MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;kFACrB,0JAAC;wEAAK,WAAU;kFAAyB,WAAW,KAAK;;;;;;kFACzD,0JAAC;wEACC,MAAM,AAAC,OAAuB,OAAjB,WAAW,KAAK;wEAC7B,WAAU;kFACX;;;;;;;;;;;;0EAIH,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,mNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;kFACxB,0JAAC;wEAAK,WAAU;kFAAyB,WAAW,KAAK;;;;;;kFACzD,0JAAC;wEACC,MAAM,AAAC,UAA0B,OAAjB,WAAW,KAAK;wEAChC,WAAU;kFACX;;;;;;;;;;;;4DAIF,WAAW,MAAM,kBAChB,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,yOAAA,CAAA,0BAAuB;wEAAC,WAAU;;;;;;kFACnC,0JAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,0JAAC;wEAAK,WAAU;kFAA8B,WAAW,MAAM;;;;;;;;;;;;4DAGlE,WAAW,EAAE,kBACZ,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,2MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,0JAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,0JAAC;wEAAK,WAAU;kFAA8B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQrE,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;8DAEpD,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAE,WAAU;kEAA6C,WAAW,OAAO;;;;;;;;;;;;;;;;;sDAKhF,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAG,WAAU;0EAAoC;;;;;;4DACjD,CAAC,gCACA,0JAAC;gEACC,SAAS,IAAM,kBAAkB;gEACjC,WAAU;0EACX;;;;;;;;;;;;;;;;;8DAMP,0JAAC;oDAAI,WAAU;8DACZ,+BACC,0JAAC;;0EACC,0JAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,MAAM;gEACN,WAAU;gEACV,aAAY;;;;;;0EAEd,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEACC,SAAS;4EACP,SAAS,WAAW,KAAK,IAAI;4EAC7B,kBAAkB;wEACpB;wEACA,WAAU;kFACX;;;;;;kFAGD,0JAAC;wEACC,SAAS;wEACT,WAAU;kFACX;;;;;;;;;;;;;;;;;6EAML,0JAAC;wDAAE,WAAU;kEACV,WAAW,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;8CAQ/B,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;8DAEpD,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;;8EACC,0JAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAChE,0JAAC;oEACC,OAAO,WAAW,MAAM;oEACxB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oEAClD,WAAU;;sFAEV,0JAAC;4EAAO,OAAM;sFAAU;;;;;;sFACxB,0JAAC;4EAAO,OAAM;sFAAY;;;;;;sFAC1B,0JAAC;4EAAO,OAAM;sFAAc;;;;;;sFAC5B,0JAAC;4EAAO,OAAM;sFAAY;;;;;;sFAC1B,0JAAC;4EAAO,OAAM;sFAAS;;;;;;;;;;;;8EAEzB,0JAAC;oEAAI,WAAU;8EACZ,eAAe,WAAW,MAAM;;;;;;;;;;;;sEAIrC,0JAAC;;8EACC,0JAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAChE,0JAAC;oEACC,OAAO,WAAW,QAAQ;oEAC1B,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oEACpD,WAAU;;sFAEV,0JAAC;4EAAO,OAAM;sFAAM;;;;;;sFACpB,0JAAC;4EAAO,OAAM;sFAAS;;;;;;sFACvB,0JAAC;4EAAO,OAAM;sFAAO;;;;;;sFACrB,0JAAC;4EAAO,OAAM;sFAAS;;;;;;;;;;;;8EAEzB,0JAAC;oEAAI,WAAU;8EACZ,iBAAiB,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAO7C,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;8DAEpD,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,mNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;kFACxB,0JAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,0JAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,WAAW,WAAW,EAAE,cAAc,CAAC;;;;;;;;;;;;4DAGpD,WAAW,eAAe,kBACzB,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,6MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;kFACrB,0JAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,0JAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,WAAW,eAAe,EAAE,cAAc,CAAC;;;;;;;;;;;;4DAI1D,WAAW,YAAY,kBACtB,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,yMAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,0JAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,0JAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,WAAW,YAAY,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASlE,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;8DAEpD,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DACC,SAAS,IAAM,mBAAmB;4DAClC,WAAU;sEACX;;;;;;sEAGD,0JAAC;4DACC,SAAS,IAAM,mBAAmB;4DAClC,WAAU;sEACX;;;;;;sEAGD,0JAAC;4DACC,SAAS,IAAM,iBAAiB;oEAAE,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gEAAG;4DAC7G,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA5awB;;QAMP,0HAAA,CAAA,YAAS;;;KANF", "debugId": null}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/inquiries/[id]\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}