{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/lib/notifications.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nclass NotificationService {\n  private socket: Socket | null = null;\n  private isConnected = false;\n  private connectionAttempts = 0;\n  private maxRetries = 3;\n\n  connect() {\n    if (typeof window !== 'undefined' && !this.socket && this.connectionAttempts < this.maxRetries) {\n      try {\n        this.connectionAttempts++;\n        console.log(`Attempting to connect to Socket.IO server (attempt ${this.connectionAttempts})`);\n\n        this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {\n          path: '/api/socket',\n          transports: ['polling', 'websocket'],\n          timeout: 5000,\n          forceNew: true,\n        });\n\n        this.socket.on('connect', () => {\n          console.log('Connected to notification service');\n          this.isConnected = true;\n          this.connectionAttempts = 0; // Reset on successful connection\n        });\n\n        this.socket.on('disconnect', () => {\n          console.log('Disconnected from notification service');\n          this.isConnected = false;\n        });\n\n        this.socket.on('connect_error', (error) => {\n          console.warn('Socket.IO connection error:', error.message);\n          this.isConnected = false;\n\n          if (this.connectionAttempts >= this.maxRetries) {\n            console.warn('Max connection attempts reached. Socket.IO features will be disabled.');\n          }\n        });\n\n        this.socket.on('admin-joined', (data) => {\n          console.log('Successfully joined admin room:', data);\n        });\n\n      } catch (error) {\n        console.error('Failed to initialize Socket.IO:', error);\n        this.isConnected = false;\n      }\n    }\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n    }\n  }\n\n  joinAdminRoom() {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('join-admin');\n    } else {\n      console.warn('Cannot join admin room: Socket.IO not connected');\n    }\n  }\n\n  onNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.on('new-submission', callback);\n    } else {\n      console.warn('Cannot listen for new submissions: Socket.IO not available');\n    }\n  }\n\n  offNewSubmission(callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.off('new-submission', callback);\n    }\n  }\n\n  emitNewSubmission(data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('new-submission', data);\n    } else {\n      console.warn('Cannot emit new submission: Socket.IO not connected');\n    }\n  }\n\n  // Admin notification methods\n  notifyAdmins(type: string, data: any) {\n    if (this.socket && this.isConnected) {\n      this.socket.to('admin').emit('admin-notification', {\n        type,\n        data,\n        timestamp: new Date().toISOString(),\n      });\n    } else {\n      console.warn('Cannot notify admins: Socket.IO not connected');\n    }\n  }\n\n  onAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.on('admin-notification', callback);\n    } else {\n      console.warn('Cannot listen for admin notifications: Socket.IO not available');\n    }\n  }\n\n  offAdminNotification(callback: (notification: any) => void) {\n    if (this.socket) {\n      this.socket.off('admin-notification', callback);\n    }\n  }\n\n  // Check if service is available\n  isAvailable(): boolean {\n    return this.socket !== null && this.isConnected;\n  }\n\n  // Get connection status\n  getStatus(): string {\n    if (!this.socket) return 'not-initialized';\n    if (this.isConnected) return 'connected';\n    return 'disconnected';\n  }\n}\n\nexport const notificationService = new NotificationService();\n\n// Browser notification utilities\nexport const requestNotificationPermission = async (): Promise<boolean> => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return false;\n  }\n\n  if (Notification.permission === 'granted') {\n    return true;\n  }\n\n  if (Notification.permission === 'denied') {\n    return false;\n  }\n\n  const permission = await Notification.requestPermission();\n  return permission === 'granted';\n};\n\nexport const showBrowserNotification = (title: string, options?: NotificationOptions) => {\n  if (typeof window === 'undefined' || !('Notification' in window)) {\n    return;\n  }\n\n  if (Notification.permission === 'granted') {\n    new Notification(title, {\n      icon: '/favicon.ico',\n      badge: '/favicon.ico',\n      ...options,\n    });\n  }\n};\n\nexport default NotificationService;\n"], "names": [], "mappings": ";;;;;;AAcyB;;AAdzB;AAAA;;;AAEA,MAAM;IAMJ,UAAU;QACR,IAAI,aAAkB,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,EAAE;YAC9F,IAAI;gBACF,IAAI,CAAC,kBAAkB;gBACvB,QAAQ,GAAG,CAAC,AAAC,sDAA6E,OAAxB,IAAI,CAAC,kBAAkB,EAAC;gBAE1F,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,2KAAA,CAAA,KAAE,AAAD,EAAE,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE;oBAC7E,MAAM;oBACN,YAAY;wBAAC;wBAAW;qBAAY;oBACpC,SAAS;oBACT,UAAU;gBACZ;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;oBACxB,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;oBACnB,IAAI,CAAC,kBAAkB,GAAG,GAAG,iCAAiC;gBAChE;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;gBACrB;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;oBAC/B,QAAQ,IAAI,CAAC,+BAA+B,MAAM,OAAO;oBACzD,IAAI,CAAC,WAAW,GAAG;oBAEnB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,EAAE;wBAC9C,QAAQ,IAAI,CAAC;oBACf;gBACF;gBAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC;oBAC9B,QAAQ,GAAG,CAAC,mCAAmC;gBACjD;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,IAAI,CAAC,WAAW,GAAG;YACrB;QACF;IACF;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,gBAAgB;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,gBAAgB,QAA6B,EAAE;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB;QACnC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,iBAAiB,QAA6B,EAAE;QAC9C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB;QACpC;IACF;IAEA,kBAAkB,IAAS,EAAE;QAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;QACrC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,6BAA6B;IAC7B,aAAa,IAAY,EAAE,IAAS,EAAE;QACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,sBAAsB;gBACjD;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,oBAAoB,QAAqC,EAAE;QACzD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,sBAAsB;QACvC,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,qBAAqB,QAAqC,EAAE;QAC1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB;QACxC;IACF;IAEA,gCAAgC;IAChC,cAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,WAAW;IACjD;IAEA,wBAAwB;IACxB,YAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO;QAC7B,OAAO;IACT;;QA5HA,wKAAQ,UAAwB;QAChC,wKAAQ,eAAc;QACtB,wKAAQ,sBAAqB;QAC7B,wKAAQ,cAAa;;AA0HvB;AAEO,MAAM,sBAAsB,IAAI;AAGhC,MAAM,gCAAgC;IAC3C,IAAI,aAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE,OAAO;IACT;IAEA,IAAI,aAAa,UAAU,KAAK,WAAW;QACzC,OAAO;IACT;IAEA,IAAI,aAAa,UAAU,KAAK,UAAU;QACxC,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,aAAa,iBAAiB;IACvD,OAAO,eAAe;AACxB;AAEO,MAAM,0BAA0B,CAAC,OAAe;IACrD,IAAI,aAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;QAChE;IACF;IAEA,IAAI,aAAa,UAAU,KAAK,WAAW;QACzC,IAAI,aAAa,OAAO;YACtB,MAAM;YACN,OAAO;YACP,GAAG,OAAO;QACZ;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/NotificationCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { BellIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { notificationService, showBrowserNotification, requestNotificationPermission } from '../../lib/notifications';\n\ninterface Notification {\n  id: string;\n  type: 'new-submission' | 'system' | 'warning';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n\nexport default function NotificationCenter() {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [connectionStatus, setConnectionStatus] = useState<string>('not-initialized');\n\n  useEffect(() => {\n    // Initialize notification service with error handling\n    try {\n      notificationService.connect();\n\n      // Check connection status periodically\n      const statusInterval = setInterval(() => {\n        setConnectionStatus(notificationService.getStatus());\n      }, 2000);\n\n      // Try to join admin room after a short delay\n      setTimeout(() => {\n        notificationService.joinAdminRoom();\n      }, 1000);\n\n      // Request browser notification permission\n      requestNotificationPermission();\n\n      return () => {\n        clearInterval(statusInterval);\n      };\n    } catch (error) {\n      console.error('Failed to initialize notification service:', error);\n      setConnectionStatus('error');\n    }\n\n    // Listen for new submissions\n    const handleNewSubmission = (data: any) => {\n      const notification: Notification = {\n        id: Date.now().toString(),\n        type: 'new-submission',\n        title: '新的客户咨询',\n        message: `${data.companyName} 提交了新的咨询`,\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n\n      setNotifications(prev => [notification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(notification.title, {\n        body: notification.message,\n        tag: 'new-submission',\n      });\n    };\n\n    // Listen for admin notifications\n    const handleAdminNotification = (notification: any) => {\n      const newNotification: Notification = {\n        id: Date.now().toString(),\n        type: notification.type,\n        title: notification.title || '系统通知',\n        message: notification.message,\n        timestamp: notification.timestamp,\n        read: false,\n      };\n\n      setNotifications(prev => [newNotification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Show browser notification\n      showBrowserNotification(newNotification.title, {\n        body: newNotification.message,\n        tag: notification.type,\n      });\n    };\n\n    notificationService.onNewSubmission(handleNewSubmission);\n    notificationService.onAdminNotification(handleAdminNotification);\n\n    return () => {\n      notificationService.offNewSubmission(handleNewSubmission);\n      notificationService.offAdminNotification(handleAdminNotification);\n      notificationService.disconnect();\n    };\n  }, []);\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id\n          ? { ...notification, read: true }\n          : notification\n      )\n    );\n    setUnreadCount(prev => Math.max(0, prev - 1));\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n    setUnreadCount(0);\n  };\n\n  const removeNotification = (id: string) => {\n    const notification = notifications.find(n => n.id === id);\n    if (notification && !notification.read) {\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    }\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'new-submission':\n        return '📧';\n      case 'system':\n        return '⚙️';\n      case 'warning':\n        return '⚠️';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) {\n      return '刚刚';\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes}分钟前`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)}小时前`;\n    } else {\n      return date.toLocaleDateString('zh-CN');\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\"\n      >\n        <BellIcon className=\"h-6 w-6\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n        {/* Connection status indicator */}\n        <span className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full ${\n          connectionStatus === 'connected' ? 'bg-green-500' :\n          connectionStatus === 'disconnected' ? 'bg-yellow-500' :\n          'bg-gray-400'\n        }`} title={`Socket.IO: ${connectionStatus}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 max-h-96 overflow-hidden\">\n            <div className=\"p-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-medium text-gray-900\">通知</h3>\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-500\"\n                  >\n                    全部标记为已读\n                  </button>\n                )}\n              </div>\n              {/* Connection status */}\n              <div className=\"mt-2 text-xs text-gray-500\">\n                实时通知: {\n                  connectionStatus === 'connected' ? '🟢 已连接' :\n                  connectionStatus === 'disconnected' ? '🟡 连接中断' :\n                  connectionStatus === 'error' ? '🔴 连接错误' :\n                  '⚪ 未连接'\n                }\n              </div>\n            </div>\n            \n            <div className=\"max-h-64 overflow-y-auto\">\n              {notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  暂无通知\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${\n                      !notification.read ? 'bg-blue-50' : ''\n                    }`}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-start space-x-3 flex-1\">\n                        <span className=\"text-lg\">\n                          {getNotificationIcon(notification.type)}\n                        </span>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {notification.title}\n                          </p>\n                          <p className=\"text-sm text-gray-600 mt-1\">\n                            {notification.message}\n                          </p>\n                          <p className=\"text-xs text-gray-400 mt-1\">\n                            {formatTimestamp(notification.timestamp)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {!notification.read && (\n                          <button\n                            onClick={() => markAsRead(notification.id)}\n                            className=\"w-2 h-2 bg-blue-500 rounded-full\"\n                            title=\"标记为已读\"\n                          />\n                        )}\n                        <button\n                          onClick={() => removeNotification(notification.id)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAee,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;wCAAE;YACR,sDAAsD;YACtD,IAAI;gBACF,uHAAA,CAAA,sBAAmB,CAAC,OAAO;gBAE3B,uCAAuC;gBACvC,MAAM,iBAAiB;mEAAY;wBACjC,oBAAoB,uHAAA,CAAA,sBAAmB,CAAC,SAAS;oBACnD;kEAAG;gBAEH,6CAA6C;gBAC7C;oDAAW;wBACT,uHAAA,CAAA,sBAAmB,CAAC,aAAa;oBACnC;mDAAG;gBAEH,0CAA0C;gBAC1C,CAAA,GAAA,uHAAA,CAAA,gCAA6B,AAAD;gBAE5B;oDAAO;wBACL,cAAc;oBAChB;;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8CAA8C;gBAC5D,oBAAoB;YACtB;YAEA,6BAA6B;YAC7B,MAAM;oEAAsB,CAAC;oBAC3B,MAAM,eAA6B;wBACjC,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM;wBACN,OAAO;wBACP,SAAS,AAAC,GAAmB,OAAjB,KAAK,WAAW,EAAC;wBAC7B,WAAW,IAAI,OAAO,WAAW;wBACjC,MAAM;oBACR;oBAEA;4EAAiB,CAAA,OAAQ;gCAAC;mCAAiB;6BAAK;;oBAChD;4EAAe,CAAA,OAAQ,OAAO;;oBAE9B,4BAA4B;oBAC5B,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa,KAAK,EAAE;wBAC1C,MAAM,aAAa,OAAO;wBAC1B,KAAK;oBACP;gBACF;;YAEA,iCAAiC;YACjC,MAAM;wEAA0B,CAAC;oBAC/B,MAAM,kBAAgC;wBACpC,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM,aAAa,IAAI;wBACvB,OAAO,aAAa,KAAK,IAAI;wBAC7B,SAAS,aAAa,OAAO;wBAC7B,WAAW,aAAa,SAAS;wBACjC,MAAM;oBACR;oBAEA;gFAAiB,CAAA,OAAQ;gCAAC;mCAAoB;6BAAK;;oBACnD;gFAAe,CAAA,OAAQ,OAAO;;oBAE9B,4BAA4B;oBAC5B,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE,gBAAgB,KAAK,EAAE;wBAC7C,MAAM,gBAAgB,OAAO;wBAC7B,KAAK,aAAa,IAAI;oBACxB;gBACF;;YAEA,uHAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;YACpC,uHAAA,CAAA,sBAAmB,CAAC,mBAAmB,CAAC;YAExC;gDAAO;oBACL,uHAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC;oBACrC,uHAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;oBACzC,uHAAA,CAAA,sBAAmB,CAAC,UAAU;gBAChC;;QACF;uCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAC9B;QAGR,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IAC5C;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;QAE3D,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,gBAAgB,CAAC,aAAa,IAAI,EAAE;YACtC,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C;QACA,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,AAAC,GAAgB,OAAd,eAAc;QAC1B,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,AAAC,GAAiC,OAA/B,KAAK,KAAK,CAAC,gBAAgB,KAAI;QAC3C,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,0JAAC,2MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBACnB,cAAc,mBACb,0JAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;kCAIhC,0JAAC;wBAAK,WAAW,AAAC,oDAIjB,OAHC,qBAAqB,cAAc,iBACnC,qBAAqB,iBAAiB,kBACtC;wBACE,OAAO,AAAC,cAA8B,OAAjB;;;;;;;;;;;;YAG1B,wBACC;;kCACE,0JAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,cAAc,mBACb,0JAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAML,0JAAC;wCAAI,WAAU;;4CAA6B;4CAExC,qBAAqB,cAAc,WACnC,qBAAqB,iBAAiB,YACtC,qBAAqB,UAAU,YAC/B;;;;;;;;;;;;;0CAKN,0JAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,0JAAC;oCAAI,WAAU;8CAAgC;;;;;2CAI/C,cAAc,GAAG,CAAC,CAAC,6BACjB,0JAAC;wCAEC,WAAW,AAAC,iDAEX,OADC,CAAC,aAAa,IAAI,GAAG,eAAe;kDAGtC,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAK,WAAU;sEACb,oBAAoB,aAAa,IAAI;;;;;;sEAExC,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAE,WAAU;8EACV,aAAa,KAAK;;;;;;8EAErB,0JAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,0JAAC;oEAAE,WAAU;8EACV,gBAAgB,aAAa,SAAS;;;;;;;;;;;;;;;;;;8DAI7C,0JAAC;oDAAI,WAAU;;wDACZ,CAAC,aAAa,IAAI,kBACjB,0JAAC;4DACC,SAAS,IAAM,WAAW,aAAa,EAAE;4DACzC,WAAU;4DACV,OAAM;;;;;;sEAGV,0JAAC;4DACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,WAAU;sEAEV,cAAA,0JAAC,6MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAlCtB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AA+CxC;GApPwB;KAAA", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Link from 'next/link';\nimport { \n  HomeIcon,\n  UsersIcon,\n  EnvelopeIcon,\n  ChartBarIcon,\n  CogIcon,\n  DocumentTextIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport NotificationCenter from './NotificationCenter';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: '仪表板', href: '/admin/dashboard', icon: HomeIcon },\n  { name: '咨询管理', href: '/admin/inquiries', icon: EnvelopeIcon },\n  { name: '用户管理', href: '/admin/users', icon: UsersIcon },\n  { name: '数据分析', href: '/admin/analytics', icon: ChartBarIcon },\n  { \n    name: '系统设置', \n    icon: CogIcon,\n    children: [\n      { name: '基本配置', href: '/admin/system/config', icon: CogIcon },\n      { name: '邮件设置', href: '/admin/email-settings', icon: EnvelopeIcon },\n      { name: '多语言管理', href: '/admin/system/localization', icon: GlobeAltIcon },\n      { name: '安全设置', href: '/admin/system/security', icon: ShieldCheckIcon },\n    ]\n  },\n  { \n    name: '内容管理', \n    icon: DocumentTextIcon,\n    children: [\n      { name: '页面内容', href: '/admin/content/pages', icon: DocumentTextIcon },\n      { name: '服务类型', href: '/admin/content/services', icon: ServerIcon },\n    ]\n  },\n];\n\nexport default function AdminLayout({ children, title }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedItems, setExpandedItems] = useState<string[]>([]);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [userInfo, setUserInfo] = useState<any>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    fetch('/api/admin/verify', {\n      headers: { 'Authorization': `Bearer ${token}` }\n    })\n    .then(response => response.json())\n    .then(result => {\n      if (result.success) {\n        setIsAuthenticated(true);\n        setUserInfo(result.user);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const toggleExpanded = (itemName: string) => {\n    setExpandedItems(prev => \n      prev.includes(itemName) \n        ? prev.filter(name => name !== itemName)\n        : [...prev, itemName]\n    );\n  };\n\n  const isCurrentPath = (href: string) => {\n    return router.pathname === href || router.pathname.startsWith(href + '/');\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XMarkIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <SidebarContent \n            navigation={navigation} \n            expandedItems={expandedItems}\n            toggleExpanded={toggleExpanded}\n            isCurrentPath={isCurrentPath}\n          />\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <SidebarContent \n            navigation={navigation} \n            expandedItems={expandedItems}\n            toggleExpanded={toggleExpanded}\n            isCurrentPath={isCurrentPath}\n          />\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Top bar */}\n        <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Bars3Icon className=\"h-6 w-6\" />\n          </button>\n          \n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex items-center\">\n              {title && (\n                <h1 className=\"text-2xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            \n            <div className=\"ml-4 flex items-center md:ml-6 space-x-4\">\n              <NotificationCenter />\n              \n              {/* User menu */}\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-700\">{userInfo?.fullName || userInfo?.username}</div>\n                  <div className=\"text-gray-500\">{userInfo?.role}</div>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-400 hover:text-gray-600\"\n                  title=\"退出登录\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n\nfunction SidebarContent({ \n  navigation, \n  expandedItems, \n  toggleExpanded, \n  isCurrentPath \n}: {\n  navigation: any[];\n  expandedItems: string[];\n  toggleExpanded: (name: string) => void;\n  isCurrentPath: (href: string) => boolean;\n}) {\n  return (\n    <div className=\"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\">\n      {/* Logo */}\n      <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n        <div className=\"flex items-center flex-shrink-0 px-4\">\n          <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg mr-3\">\n            <span className=\"text-white font-bold\">VPL</span>\n          </div>\n          <span className=\"text-xl font-semibold text-gray-900\">管理后台</span>\n        </div>\n        \n        {/* Navigation */}\n        <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n          {navigation.map((item) => (\n            <div key={item.name}>\n              {item.children ? (\n                <div>\n                  <button\n                    onClick={() => toggleExpanded(item.name)}\n                    className={`group w-full flex items-center pl-2 pr-1 py-2 text-left text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      expandedItems.includes(item.name)\n                        ? 'bg-gray-100 text-gray-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                    {item.name}\n                    <svg\n                      className={`ml-auto h-5 w-5 transform transition-colors duration-150 ${\n                        expandedItems.includes(item.name) ? 'rotate-90 text-gray-400' : 'text-gray-300'\n                      }`}\n                      viewBox=\"0 0 20 20\"\n                      fill=\"currentColor\"\n                    >\n                      <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </button>\n                  {expandedItems.includes(item.name) && (\n                    <div className=\"mt-1 space-y-1\">\n                      {item.children.map((subItem: any) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          className={`group w-full flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md ${\n                            isCurrentPath(subItem.href)\n                              ? 'bg-blue-100 text-blue-700'\n                              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                          }`}\n                        >\n                          <subItem.icon className=\"mr-3 h-4 w-4\" />\n                          {subItem.name}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <Link\n                  href={item.href}\n                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                    isCurrentPath(item.href)\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                >\n                  <item.icon className={`mr-3 h-5 w-5 ${\n                    isCurrentPath(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'\n                  }`} />\n                  {item.name}\n                </Link>\n              )}\n            </div>\n          ))}\n        </nav>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAoB,MAAM,2MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,mNAAA,CAAA,eAAY;IAAC;IAC7D;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,6MAAA,CAAA,YAAS;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,mNAAA,CAAA,eAAY;IAAC;IAC7D;QACE,MAAM;QACN,MAAM,yMAAA,CAAA,UAAO;QACb,UAAU;YACR;gBAAE,MAAM;gBAAQ,MAAM;gBAAwB,MAAM,yMAAA,CAAA,UAAO;YAAC;YAC5D;gBAAE,MAAM;gBAAQ,MAAM;gBAAyB,MAAM,mNAAA,CAAA,eAAY;YAAC;YAClE;gBAAE,MAAM;gBAAS,MAAM;gBAA8B,MAAM,mNAAA,CAAA,eAAY;YAAC;YACxE;gBAAE,MAAM;gBAAQ,MAAM;gBAA0B,MAAM,yNAAA,CAAA,kBAAe;YAAC;SACvE;IACH;IACA;QACE,MAAM;QACN,MAAM,2NAAA,CAAA,mBAAgB;QACtB,UAAU;YACR;gBAAE,MAAM;gBAAQ,MAAM;gBAAwB,MAAM,2NAAA,CAAA,mBAAgB;YAAC;YACrE;gBAAE,MAAM;gBAAQ,MAAM;gBAA2B,MAAM,+MAAA,CAAA,aAAU;YAAC;SACnE;IACH;CACD;AAEc,SAAS,YAAY,KAAqC;QAArC,EAAE,QAAQ,EAAE,KAAK,EAAoB,GAArC;;IAClC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,qBAAqB;YACzB,SAAS;gBAAE,iBAAiB,AAAC,UAAe,OAAN;YAAQ;QAChD,GACC,IAAI,CAAC,CAAA,WAAY,SAAS,IAAI,IAC9B,IAAI,CAAC,CAAA;YACJ,IAAI,OAAO,OAAO,EAAE;gBAClB,mBAAmB;gBACnB,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO;IACvE;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BAEb,0JAAC;gBAAI,WAAW,AAAC,qCAAgE,OAA5B,cAAc,KAAK;;kCACtE,0JAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,0JAAC;gCACC,YAAY;gCACZ,eAAe;gCACf,gBAAgB;gCAChB,eAAe;;;;;;;;;;;;;;;;;;0BAMrB,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBACC,YAAY;wBACZ,eAAe;wBACf,gBAAgB;wBAChB,eAAe;;;;;;;;;;;;;;;;0BAMrB,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,0JAAC,6MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAGvB,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACZ,uBACC,0JAAC;4CAAG,WAAU;sDAAwC;;;;;;;;;;;kDAI1D,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,6IAAA,CAAA,UAAkB;;;;;0DAGnB,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAA6B,CAAA,qBAAA,+BAAA,SAAU,QAAQ,MAAI,qBAAA,+BAAA,SAAU,QAAQ;;;;;;0EACpF,0JAAC;gEAAI,WAAU;0EAAiB,qBAAA,+BAAA,SAAU,IAAI;;;;;;;;;;;;kEAEhD,0JAAC;wDACC,SAAS;wDACT,WAAU;wDACV,OAAM;kEAEN,cAAA,0JAAC,6OAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,0JAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GA1JwB;;QAMP,0HAAA,CAAA,YAAS;;;KANF;AA4JxB,SAAS,eAAe,KAUvB;QAVuB,EACtB,UAAU,EACV,aAAa,EACb,cAAc,EACd,aAAa,EAMd,GAVuB;IAWtB,qBACE,0JAAC;QAAI,WAAU;kBAEb,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;sCAEzC,0JAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;8BAIxD,0JAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC;sCACE,KAAK,QAAQ,iBACZ,0JAAC;;kDACC,0JAAC;wCACC,SAAS,IAAM,eAAe,KAAK,IAAI;wCACvC,WAAW,AAAC,8IAIX,OAHC,cAAc,QAAQ,CAAC,KAAK,IAAI,IAC5B,8BACA;;0DAGN,0JAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;0DACV,0JAAC;gDACC,WAAW,AAAC,4DAEX,OADC,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,4BAA4B;gDAElE,SAAQ;gDACR,MAAK;0DAEL,cAAA,0JAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;oCAG5J,cAAc,QAAQ,CAAC,KAAK,IAAI,mBAC/B,0JAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,WAAW,AAAC,iFAIX,OAHC,cAAc,QAAQ,IAAI,IACtB,8BACA;;kEAGN,0JAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;oDACvB,QAAQ,IAAI;;+CATR,QAAQ,IAAI;;;;;;;;;;;;;;;qDAgB3B,0JAAC,wHAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,AAAC,oEAIX,OAHC,cAAc,KAAK,IAAI,IACnB,8BACA;;kDAGN,0JAAC,KAAK,IAAI;wCAAC,WAAW,AAAC,gBAEtB,OADC,cAAc,KAAK,IAAI,IAAI,kBAAkB;;;;;;oCAE9C,KAAK,IAAI;;;;;;;2BAtDN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AA+D/B;MAxFS", "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/ServiceEditor.tsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\nimport { \n  XMarkIcon,\n  PhotoIcon,\n  DocumentTextIcon,\n  TagIcon,\n  EyeIcon,\n  CheckIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline';\n\ninterface ServiceContent {\n  id: string;\n  name: string;\n  slug: string;\n  title: string;\n  description: string;\n  content?: string;\n  features: string[];\n  status: 'active' | 'inactive' | 'draft';\n  sortOrder: number;\n  seoTitle?: string;\n  seoDescription?: string;\n  seoKeywords?: string;\n  images: string[];\n  tags: string[];\n  createdAt: string;\n  updatedAt: string;\n  version: number;\n}\n\ninterface ServiceEditorProps {\n  service: ServiceContent | null;\n  onSave: (data: Partial<ServiceContent>) => void;\n  onCancel: () => void;\n}\n\nexport default function ServiceEditor({ service, onSave, onCancel }: ServiceEditorProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    slug: '',\n    title: '',\n    description: '',\n    content: '',\n    features: [] as string[],\n    status: 'draft' as 'active' | 'inactive' | 'draft',\n    seoTitle: '',\n    seoDescription: '',\n    seoKeywords: '',\n    images: [] as string[],\n    tags: [] as string[]\n  });\n  \n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeTab, setActiveTab] = useState<'basic' | 'content' | 'seo' | 'media'>('basic');\n  const [newFeature, setNewFeature] = useState('');\n  const [newTag, setNewTag] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Initialize form data\n  useEffect(() => {\n    if (service) {\n      setFormData({\n        name: service.name || '',\n        slug: service.slug || '',\n        title: service.title || '',\n        description: service.description || '',\n        content: service.content || '',\n        features: service.features || [],\n        status: service.status || 'draft',\n        seoTitle: service.seoTitle || '',\n        seoDescription: service.seoDescription || '',\n        seoKeywords: service.seoKeywords || '',\n        images: service.images || [],\n        tags: service.tags || []\n      });\n    } else {\n      // Reset for new service\n      setFormData({\n        name: '',\n        slug: '',\n        title: '',\n        description: '',\n        content: '',\n        features: [],\n        status: 'draft',\n        seoTitle: '',\n        seoDescription: '',\n        seoKeywords: '',\n        images: [],\n        tags: []\n      });\n    }\n  }, [service]);\n\n  // Auto-generate slug from name\n  useEffect(() => {\n    if (!service && formData.name) {\n      const slug = formData.name\n        .toLowerCase()\n        .replace(/[^a-z0-9\\u4e00-\\u9fa5]/g, '_')\n        .replace(/_+/g, '_')\n        .replace(/^_|_$/g, '');\n      setFormData(prev => ({ ...prev, slug }));\n    }\n  }, [formData.name, service]);\n\n  const handleInputChange = (field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  const addFeature = () => {\n    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        features: [...prev.features, newFeature.trim()]\n      }));\n      setNewFeature('');\n    }\n  };\n\n  const removeFeature = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      features: prev.features.filter((_, i) => i !== index)\n    }));\n  };\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }));\n      setNewTag('');\n    }\n  };\n\n  const removeTag = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter((_, i) => i !== index)\n    }));\n  };\n\n  const handleImageUpload = async (files: FileList) => {\n    if (!files.length) return;\n\n    setIsUploading(true);\n    const formDataUpload = new FormData();\n    \n    Array.from(files).forEach(file => {\n      formDataUpload.append('file', file);\n    });\n\n    try {\n      const response = await fetch('/api/admin/services/upload', {\n        method: 'POST',\n        headers: {\n          'Authorization': 'Bearer mock-token'\n        },\n        body: formDataUpload\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        const newImages = result.files.map((file: any) => file.url);\n        setFormData(prev => ({\n          ...prev,\n          images: [...prev.images, ...newImages]\n        }));\n      } else {\n        const error = await response.json();\n        alert(`图片上传失败: ${error.error}`);\n      }\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('图片上传失败，请稍后重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const removeImage = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter((_, i) => i !== index)\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = '服务名称不能为空';\n    }\n\n    if (!formData.slug.trim()) {\n      newErrors.slug = 'URL标识不能为空';\n    } else if (!/^[a-z0-9_-]+$/.test(formData.slug)) {\n      newErrors.slug = 'URL标识只能包含小写字母、数字、下划线和连字符';\n    }\n\n    if (!formData.title.trim()) {\n      newErrors.title = '页面标题不能为空';\n    }\n\n    if (!formData.description.trim()) {\n      newErrors.description = '服务描述不能为空';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    try {\n      await onSave(formData);\n    } catch (error) {\n      console.error('Save error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const tabs = [\n    { id: 'basic', name: '基本信息', icon: DocumentTextIcon },\n    { id: 'content', name: '内容编辑', icon: DocumentTextIcon },\n    { id: 'seo', name: 'SEO设置', icon: TagIcon },\n    { id: 'media', name: '媒体管理', icon: PhotoIcon }\n  ];\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-4 mx-auto p-0 border w-11/12 max-w-4xl shadow-lg rounded-lg bg-white mb-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            {service ? '编辑服务' : '添加服务'}\n          </h3>\n          <button\n            onClick={onCancel}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4 mr-2\" />\n                  {tab.name}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"p-6 max-h-96 overflow-y-auto\">\n            {/* Basic Info Tab */}\n            {activeTab === 'basic' && (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      服务名称 *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.name}\n                      onChange={(e) => handleInputChange('name', e.target.value)}\n                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                        errors.name ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请输入服务名称\"\n                    />\n                    {errors.name && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      URL标识 *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.slug}\n                      onChange={(e) => handleInputChange('slug', e.target.value)}\n                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                        errors.slug ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"service_name\"\n                    />\n                    {errors.slug && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.slug}</p>\n                    )}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    页面标题 *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.title}\n                    onChange={(e) => handleInputChange('title', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      errors.title ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                    placeholder=\"请输入页面标题\"\n                  />\n                  {errors.title && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    服务描述 *\n                  </label>\n                  <textarea\n                    value={formData.description}\n                    onChange={(e) => handleInputChange('description', e.target.value)}\n                    rows={3}\n                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      errors.description ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                    placeholder=\"请输入服务描述\"\n                  />\n                  {errors.description && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    状态\n                  </label>\n                  <select\n                    value={formData.status}\n                    onChange={(e) => handleInputChange('status', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"draft\">草稿</option>\n                    <option value=\"active\">已启用</option>\n                    <option value=\"inactive\">已禁用</option>\n                  </select>\n                </div>\n\n                {/* Features */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    特色功能\n                  </label>\n                  <div className=\"flex space-x-2 mb-2\">\n                    <input\n                      type=\"text\"\n                      value={newFeature}\n                      onChange={(e) => setNewFeature(e.target.value)}\n                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"添加特色功能\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={addFeature}\n                      className=\"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      添加\n                    </button>\n                  </div>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {formData.features.map((feature, index) => (\n                      <span\n                        key={index}\n                        className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\"\n                      >\n                        {feature}\n                        <button\n                          type=\"button\"\n                          onClick={() => removeFeature(index)}\n                          className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                        >\n                          <XMarkIcon className=\"h-3 w-3\" />\n                        </button>\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Tags */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    标签\n                  </label>\n                  <div className=\"flex space-x-2 mb-2\">\n                    <input\n                      type=\"text\"\n                      value={newTag}\n                      onChange={(e) => setNewTag(e.target.value)}\n                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"添加标签\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={addTag}\n                      className=\"px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500\"\n                    >\n                      添加\n                    </button>\n                  </div>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {formData.tags.map((tag, index) => (\n                      <span\n                        key={index}\n                        className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800\"\n                      >\n                        {tag}\n                        <button\n                          type=\"button\"\n                          onClick={() => removeTag(index)}\n                          className=\"ml-2 text-gray-600 hover:text-gray-800\"\n                        >\n                          <XMarkIcon className=\"h-3 w-3\" />\n                        </button>\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Content Tab */}\n            {activeTab === 'content' && (\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    详细内容\n                  </label>\n                  <textarea\n                    value={formData.content}\n                    onChange={(e) => handleInputChange('content', e.target.value)}\n                    rows={12}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm\"\n                    placeholder=\"支持HTML格式，例如：&#10;&#10;<h2>服务详情</h2>&#10;<p>这里是服务的详细描述...</p>&#10;<ul>&#10;  <li>特性1</li>&#10;  <li>特性2</li>&#10;</ul>\"\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    支持HTML格式。可以使用标签如 &lt;h2&gt;、&lt;p&gt;、&lt;ul&gt;、&lt;li&gt;、&lt;strong&gt; 等\n                  </p>\n                </div>\n\n                {/* Content Preview */}\n                {formData.content && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      内容预览\n                    </label>\n                    <div\n                      className=\"w-full p-4 border border-gray-300 rounded-md bg-gray-50 prose prose-sm max-w-none\"\n                      dangerouslySetInnerHTML={{ __html: formData.content }}\n                    />\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* SEO Tab */}\n            {activeTab === 'seo' && (\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    SEO标题\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.seoTitle}\n                    onChange={(e) => handleInputChange('seoTitle', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"留空则使用页面标题\"\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    建议长度：50-60个字符 (当前: {formData.seoTitle.length}个字符)\n                  </p>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    SEO描述\n                  </label>\n                  <textarea\n                    value={formData.seoDescription}\n                    onChange={(e) => handleInputChange('seoDescription', e.target.value)}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"留空则使用服务描述\"\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    建议长度：150-160个字符 (当前: {formData.seoDescription.length}个字符)\n                  </p>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    SEO关键词\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.seoKeywords}\n                    onChange={(e) => handleInputChange('seoKeywords', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"用逗号分隔关键词，例如：VPN,网络安全,加密\"\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    用逗号分隔多个关键词，建议3-5个关键词\n                  </p>\n                </div>\n\n                {/* SEO Preview */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    搜索结果预览\n                  </label>\n                  <div className=\"p-4 border border-gray-300 rounded-md bg-gray-50\">\n                    <div className=\"text-blue-600 text-lg font-medium hover:underline cursor-pointer\">\n                      {formData.seoTitle || formData.title || '服务标题'}\n                    </div>\n                    <div className=\"text-green-600 text-sm\">\n                      https://vpl.com/services/{formData.slug || 'service-name'}\n                    </div>\n                    <div className=\"text-gray-600 text-sm mt-1\">\n                      {formData.seoDescription || formData.description || '服务描述...'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Media Tab */}\n            {activeTab === 'media' && (\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    服务图片\n                  </label>\n\n                  {/* Upload Area */}\n                  <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors\">\n                    <input\n                      ref={fileInputRef}\n                      type=\"file\"\n                      multiple\n                      accept=\"image/*\"\n                      onChange={(e) => e.target.files && handleImageUpload(e.target.files)}\n                      className=\"hidden\"\n                    />\n                    <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div className=\"mt-2\">\n                      <button\n                        type=\"button\"\n                        onClick={() => fileInputRef.current?.click()}\n                        disabled={isUploading}\n                        className=\"text-blue-600 hover:text-blue-500 font-medium disabled:opacity-50\"\n                      >\n                        {isUploading ? '上传中...' : '点击上传图片'}\n                      </button>\n                      <p className=\"text-gray-500 text-sm mt-1\">\n                        或拖拽图片到此处\n                      </p>\n                    </div>\n                    <p className=\"text-xs text-gray-400 mt-2\">\n                      支持 JPG、PNG、WebP、GIF 格式，最大 5MB\n                    </p>\n                  </div>\n\n                  {/* Image Gallery */}\n                  {formData.images.length > 0 && (\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4 mt-4\">\n                      {formData.images.map((image, index) => (\n                        <div key={index} className=\"relative group\">\n                          <img\n                            src={image}\n                            alt={`服务图片 ${index + 1}`}\n                            className=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={() => removeImage(index)}\n                            className=\"absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-700\"\n                          >\n                            <XMarkIcon className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          <div className=\"flex justify-between items-center px-6 py-4 border-t border-gray-200 bg-gray-50\">\n            <div className=\"flex items-center space-x-4\">\n              {service && (\n                <button\n                  type=\"button\"\n                  onClick={() => window.open(`/services/${formData.slug}`, '_blank')}\n                  className=\"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                >\n                  <EyeIcon className=\"h-4 w-4 mr-2\" />\n                  预览\n                </button>\n              )}\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <button\n                type=\"button\"\n                onClick={onCancel}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                取消\n              </button>\n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    保存中...\n                  </>\n                ) : (\n                  <>\n                    <CheckIcon className=\"h-4 w-4 mr-2\" />\n                    保存\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAoCe,SAAS,cAAc,KAAiD;QAAjD,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAsB,GAAjD;;IACpC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU,EAAE;QACZ,QAAQ;QACR,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,QAAQ,EAAE;QACV,MAAM,EAAE;IACV;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAyC;IAClF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,uBAAuB;IACvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS;gBACX,YAAY;oBACV,MAAM,QAAQ,IAAI,IAAI;oBACtB,MAAM,QAAQ,IAAI,IAAI;oBACtB,OAAO,QAAQ,KAAK,IAAI;oBACxB,aAAa,QAAQ,WAAW,IAAI;oBACpC,SAAS,QAAQ,OAAO,IAAI;oBAC5B,UAAU,QAAQ,QAAQ,IAAI,EAAE;oBAChC,QAAQ,QAAQ,MAAM,IAAI;oBAC1B,UAAU,QAAQ,QAAQ,IAAI;oBAC9B,gBAAgB,QAAQ,cAAc,IAAI;oBAC1C,aAAa,QAAQ,WAAW,IAAI;oBACpC,QAAQ,QAAQ,MAAM,IAAI,EAAE;oBAC5B,MAAM,QAAQ,IAAI,IAAI,EAAE;gBAC1B;YACF,OAAO;gBACL,wBAAwB;gBACxB,YAAY;oBACV,MAAM;oBACN,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,SAAS;oBACT,UAAU,EAAE;oBACZ,QAAQ;oBACR,UAAU;oBACV,gBAAgB;oBAChB,aAAa;oBACb,QAAQ,EAAE;oBACV,MAAM,EAAE;gBACV;YACF;QACF;kCAAG;QAAC;KAAQ;IAEZ,+BAA+B;IAC/B,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,SAAS,IAAI,EAAE;gBAC7B,MAAM,OAAO,SAAS,IAAI,CACvB,WAAW,GACX,OAAO,CAAC,2BAA2B,KACnC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,UAAU;gBACrB;+CAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE;wBAAK,CAAC;;YACxC;QACF;kCAAG;QAAC,SAAS,IAAI;QAAE;KAAQ;IAE3B,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,QAAQ,CAAC,WAAW,IAAI,KAAK;YACvE,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;2BAAI,KAAK,QAAQ;wBAAE,WAAW,IAAI;qBAAG;gBACjD,CAAC;YACD,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACjD,CAAC;IACH;IAEA,MAAM,SAAS;QACb,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK;YAC3D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,OAAO,IAAI;qBAAG;gBACrC,CAAC;YACD,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACzC,CAAC;IACH;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,MAAM,MAAM,EAAE;QAEnB,eAAe;QACf,MAAM,iBAAiB,IAAI;QAE3B,MAAM,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;YACxB,eAAe,MAAM,CAAC,QAAQ;QAChC;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,iBAAiB;gBACnB;gBACA,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,YAAY,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,KAAK,GAAG;gBAC1D,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,QAAQ;+BAAI,KAAK,MAAM;+BAAK;yBAAU;oBACxC,CAAC;YACH,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,WAAsB,OAAZ,MAAM,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,CAAC,gBAAgB,IAAI,CAAC,SAAS,IAAI,GAAG;YAC/C,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,OAAO;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,MAAM;YAAQ,MAAM,2NAAA,CAAA,mBAAgB;QAAC;QACpD;YAAE,IAAI;YAAW,MAAM;YAAQ,MAAM,2NAAA,CAAA,mBAAgB;QAAC;QACtD;YAAE,IAAI;YAAO,MAAM;YAAS,MAAM,yMAAA,CAAA,UAAO;QAAC;QAC1C;YAAE,IAAI;YAAS,MAAM;YAAQ,MAAM,6MAAA,CAAA,YAAS;QAAC;KAC9C;IAED,qBACE,0JAAC;QAAI,WAAU;kBACb,cAAA,0JAAC;YAAI,WAAU;;8BAEb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAG,WAAU;sCACX,UAAU,SAAS;;;;;;sCAEtB,0JAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,0JAAC,6MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKzB,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;wBAAsB,cAAW;kCAC7C,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,OAAO,IAAI,IAAI;4BACrB,qBACE,0JAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,AAAC,8DAIX,OAHC,cAAc,IAAI,EAAE,GAChB,kCACA;;kDAGN,0JAAC;wCAAK,WAAU;;;;;;oCACf,IAAI,IAAI;;+BATJ,IAAI,EAAE;;;;;wBAYjB;;;;;;;;;;;8BAIJ,0JAAC;oBAAK,UAAU;;sCACd,0JAAC;4BAAI,WAAU;;gCAEZ,cAAc,yBACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;;sEACC,0JAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,0JAAC;4DACC,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACzD,WAAW,AAAC,oGAEX,OADC,OAAO,IAAI,GAAG,mBAAmB;4DAEnC,aAAY;;;;;;wDAEb,OAAO,IAAI,kBACV,0JAAC;4DAAE,WAAU;sEAA6B,OAAO,IAAI;;;;;;;;;;;;8DAIzD,0JAAC;;sEACC,0JAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,0JAAC;4DACC,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACzD,WAAW,AAAC,oGAEX,OADC,OAAO,IAAI,GAAG,mBAAmB;4DAEnC,aAAY;;;;;;wDAEb,OAAO,IAAI,kBACV,0JAAC;4DAAE,WAAU;sEAA6B,OAAO,IAAI;;;;;;;;;;;;;;;;;;sDAK3D,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAW,AAAC,oGAEX,OADC,OAAO,KAAK,GAAG,mBAAmB;oDAEpC,aAAY;;;;;;gDAEb,OAAO,KAAK,kBACX,0JAAC;oDAAE,WAAU;8DAA6B,OAAO,KAAK;;;;;;;;;;;;sDAI1D,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,MAAM;oDACN,WAAW,AAAC,oGAEX,OADC,OAAO,WAAW,GAAG,mBAAmB;oDAE1C,aAAY;;;;;;gDAEb,OAAO,WAAW,kBACjB,0JAAC;oDAAE,WAAU;8DAA6B,OAAO,WAAW;;;;;;;;;;;;sDAIhE,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,OAAO,SAAS,MAAM;oDACtB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;oDAC3D,WAAU;;sEAEV,0JAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,0JAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,0JAAC;4DAAO,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAK7B,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,YAAY;4DACzE,WAAU;4DACV,aAAY;;;;;;sEAEd,0JAAC;4DACC,MAAK;4DACL,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;8DAIH,0JAAC;oDAAI,WAAU;8DACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,0JAAC;4DAEC,WAAU;;gEAET;8EACD,0JAAC;oEACC,MAAK;oEACL,SAAS,IAAM,cAAc;oEAC7B,WAAU;8EAEV,cAAA,0JAAC,6MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;2DATlB;;;;;;;;;;;;;;;;sDAiBb,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4DACzC,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,QAAQ;4DACrE,WAAU;4DACV,aAAY;;;;;;sEAEd,0JAAC;4DACC,MAAK;4DACL,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;8DAIH,0JAAC;oDAAI,WAAU;8DACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACvB,0JAAC;4DAEC,WAAU;;gEAET;8EACD,0JAAC;oEACC,MAAK;oEACL,SAAS,IAAM,UAAU;oEACzB,WAAU;8EAEV,cAAA,0JAAC,6MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;2DATlB;;;;;;;;;;;;;;;;;;;;;;gCAmBhB,cAAc,2BACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;8DAEd,0JAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;wCAM3C,SAAS,OAAO,kBACf,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,WAAU;oDACV,yBAAyB;wDAAE,QAAQ,SAAS,OAAO;oDAAC;;;;;;;;;;;;;;;;;;gCAQ7D,cAAc,uBACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,WAAU;oDACV,aAAY;;;;;;8DAEd,0JAAC;oDAAE,WAAU;;wDAA6B;wDACpB,SAAS,QAAQ,CAAC,MAAM;wDAAC;;;;;;;;;;;;;sDAIjD,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,OAAO,SAAS,cAAc;oDAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACnE,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;8DAEd,0JAAC;oDAAE,WAAU;;wDAA6B;wDAClB,SAAS,cAAc,CAAC,MAAM;wDAAC;;;;;;;;;;;;;sDAIzD,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDACC,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;oDACV,aAAY;;;;;;8DAEd,0JAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAM5C,0JAAC;;8DACC,0JAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;sEACZ,SAAS,QAAQ,IAAI,SAAS,KAAK,IAAI;;;;;;sEAE1C,0JAAC;4DAAI,WAAU;;gEAAyB;gEACZ,SAAS,IAAI,IAAI;;;;;;;sEAE7C,0JAAC;4DAAI,WAAU;sEACZ,SAAS,cAAc,IAAI,SAAS,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;gCAQ7D,cAAc,yBACb,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;;0DACC,0JAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAKhE,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDACC,KAAK;wDACL,MAAK;wDACL,QAAQ;wDACR,QAAO;wDACP,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACnE,WAAU;;;;;;kEAEZ,0JAAC,6MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEACC,MAAK;gEACL,SAAS;wEAAM;4EAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;;gEAC1C,UAAU;gEACV,WAAU;0EAET,cAAc,WAAW;;;;;;0EAE5B,0JAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAI5C,0JAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;4CAM3C,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,0JAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,0JAAC;wDAAgB,WAAU;;0EACzB,0JAAC;gEACC,KAAK;gEACL,KAAK,AAAC,QAAiB,OAAV,QAAQ;gEACrB,WAAU;;;;;;0EAEZ,0JAAC;gEACC,MAAK;gEACL,SAAS,IAAM,YAAY;gEAC3B,WAAU;0EAEV,cAAA,0JAAC,6MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;uDAXf;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAuBxB,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;8CACZ,yBACC,0JAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,aAA0B,OAAd,SAAS,IAAI,GAAI;wCACzD,WAAU;;0DAEV,0JAAC,yMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAM1C,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,0JAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,0JAAC;wDAAI,WAAU;;;;;;oDAAuE;;6EAIxF;;kEACE,0JAAC,6MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;GA9nBwB;KAAA", "debugId": null}}, {"offset": {"line": 2789, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/admin/DragDropList.tsx"], "sourcesContent": ["import { useState, useRef, DragEvent } from 'react';\n\ninterface DragDropListProps<T> {\n  items: T[];\n  onReorder: (items: T[]) => void;\n  renderItem: (item: T, index: number, isDragging: boolean) => React.ReactNode;\n  keyExtractor: (item: T) => string;\n  className?: string;\n}\n\nexport default function DragDropList<T>({\n  items,\n  onReorder,\n  renderItem,\n  keyExtractor,\n  className = ''\n}: DragDropListProps<T>) {\n  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);\n  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);\n  const draggedItem = useRef<T | null>(null);\n\n  const handleDragStart = (e: DragEvent, index: number) => {\n    setDraggedIndex(index);\n    draggedItem.current = items[index];\n    e.dataTransfer.effectAllowed = 'move';\n    e.dataTransfer.setData('text/html', '');\n  };\n\n  const handleDragOver = (e: DragEvent, index: number) => {\n    e.preventDefault();\n    e.dataTransfer.dropEffect = 'move';\n    setDragOverIndex(index);\n  };\n\n  const handleDragLeave = () => {\n    setDragOverIndex(null);\n  };\n\n  const handleDrop = (e: DragEvent, dropIndex: number) => {\n    e.preventDefault();\n    \n    if (draggedIndex === null || draggedItem.current === null) return;\n    \n    if (draggedIndex === dropIndex) {\n      setDraggedIndex(null);\n      setDragOverIndex(null);\n      draggedItem.current = null;\n      return;\n    }\n\n    const newItems = [...items];\n    newItems.splice(draggedIndex, 1);\n    newItems.splice(dropIndex, 0, draggedItem.current);\n\n    onReorder(newItems);\n    \n    setDraggedIndex(null);\n    setDragOverIndex(null);\n    draggedItem.current = null;\n  };\n\n  const handleDragEnd = () => {\n    setDraggedIndex(null);\n    setDragOverIndex(null);\n    draggedItem.current = null;\n  };\n\n  return (\n    <div className={className}>\n      {items.map((item, index) => (\n        <div\n          key={keyExtractor(item)}\n          draggable\n          onDragStart={(e) => handleDragStart(e, index)}\n          onDragOver={(e) => handleDragOver(e, index)}\n          onDragLeave={handleDragLeave}\n          onDrop={(e) => handleDrop(e, index)}\n          onDragEnd={handleDragEnd}\n          className={`transition-all duration-200 ${\n            draggedIndex === index ? 'opacity-50 scale-95' : ''\n          } ${\n            dragOverIndex === index && draggedIndex !== index\n              ? 'border-t-2 border-blue-500'\n              : ''\n          }`}\n        >\n          {renderItem(item, index, draggedIndex === index)}\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;AAUe,SAAS,aAAgB,KAMjB;QANiB,EACtC,KAAK,EACL,SAAS,EACT,UAAU,EACV,YAAY,EACZ,YAAY,EAAE,EACO,GANiB;;IAOtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAY;IAErC,MAAM,kBAAkB,CAAC,GAAc;QACrC,gBAAgB;QAChB,YAAY,OAAO,GAAG,KAAK,CAAC,MAAM;QAClC,EAAE,YAAY,CAAC,aAAa,GAAG;QAC/B,EAAE,YAAY,CAAC,OAAO,CAAC,aAAa;IACtC;IAEA,MAAM,iBAAiB,CAAC,GAAc;QACpC,EAAE,cAAc;QAChB,EAAE,YAAY,CAAC,UAAU,GAAG;QAC5B,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,iBAAiB;IACnB;IAEA,MAAM,aAAa,CAAC,GAAc;QAChC,EAAE,cAAc;QAEhB,IAAI,iBAAiB,QAAQ,YAAY,OAAO,KAAK,MAAM;QAE3D,IAAI,iBAAiB,WAAW;YAC9B,gBAAgB;YAChB,iBAAiB;YACjB,YAAY,OAAO,GAAG;YACtB;QACF;QAEA,MAAM,WAAW;eAAI;SAAM;QAC3B,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,WAAW,GAAG,YAAY,OAAO;QAEjD,UAAU;QAEV,gBAAgB;QAChB,iBAAiB;QACjB,YAAY,OAAO,GAAG;IACxB;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY,OAAO,GAAG;IACxB;IAEA,qBACE,0JAAC;QAAI,WAAW;kBACb,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,0JAAC;gBAEC,SAAS;gBACT,aAAa,CAAC,IAAM,gBAAgB,GAAG;gBACvC,YAAY,CAAC,IAAM,eAAe,GAAG;gBACrC,aAAa;gBACb,QAAQ,CAAC,IAAM,WAAW,GAAG;gBAC7B,WAAW;gBACX,WAAW,AAAC,+BAGV,OAFA,iBAAiB,QAAQ,wBAAwB,IAClD,KAIA,OAHC,kBAAkB,SAAS,iBAAiB,QACxC,+BACA;0BAGL,WAAW,MAAM,OAAO,iBAAiB;eAfrC,aAAa;;;;;;;;;;AAoB5B;GAjFwB;KAAA", "debugId": null}}, {"offset": {"line": 2876, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/content/services.tsx"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport Head from 'next/head';\nimport AdminLayout from '../../../components/admin/AdminLayout';\nimport {\n  CogIcon,\n  PencilIcon,\n  EyeIcon,\n  PlusIcon,\n  TrashIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  ArrowsUpDownIcon,\n  CheckIcon,\n  XMarkIcon,\n  PhotoIcon,\n  DocumentDuplicateIcon,\n  ClockIcon,\n  TagIcon,\n  Bars3Icon\n} from '@heroicons/react/24/outline';\nimport ServiceEditor from '../../../components/admin/ServiceEditor';\nimport DragDropList from '../../../components/admin/DragDropList';\n\ninterface ServiceContent {\n  id: string;\n  name: string;\n  slug: string;\n  title: string;\n  description: string;\n  content?: string;\n  features: string[];\n  status: 'active' | 'inactive' | 'draft';\n  sortOrder: number;\n  seoTitle?: string;\n  seoDescription?: string;\n  seoKeywords?: string;\n  images: string[];\n  tags: string[];\n  createdAt: string;\n  updatedAt: string;\n  version: number;\n}\n\nexport default function ServicesContent() {\n  const [services, setServices] = useState<ServiceContent[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedService, setSelectedService] = useState<ServiceContent | null>(null);\n  const [isEditing, setIsEditing] = useState(false);\n  const [selectedIds, setSelectedIds] = useState<string[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n  const [sortBy, setSortBy] = useState<string>('sortOrder');\n  const [sortOrder, setSortOrder] = useState<string>('asc');\n  const [showBatchActions, setShowBatchActions] = useState(false);\n  const [isPerformingBatch, setIsPerformingBatch] = useState(false);\n\n  // Fetch services from API\n  const fetchServices = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/admin/services?search=${searchTerm}&status=${statusFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}`, {\n        headers: {\n          'Authorization': 'Bearer mock-token' // In production, use real JWT token\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setServices(data.services);\n      } else {\n        console.error('Failed to fetch services');\n      }\n    } catch (error) {\n      console.error('Error fetching services:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [searchTerm, statusFilter, sortBy, sortOrder]);\n\n  useEffect(() => {\n    fetchServices();\n  }, [fetchServices]);\n\n  // CRUD Operations\n  const handleEdit = (service: ServiceContent) => {\n    setSelectedService(service);\n    setIsEditing(true);\n  };\n\n  const handleCreate = () => {\n    setSelectedService(null);\n    setIsEditing(true);\n  };\n\n  const handleSave = async (serviceData: Partial<ServiceContent>) => {\n    try {\n      const isUpdate = selectedService?.id;\n      const url = isUpdate ? `/api/admin/services/${selectedService.id}` : '/api/admin/services';\n      const method = isUpdate ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': 'Bearer mock-token'\n        },\n        body: JSON.stringify(serviceData)\n      });\n\n      if (response.ok) {\n        await fetchServices(); // Refresh the list\n        setIsEditing(false);\n        setSelectedService(null);\n        alert(isUpdate ? '服务更新成功' : '服务创建成功');\n      } else {\n        const error = await response.json();\n        alert(`操作失败: ${error.error}`);\n      }\n    } catch (error) {\n      console.error('Save error:', error);\n      alert('操作失败，请稍后重试');\n    }\n  };\n\n  const handleDelete = async (serviceId: string) => {\n    if (!confirm('确定要删除这个服务吗？此操作不可撤销。')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/admin/services/${serviceId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': 'Bearer mock-token'\n        }\n      });\n\n      if (response.ok) {\n        await fetchServices();\n        alert('服务删除成功');\n      } else {\n        const error = await response.json();\n        alert(`删除失败: ${error.error}`);\n      }\n    } catch (error) {\n      console.error('Delete error:', error);\n      alert('删除失败，请稍后重试');\n    }\n  };\n\n  const toggleStatus = async (serviceId: string, newStatus: 'active' | 'inactive') => {\n    try {\n      const response = await fetch(`/api/admin/services/${serviceId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': 'Bearer mock-token'\n        },\n        body: JSON.stringify({ status: newStatus })\n      });\n\n      if (response.ok) {\n        await fetchServices();\n      } else {\n        const error = await response.json();\n        alert(`状态更新失败: ${error.error}`);\n      }\n    } catch (error) {\n      console.error('Status toggle error:', error);\n      alert('状态更新失败，请稍后重试');\n    }\n  };\n\n  // Batch Operations\n  const handleBatchAction = async (action: 'activate' | 'deactivate' | 'delete') => {\n    if (selectedIds.length === 0) {\n      alert('请先选择要操作的服务');\n      return;\n    }\n\n    const actionText = action === 'activate' ? '启用' : action === 'deactivate' ? '禁用' : '删除';\n    if (!confirm(`确定要${actionText}选中的 ${selectedIds.length} 个服务吗？`)) {\n      return;\n    }\n\n    try {\n      setIsPerformingBatch(true);\n      const response = await fetch('/api/admin/services/batch', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': 'Bearer mock-token'\n        },\n        body: JSON.stringify({\n          action,\n          ids: selectedIds\n        })\n      });\n\n      if (response.ok) {\n        await fetchServices();\n        setSelectedIds([]);\n        setShowBatchActions(false);\n        const result = await response.json();\n        alert(result.message);\n      } else {\n        const error = await response.json();\n        alert(`批量操作失败: ${error.error}`);\n      }\n    } catch (error) {\n      console.error('Batch operation error:', error);\n      alert('批量操作失败，请稍后重试');\n    } finally {\n      setIsPerformingBatch(false);\n    }\n  };\n\n  // Drag and Drop\n  const handleReorder = async (reorderedServices: ServiceContent[]) => {\n    // Update local state immediately for better UX\n    setServices(reorderedServices);\n\n    // Update sort orders\n    const orders: { [key: string]: number } = {};\n    reorderedServices.forEach((item, index) => {\n      orders[item.id] = index + 1;\n    });\n\n    try {\n      const response = await fetch('/api/admin/services/batch', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': 'Bearer mock-token'\n        },\n        body: JSON.stringify({\n          action: 'updateOrder',\n          ids: reorderedServices.map(item => item.id),\n          data: { orders }\n        })\n      });\n\n      if (!response.ok) {\n        // Revert on error\n        await fetchServices();\n        const error = await response.json();\n        alert(`排序更新失败: ${error.error}`);\n      }\n    } catch (error) {\n      console.error('Drag and drop error:', error);\n      await fetchServices(); // Revert on error\n      alert('排序更新失败，请稍后重试');\n    }\n  };\n\n  // Selection handlers\n  const handleSelectAll = () => {\n    if (selectedIds.length === services.length) {\n      setSelectedIds([]);\n    } else {\n      setSelectedIds(services.map(s => s.id));\n    }\n  };\n\n  const handleSelectService = (serviceId: string) => {\n    setSelectedIds(prev =>\n      prev.includes(serviceId)\n        ? prev.filter(id => id !== serviceId)\n        : [...prev, serviceId]\n    );\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <>\n      <Head>\n        <title>服务内容管理 - VPL后台管理系统</title>\n        <meta name=\"description\" content=\"管理网站服务内容\" />\n      </Head>\n      \n      <AdminLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n            <div className=\"flex items-center\">\n              <CogIcon className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">服务内容管理</h1>\n                <p className=\"text-gray-600\">管理网站上的服务内容和描述</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <button\n                onClick={handleCreate}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                添加服务\n              </button>\n            </div>\n          </div>\n\n          {/* Search and Filter Bar */}\n          <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <div className=\"relative\">\n                  <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索服务名称、描述或标签...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              {/* Status Filter */}\n              <div className=\"sm:w-48\">\n                <select\n                  value={statusFilter}\n                  onChange={(e) => setStatusFilter(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">所有状态</option>\n                  <option value=\"active\">已启用</option>\n                  <option value=\"inactive\">已禁用</option>\n                  <option value=\"draft\">草稿</option>\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"sm:w-48\">\n                <select\n                  value={`${sortBy}-${sortOrder}`}\n                  onChange={(e) => {\n                    const [field, order] = e.target.value.split('-');\n                    setSortBy(field);\n                    setSortOrder(order);\n                  }}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"sortOrder-asc\">排序 (升序)</option>\n                  <option value=\"sortOrder-desc\">排序 (降序)</option>\n                  <option value=\"name-asc\">名称 (A-Z)</option>\n                  <option value=\"name-desc\">名称 (Z-A)</option>\n                  <option value=\"updatedAt-desc\">最近更新</option>\n                  <option value=\"createdAt-desc\">最近创建</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Batch Actions Bar */}\n          {selectedIds.length > 0 && (\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <CheckIcon className=\"h-5 w-5 text-blue-600 mr-2\" />\n                  <span className=\"text-sm font-medium text-blue-900\">\n                    已选择 {selectedIds.length} 个服务\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => handleBatchAction('activate')}\n                    disabled={isPerformingBatch}\n                    className=\"px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50\"\n                  >\n                    批量启用\n                  </button>\n                  <button\n                    onClick={() => handleBatchAction('deactivate')}\n                    disabled={isPerformingBatch}\n                    className=\"px-3 py-1.5 text-xs font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:opacity-50\"\n                  >\n                    批量禁用\n                  </button>\n                  <button\n                    onClick={() => handleBatchAction('delete')}\n                    disabled={isPerformingBatch}\n                    className=\"px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50\"\n                  >\n                    批量删除\n                  </button>\n                  <button\n                    onClick={() => setSelectedIds([])}\n                    className=\"px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500\"\n                  >\n                    取消选择\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Services List with Drag and Drop */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            {/* List Header */}\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedIds.length === services.length && services.length > 0}\n                      onChange={handleSelectAll}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <span className=\"ml-2 text-sm text-gray-700\">全选</span>\n                  </label>\n                  <span className=\"text-sm text-gray-500\">\n                    共 {services.length} 个服务\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <ArrowsUpDownIcon className=\"h-4 w-4 text-gray-400\" />\n                  <span className=\"text-xs text-gray-500\">拖拽排序</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Draggable Services List */}\n            <DragDropList\n              items={services}\n              onReorder={handleReorder}\n              keyExtractor={(service) => service.id}\n              renderItem={(service, index, isDragging) => (\n                <div className={`border-b border-gray-200 last:border-b-0 ${\n                  isDragging ? 'bg-blue-50 shadow-lg' : 'bg-white'\n                }`}>\n                  <div className=\"px-6 py-4\">\n                    <div className=\"flex items-center space-x-4\">\n                      {/* Drag Handle */}\n                      <div className=\"cursor-move\">\n                        <Bars3Icon className=\"h-5 w-5 text-gray-400\" />\n                      </div>\n\n                      {/* Checkbox */}\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedIds.includes(service.id)}\n                        onChange={() => handleSelectService(service.id)}\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                      />\n\n                      {/* Service Info */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center space-x-3\">\n                            <div>\n                              <h3 className=\"text-sm font-semibold text-gray-900\">\n                                {service.name}\n                              </h3>\n                              <p className=\"text-xs text-gray-500\">\n                                /{service.slug}\n                              </p>\n                            </div>\n                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                              service.status === 'active'\n                                ? 'bg-green-100 text-green-800'\n                                : service.status === 'inactive'\n                                ? 'bg-red-100 text-red-800'\n                                : 'bg-yellow-100 text-yellow-800'\n                            }`}>\n                              {service.status === 'active' ? '已启用' : service.status === 'inactive' ? '已禁用' : '草稿'}\n                            </span>\n                          </div>\n\n                          {/* Actions */}\n                          <div className=\"flex items-center space-x-2\">\n                            <button\n                              onClick={() => handleEdit(service)}\n                              className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                            >\n                              <PencilIcon className=\"h-3 w-3 mr-1\" />\n                              编辑\n                            </button>\n                            <button\n                              onClick={() => window.open(`/services/${service.slug}`, '_blank')}\n                              className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500\"\n                            >\n                              <EyeIcon className=\"h-3 w-3 mr-1\" />\n                              预览\n                            </button>\n                            <button\n                              onClick={() => toggleStatus(service.id, service.status === 'active' ? 'inactive' : 'active')}\n                              className={`inline-flex items-center px-2 py-1 text-xs font-medium border rounded focus:outline-none focus:ring-2 ${\n                                service.status === 'active'\n                                  ? 'text-red-700 bg-red-100 border-red-300 hover:bg-red-200 focus:ring-red-500'\n                                  : 'text-green-700 bg-green-100 border-green-300 hover:bg-green-200 focus:ring-green-500'\n                              }`}\n                            >\n                              {service.status === 'active' ? '禁用' : '启用'}\n                            </button>\n                            <button\n                              onClick={() => handleDelete(service.id)}\n                              className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500\"\n                            >\n                              <TrashIcon className=\"h-3 w-3 mr-1\" />\n                              删除\n                            </button>\n                          </div>\n                        </div>\n\n                        <p className=\"mt-2 text-sm text-gray-600 line-clamp-2\">\n                          {service.description}\n                        </p>\n\n                        <div className=\"mt-2 flex items-center space-x-4 text-xs text-gray-500\">\n                          <span className=\"flex items-center\">\n                            <ClockIcon className=\"h-3 w-3 mr-1\" />\n                            {new Date(service.updatedAt).toLocaleDateString()}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <TagIcon className=\"h-3 w-3 mr-1\" />\n                            {service.features.length} 个特性\n                          </span>\n                          {service.images.length > 0 && (\n                            <span className=\"flex items-center\">\n                              <PhotoIcon className=\"h-3 w-3 mr-1\" />\n                              {service.images.length} 张图片\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            />\n          </div>\n\n          {/* Empty State */}\n          {services.length === 0 && !loading && (\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n              <div className=\"text-center py-12\">\n                <CogIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无服务</h3>\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  {searchTerm || statusFilter !== 'all'\n                    ? '没有找到匹配的服务，请尝试调整搜索条件'\n                    : '开始添加您的第一个服务内容'\n                  }\n                </p>\n                {!searchTerm && statusFilter === 'all' && (\n                  <div className=\"mt-6\">\n                    <button\n                      onClick={handleCreate}\n                      className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      添加服务\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Service Editor Modal */}\n        {isEditing && (\n          <ServiceEditor\n            service={selectedService}\n            onSave={handleSave}\n            onCancel={() => {\n              setIsEditing(false);\n              setSelectedService(null);\n            }}\n          />\n        )}\n      </AdminLayout>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD;sDAAE;YAChC,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM,AAAC,8BAAkD,OAArB,YAAW,YAAiC,OAAvB,cAAa,YAA8B,OAApB,QAAO,eAAuB,OAAV,YAAa;oBACtI,SAAS;wBACP,iBAAiB,oBAAoB,oCAAoC;oBAC3E;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,YAAY,KAAK,QAAQ;gBAC3B,OAAO;oBACL,QAAQ,KAAK,CAAC;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,WAAW;YACb;QACF;qDAAG;QAAC;QAAY;QAAc;QAAQ;KAAU;IAEhD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;KAAc;IAElB,kBAAkB;IAClB,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,4BAAA,sCAAA,gBAAiB,EAAE;YACpC,MAAM,MAAM,WAAW,AAAC,uBAAyC,OAAnB,gBAAgB,EAAE,IAAK;YACrE,MAAM,SAAS,WAAW,QAAQ;YAElC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB;gBACnB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,iBAAiB,mBAAmB;gBAC1C,aAAa;gBACb,mBAAmB;gBACnB,MAAM,WAAW,WAAW;YAC9B,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,SAAoB,OAAZ,MAAM,KAAK;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,wBAAwB;YACnC;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,uBAAgC,OAAV,YAAa;gBAC/D,QAAQ;gBACR,SAAS;oBACP,iBAAiB;gBACnB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM;YACR,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,SAAoB,OAAZ,MAAM,KAAK;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR;IACF;IAEA,MAAM,eAAe,OAAO,WAAmB;QAC7C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,uBAAgC,OAAV,YAAa;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB;gBACnB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAU;YAC3C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,WAAsB,OAAZ,MAAM,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,MAAM;YACN;QACF;QAEA,MAAM,aAAa,WAAW,aAAa,OAAO,WAAW,eAAe,OAAO;QACnF,IAAI,CAAC,QAAQ,AAAC,MAAsB,OAAjB,YAAW,QAAyB,OAAnB,YAAY,MAAM,EAAC,YAAU;YAC/D;QACF;QAEA,IAAI;YACF,qBAAqB;YACrB,MAAM,WAAW,MAAM,MAAM,6BAA6B;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB;gBACnB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,KAAK;gBACP;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,eAAe,EAAE;gBACjB,oBAAoB;gBACpB,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,OAAO,OAAO;YACtB,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,WAAsB,OAAZ,MAAM,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,gBAAgB;IAChB,MAAM,gBAAgB,OAAO;QAC3B,+CAA+C;QAC/C,YAAY;QAEZ,qBAAqB;QACrB,MAAM,SAAoC,CAAC;QAC3C,kBAAkB,OAAO,CAAC,CAAC,MAAM;YAC/B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,QAAQ;QAC5B;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,6BAA6B;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB;gBACnB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,KAAK,kBAAkB,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;oBAC1C,MAAM;wBAAE;oBAAO;gBACjB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,kBAAkB;gBAClB,MAAM;gBACN,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,WAAsB,OAAZ,MAAM,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,iBAAiB,kBAAkB;YACzC,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,IAAI,YAAY,MAAM,KAAK,SAAS,MAAM,EAAE;YAC1C,eAAe,EAAE;QACnB,OAAO;YACL,eAAe,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QACvC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,eAAe,CAAA,OACb,KAAK,QAAQ,CAAC,aACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,aACzB;mBAAI;gBAAM;aAAU;IAE5B;IAEA,IAAI,SAAS;QACX,qBACE,0JAAC,sIAAA,CAAA,UAAW;sBACV,cAAA,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAGnC,0JAAC,sIAAA,CAAA,UAAW;;kCACV,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,yMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,0JAAC;;kEACC,0JAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,0JAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAGjC,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,0JAAC,2MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAO3C,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC,iOAAA,CAAA,sBAAmB;wDAAC,WAAU;;;;;;kEAC/B,0JAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;;;;;;sDAMhB,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,0JAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,0JAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,0JAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,0JAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;sDAK1B,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDACC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAa,OAAV;gDACpB,UAAU,CAAC;oDACT,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;oDAC5C,UAAU;oDACV,aAAa;gDACf;gDACA,WAAU;;kEAEV,0JAAC;wDAAO,OAAM;kEAAgB;;;;;;kEAC9B,0JAAC;wDAAO,OAAM;kEAAiB;;;;;;kEAC/B,0JAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,0JAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,0JAAC;wDAAO,OAAM;kEAAiB;;;;;;kEAC/B,0JAAC;wDAAO,OAAM;kEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOtC,YAAY,MAAM,GAAG,mBACpB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,6MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,0JAAC;oDAAK,WAAU;;wDAAoC;wDAC7C,YAAY,MAAM;wDAAC;;;;;;;;;;;;;sDAG5B,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDACC,SAAS,IAAM,kBAAkB;oDACjC,UAAU;oDACV,WAAU;8DACX;;;;;;8DAGD,0JAAC;oDACC,SAAS,IAAM,kBAAkB;oDACjC,UAAU;oDACV,WAAU;8DACX;;;;;;8DAGD,0JAAC;oDACC,SAAS,IAAM,kBAAkB;oDACjC,UAAU;oDACV,WAAU;8DACX;;;;;;8DAGD,0JAAC;oDACC,SAAS,IAAM,eAAe,EAAE;oDAChC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAST,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAM,WAAU;;8EACf,0JAAC;oEACC,MAAK;oEACL,SAAS,YAAY,MAAM,KAAK,SAAS,MAAM,IAAI,SAAS,MAAM,GAAG;oEACrE,UAAU;oEACV,WAAU;;;;;;8EAEZ,0JAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;sEAE/C,0JAAC;4DAAK,WAAU;;gEAAwB;gEACnC,SAAS,MAAM;gEAAC;;;;;;;;;;;;;8DAGvB,0JAAC;oDAAI,WAAU;;sEACb,0JAAC,2NAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;sEAC5B,0JAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM9C,0JAAC,uIAAA,CAAA,UAAY;wCACX,OAAO;wCACP,WAAW;wCACX,cAAc,CAAC,UAAY,QAAQ,EAAE;wCACrC,YAAY,CAAC,SAAS,OAAO,2BAC3B,0JAAC;gDAAI,WAAW,AAAC,4CAEhB,OADC,aAAa,yBAAyB;0DAEtC,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EAEb,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC,6MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAIvB,0JAAC;gEACC,MAAK;gEACL,SAAS,YAAY,QAAQ,CAAC,QAAQ,EAAE;gEACxC,UAAU,IAAM,oBAAoB,QAAQ,EAAE;gEAC9C,WAAU;;;;;;0EAIZ,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;;0GACC,0JAAC;gGAAG,WAAU;0GACX,QAAQ,IAAI;;;;;;0GAEf,0JAAC;gGAAE,WAAU;;oGAAwB;oGACjC,QAAQ,IAAI;;;;;;;;;;;;;kGAGlB,0JAAC;wFAAK,WAAW,AAAC,uEAMjB,OALC,QAAQ,MAAM,KAAK,WACf,gCACA,QAAQ,MAAM,KAAK,aACnB,4BACA;kGAEH,QAAQ,MAAM,KAAK,WAAW,QAAQ,QAAQ,MAAM,KAAK,aAAa,QAAQ;;;;;;;;;;;;0FAKnF,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFACC,SAAS,IAAM,WAAW;wFAC1B,WAAU;;0GAEV,0JAAC,+MAAA,CAAA,aAAU;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGzC,0JAAC;wFACC,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,aAAyB,OAAb,QAAQ,IAAI,GAAI;wFACxD,WAAU;;0GAEV,0JAAC,yMAAA,CAAA,UAAO;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGtC,0JAAC;wFACC,SAAS,IAAM,aAAa,QAAQ,EAAE,EAAE,QAAQ,MAAM,KAAK,WAAW,aAAa;wFACnF,WAAW,AAAC,yGAIX,OAHC,QAAQ,MAAM,KAAK,WACf,+EACA;kGAGL,QAAQ,MAAM,KAAK,WAAW,OAAO;;;;;;kGAExC,0JAAC;wFACC,SAAS,IAAM,aAAa,QAAQ,EAAE;wFACtC,WAAU;;0GAEV,0JAAC,6MAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;;;;;;;;;;;;;kFAM5C,0JAAC;wEAAE,WAAU;kFACV,QAAQ,WAAW;;;;;;kFAGtB,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAK,WAAU;;kGACd,0JAAC,6MAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFACpB,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;0FAEjD,0JAAC;gFAAK,WAAU;;kGACd,0JAAC,yMAAA,CAAA,UAAO;wFAAC,WAAU;;;;;;oFAClB,QAAQ,QAAQ,CAAC,MAAM;oFAAC;;;;;;;4EAE1B,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,0JAAC;gFAAK,WAAU;;kGACd,0JAAC,6MAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFACpB,QAAQ,MAAM,CAAC,MAAM;oFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAa1C,SAAS,MAAM,KAAK,KAAK,CAAC,yBACzB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;;sDACb,0JAAC,yMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,0JAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,0JAAC;4CAAE,WAAU;sDACV,cAAc,iBAAiB,QAC5B,wBACA;;;;;;wCAGL,CAAC,cAAc,iBAAiB,uBAC/B,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,0JAAC,2MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWlD,2BACC,0JAAC,wIAAA,CAAA,UAAa;wBACZ,SAAS;wBACT,QAAQ;wBACR,UAAU;4BACR,aAAa;4BACb,mBAAmB;wBACrB;;;;;;;;;;;;;;AAMZ;GA/hBwB;KAAA", "debugId": null}}, {"offset": {"line": 3973, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/content/services\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}