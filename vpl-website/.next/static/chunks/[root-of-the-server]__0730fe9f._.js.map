{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/admin/analytics/index.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useRouter } from 'next/router';\nimport Head from 'next/head';\nimport { \n  ChartBarIcon, \n  ArrowDownTrayIcon,\n  CalendarIcon,\n  ClockIcon,\n  TrendingUpIcon,\n  UsersIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\n\ninterface AnalyticsData {\n  totalSubmissions: number;\n  todaySubmissions: number;\n  weeklySubmissions: number;\n  monthlySubmissions: number;\n  conversionRate: number;\n  averageResponseTime: number;\n  topServiceTypes: Array<{\n    type: string;\n    count: number;\n    percentage: number;\n  }>;\n  submissionTrends: Array<{\n    date: string;\n    count: number;\n  }>;\n  statusDistribution: Array<{\n    status: string;\n    count: number;\n    percentage: number;\n  }>;\n  geographicDistribution: Array<{\n    region: string;\n    count: number;\n    percentage: number;\n  }>;\n  timeDistribution: Array<{\n    hour: number;\n    count: number;\n  }>;\n}\n\nexport default function Analytics() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);\n  const [dateRange, setDateRange] = useState('30d');\n  const [isExporting, setIsExporting] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      fetchAnalytics();\n    }\n  }, [isAuthenticated, dateRange]);\n\n  const checkAuthentication = () => {\n    const token = localStorage.getItem('adminToken');\n    if (!token) {\n      router.push('/admin/login');\n      return;\n    }\n\n    fetch('/api/admin/verify', {\n      headers: { 'Authorization': `Bearer ${token}` }\n    })\n    .then(response => {\n      if (response.ok) {\n        setIsAuthenticated(true);\n      } else {\n        localStorage.removeItem('adminToken');\n        router.push('/admin/login');\n      }\n    })\n    .catch(() => {\n      localStorage.removeItem('adminToken');\n      router.push('/admin/login');\n    })\n    .finally(() => {\n      setIsLoading(false);\n    });\n  };\n\n  const fetchAnalytics = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`/api/admin/analytics/dashboard?dateRange=${dateRange}`, {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setAnalytics(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch analytics:', error);\n    }\n  };\n\n  const handleExport = async (format: 'csv' | 'excel') => {\n    setIsExporting(true);\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch('/api/admin/export/submissions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          format,\n          fields: ['id', 'companyName', 'contactPerson', 'phone', 'email', 'serviceType', 'status', 'submittedAt']\n        })\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `submissions_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      } else {\n        alert('导出失败');\n      }\n    } catch (error) {\n      alert('导出时发生错误');\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    router.push('/admin/login');\n  };\n\n  const getServiceTypeName = (type: string) => {\n    const names = {\n      foreign_trade_lines: '外贸专线',\n      ecommerce_lines: '跨境电商专线',\n      vpn_services: 'VPN服务',\n      custom_solution: '定制解决方案'\n    };\n    return names[type as keyof typeof names] || type;\n  };\n\n  const getStatusName = (status: string) => {\n    const names = {\n      pending: '待处理',\n      contacted: '已联系',\n      closed: '已关闭'\n    };\n    return names[status as keyof typeof names] || status;\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      contacted: 'bg-blue-100 text-blue-800',\n      closed: 'bg-green-100 text-green-800'\n    };\n    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>数据分析 - VPL管理后台</title>\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Head>\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3\">\n                  <span className=\"text-white font-bold text-lg\">VPL</span>\n                </div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">数据分析</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => router.push('/admin/dashboard')}\n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  返回仪表板\n                </button>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-700 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-1\" />\n                  退出登录\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {/* Controls */}\n          <div className=\"mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n            <div className=\"flex items-center space-x-4 mb-4 sm:mb-0\">\n              <select\n                value={dateRange}\n                onChange={(e) => setDateRange(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              >\n                <option value=\"7d\">最近7天</option>\n                <option value=\"30d\">最近30天</option>\n                <option value=\"90d\">最近90天</option>\n                <option value=\"1y\">最近1年</option>\n              </select>\n            </div>\n\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={() => handleExport('csv')}\n                disabled={isExporting}\n                className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n              >\n                <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                导出CSV\n              </button>\n              <button\n                onClick={() => handleExport('excel')}\n                disabled={isExporting}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n              >\n                <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                导出Excel\n              </button>\n            </div>\n          </div>\n\n          {analytics && (\n            <>\n              {/* Key Metrics */}\n              <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <ChartBarIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">总咨询数</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.totalSubmissions.toLocaleString()}</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <CalendarIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">今日咨询</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.todaySubmissions}</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <TrendingUpIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">转化率</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.conversionRate}%</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-5\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <ClockIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                      <div className=\"ml-5 w-0 flex-1\">\n                        <dl>\n                          <dt className=\"text-sm font-medium text-gray-500 truncate\">平均响应时间</dt>\n                          <dd className=\"text-lg font-medium text-gray-900\">{analytics.averageResponseTime}小时</dd>\n                        </dl>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Charts Grid */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n                {/* Service Types Distribution */}\n                <div className=\"bg-white shadow rounded-lg p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">服务类型分布</h3>\n                  <div className=\"space-y-3\">\n                    {analytics.topServiceTypes.map((service, index) => (\n                      <div key={service.type} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <div className={`w-3 h-3 rounded-full mr-3 ${\n                            index === 0 ? 'bg-blue-500' :\n                            index === 1 ? 'bg-green-500' :\n                            index === 2 ? 'bg-yellow-500' : 'bg-red-500'\n                          }`}></div>\n                          <span className=\"text-sm text-gray-700\">{getServiceTypeName(service.type)}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-sm font-medium text-gray-900\">{service.count}</span>\n                          <span className=\"text-sm text-gray-500\">({service.percentage}%)</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Status Distribution */}\n                <div className=\"bg-white shadow rounded-lg p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">处理状态分布</h3>\n                  <div className=\"space-y-3\">\n                    {analytics.statusDistribution.map((status) => (\n                      <div key={status.status} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status.status)}`}>\n                            {getStatusName(status.status)}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-sm font-medium text-gray-900\">{status.count}</span>\n                          <span className=\"text-sm text-gray-500\">({status.percentage}%)</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Geographic Distribution */}\n              <div className=\"bg-white shadow rounded-lg p-6 mb-8\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">地域分布</h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {analytics.geographicDistribution.map((region) => (\n                    <div key={region.region} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <span className=\"text-sm font-medium text-gray-900\">{region.region}</span>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-sm text-gray-700\">{region.count}</span>\n                        <span className=\"text-xs text-gray-500\">({region.percentage}%)</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Submission Trends */}\n              <div className=\"bg-white shadow rounded-lg p-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">提交趋势（最近30天）</h3>\n                <div className=\"h-64 flex items-end space-x-1\">\n                  {analytics.submissionTrends.slice(-30).map((trend, index) => {\n                    const maxCount = Math.max(...analytics.submissionTrends.map(t => t.count));\n                    const height = (trend.count / maxCount) * 100;\n                    return (\n                      <div\n                        key={trend.date}\n                        className=\"flex-1 bg-blue-500 rounded-t\"\n                        style={{ height: `${height}%` }}\n                        title={`${trend.date}: ${trend.count} 条咨询`}\n                      ></div>\n                    );\n                  })}\n                </div>\n                <div className=\"mt-2 flex justify-between text-xs text-gray-500\">\n                  <span>30天前</span>\n                  <span>今天</span>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AA0Ce,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,iBAAiB;gBACnB;YACF;QACF;8BAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,qBAAqB;YACzB,SAAS;gBAAE,iBAAiB,AAAC,UAAe,OAAN;YAAQ;QAChD,GACC,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS,EAAE,EAAE;gBACf,mBAAmB;YACrB,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,GACC,KAAK,CAAC;YACL,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,GACC,OAAO,CAAC;YACP,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,4CAAqD,OAAV,YAAa;gBACpF,SAAS;oBAAE,iBAAiB,AAAC,UAAe,OAAN;gBAAQ;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,aAAa,OAAO,IAAI;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ;wBAAC;wBAAM;wBAAe;wBAAiB;wBAAS;wBAAS;wBAAe;wBAAU;qBAAc;gBAC1G;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,AAAC,eAAwD,OAA1C,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC,KAAuC,OAApC,WAAW,UAAU,SAAS;gBACpG,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ;YACZ,qBAAqB;YACrB,iBAAiB;YACjB,cAAc;YACd,iBAAiB;QACnB;QACA,OAAO,KAAK,CAAC,KAA2B,IAAI;IAC9C;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ;YACZ,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA,OAAO,KAAK,CAAC,OAA6B,IAAI;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,IAAI,WAAW;QACb,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAO,WAAU;kCAChB,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,0JAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;0DAGD,0JAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,0JAAC,6OAAA,CAAA,4BAAyB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhE,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,WAAU;;8DAEV,0JAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,0JAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,0JAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,0JAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;kDAIvB,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,UAAU;gDACV,WAAU;;kEAEV,0JAAC,6NAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGhD,0JAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,UAAU;gDACV,WAAU;;kEAEV,0JAAC,6NAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAMnD,2BACC;;kDAEE,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC,mNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;0EAE1B,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;;sFACC,0JAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,0JAAC;4EAAG,WAAU;sFAAqC,UAAU,gBAAgB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOtG,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC,mNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;0EAE1B,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;;sFACC,0JAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,0JAAC;4EAAG,WAAU;sFAAqC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOvF,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC,kLAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;0EAE5B,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;;sFACC,0JAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,0JAAC;4EAAG,WAAU;;gFAAqC,UAAU,cAAc;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOtF,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC,6MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAEvB,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;;sFACC,0JAAC;4EAAG,WAAU;sFAA6C;;;;;;sFAC3D,0JAAC;4EAAG,WAAU;;gFAAqC,UAAU,mBAAmB;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAS7F,0JAAC;wCAAI,WAAU;;0DAEb,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,0JAAC;wDAAI,WAAU;kEACZ,UAAU,eAAe,CAAC,GAAG,CAAC,CAAC,SAAS,sBACvC,0JAAC;gEAAuB,WAAU;;kFAChC,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAW,AAAC,6BAIhB,OAHC,UAAU,IAAI,gBACd,UAAU,IAAI,iBACd,UAAU,IAAI,kBAAkB;;;;;;0FAElC,0JAAC;gFAAK,WAAU;0FAAyB,mBAAmB,QAAQ,IAAI;;;;;;;;;;;;kFAE1E,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAK,WAAU;0FAAqC,QAAQ,KAAK;;;;;;0FAClE,0JAAC;gFAAK,WAAU;;oFAAwB;oFAAE,QAAQ,UAAU;oFAAC;;;;;;;;;;;;;;+DAXvD,QAAQ,IAAI;;;;;;;;;;;;;;;;0DAmB5B,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,0JAAC;wDAAI,WAAU;kEACZ,UAAU,kBAAkB,CAAC,GAAG,CAAC,CAAC,uBACjC,0JAAC;gEAAwB,WAAU;;kFACjC,0JAAC;wEAAI,WAAU;kFACb,cAAA,0JAAC;4EAAK,WAAW,AAAC,4DAAyF,OAA9B,eAAe,OAAO,MAAM;sFACtG,cAAc,OAAO,MAAM;;;;;;;;;;;kFAGhC,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAK,WAAU;0FAAqC,OAAO,KAAK;;;;;;0FACjE,0JAAC;gFAAK,WAAU;;oFAAwB;oFAAE,OAAO,UAAU;oFAAC;;;;;;;;;;;;;;+DARtD,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kDAiB/B,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,0JAAC;gDAAI,WAAU;0DACZ,UAAU,sBAAsB,CAAC,GAAG,CAAC,CAAC,uBACrC,0JAAC;wDAAwB,WAAU;;0EACjC,0JAAC;gEAAK,WAAU;0EAAqC,OAAO,MAAM;;;;;;0EAClE,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEAAK,WAAU;kFAAyB,OAAO,KAAK;;;;;;kFACrD,0JAAC;wEAAK,WAAU;;4EAAwB;4EAAE,OAAO,UAAU;4EAAC;;;;;;;;;;;;;;uDAJtD,OAAO,MAAM;;;;;;;;;;;;;;;;kDAY7B,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,0JAAC;gDAAI,WAAU;0DACZ,UAAU,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO;oDACjD,MAAM,WAAW,KAAK,GAAG,IAAI,UAAU,gBAAgB,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;oDACxE,MAAM,SAAS,AAAC,MAAM,KAAK,GAAG,WAAY;oDAC1C,qBACE,0JAAC;wDAEC,WAAU;wDACV,OAAO;4DAAE,QAAQ,AAAC,GAAS,OAAP,QAAO;wDAAG;wDAC9B,OAAO,AAAC,GAAiB,OAAf,MAAM,IAAI,EAAC,MAAgB,OAAZ,MAAM,KAAK,EAAC;uDAHhC,MAAM,IAAI;;;;;gDAMrB;;;;;;0DAEF,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;kEAAK;;;;;;kEACN,0JAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GAzXwB;;QAMP,0HAAA,CAAA,YAAS;;;KANF", "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/admin/analytics\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}