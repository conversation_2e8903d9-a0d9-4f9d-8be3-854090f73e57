{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\n\ntype SendMessage = (msg: any) => void\nexport type WebSocketMessage =\n  | {\n      type: 'turbopack-connected'\n    }\n  | {\n      type: 'turbopack-message'\n      data: Record<string, any>\n    }\n\nexport type ClientOptions = {\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void\n  sendMessage: SendMessage\n  onUpdateError: (err: unknown) => void\n}\n\nexport function connect({\n  addMessageListener,\n  sendMessage,\n  onUpdateError = console.error,\n}: ClientOptions) {\n  addMessageListener((msg) => {\n    switch (msg.type) {\n      case 'turbopack-connected':\n        handleSocketConnected(sendMessage)\n        break\n      default:\n        try {\n          if (Array.isArray(msg.data)) {\n            for (let i = 0; i < msg.data.length; i++) {\n              handleSocketMessage(msg.data[i] as ServerMessage)\n            }\n          } else {\n            handleSocketMessage(msg.data as ServerMessage)\n          }\n          applyAggregatedUpdates()\n        } catch (e: unknown) {\n          console.warn(\n            '[Fast Refresh] performing full reload\\n\\n' +\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n              'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n              'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n              'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n              'Fast Refresh requires at least one parent function component in your React tree.'\n          )\n          onUpdateError(e)\n          location.reload()\n        }\n        break\n    }\n  })\n\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS\n  if (queued != null && !Array.isArray(queued)) {\n    throw new Error('A separate HMR handler was already registered')\n  }\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    },\n  }\n\n  if (Array.isArray(queued)) {\n    for (const [chunkPath, callback] of queued) {\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback)\n    }\n  }\n}\n\ntype UpdateCallbackSet = {\n  callbacks: Set<UpdateCallback>\n  unsubscribe: () => void\n}\n\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map()\n\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\n  sendMessage(JSON.stringify(message))\n}\n\ntype ResourceKey = string\n\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\n  return JSON.stringify({\n    path: resource.path,\n    headers: resource.headers || null,\n  })\n}\n\nfunction subscribeToUpdates(\n  sendMessage: SendMessage,\n  resource: ResourceIdentifier\n): () => void {\n  sendJSON(sendMessage, {\n    type: 'turbopack-subscribe',\n    ...resource,\n  })\n\n  return () => {\n    sendJSON(sendMessage, {\n      type: 'turbopack-unsubscribe',\n      ...resource,\n    })\n  }\n}\n\nfunction handleSocketConnected(sendMessage: SendMessage) {\n  for (const key of updateCallbackSets.keys()) {\n    subscribeToUpdates(sendMessage, JSON.parse(key))\n  }\n}\n\n// we aggregate all pending updates until the issues are resolved\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\n  new Map()\n\nfunction aggregateUpdates(msg: PartialServerMessage) {\n  const key = resourceKey(msg.resource)\n  let aggregated = chunkListsWithPendingUpdates.get(key)\n\n  if (aggregated) {\n    aggregated.instruction = mergeChunkListUpdates(\n      aggregated.instruction,\n      msg.instruction\n    )\n  } else {\n    chunkListsWithPendingUpdates.set(key, msg)\n  }\n}\n\nfunction applyAggregatedUpdates() {\n  if (chunkListsWithPendingUpdates.size === 0) return\n  hooks.beforeRefresh()\n  for (const msg of chunkListsWithPendingUpdates.values()) {\n    triggerUpdate(msg)\n  }\n  chunkListsWithPendingUpdates.clear()\n  finalizeUpdate()\n}\n\nfunction mergeChunkListUpdates(\n  updateA: ChunkListUpdate,\n  updateB: ChunkListUpdate\n): ChunkListUpdate {\n  let chunks\n  if (updateA.chunks != null) {\n    if (updateB.chunks == null) {\n      chunks = updateA.chunks\n    } else {\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks)\n    }\n  } else if (updateB.chunks != null) {\n    chunks = updateB.chunks\n  }\n\n  let merged\n  if (updateA.merged != null) {\n    if (updateB.merged == null) {\n      merged = updateA.merged\n    } else {\n      // Since `merged` is an array of updates, we need to merge them all into\n      // one, consistent update.\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\n      // no need to key on the `type` field.\n      let update = updateA.merged[0]\n      for (let i = 1; i < updateA.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateA.merged[i]\n        )\n      }\n\n      for (let i = 0; i < updateB.merged.length; i++) {\n        update = mergeChunkListEcmascriptMergedUpdates(\n          update,\n          updateB.merged[i]\n        )\n      }\n\n      merged = [update]\n    }\n  } else if (updateB.merged != null) {\n    merged = updateB.merged\n  }\n\n  return {\n    type: 'ChunkListUpdate',\n    chunks,\n    merged,\n  }\n}\n\nfunction mergeChunkListChunks(\n  chunksA: Record<ChunkPath, ChunkUpdate>,\n  chunksB: Record<ChunkPath, ChunkUpdate>\n): Record<ChunkPath, ChunkUpdate> {\n  const chunks: Record<ChunkPath, ChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB)\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, ChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  return chunks\n}\n\nfunction mergeChunkUpdates(\n  updateA: ChunkUpdate,\n  updateB: ChunkUpdate\n): ChunkUpdate | undefined {\n  if (\n    (updateA.type === 'added' && updateB.type === 'deleted') ||\n    (updateA.type === 'deleted' && updateB.type === 'added')\n  ) {\n    return undefined\n  }\n\n  if (updateA.type === 'partial') {\n    invariant(updateA.instruction, 'Partial updates are unsupported')\n  }\n\n  if (updateB.type === 'partial') {\n    invariant(updateB.instruction, 'Partial updates are unsupported')\n  }\n\n  return undefined\n}\n\nfunction mergeChunkListEcmascriptMergedUpdates(\n  mergedA: EcmascriptMergedUpdate,\n  mergedB: EcmascriptMergedUpdate\n): EcmascriptMergedUpdate {\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries)\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks)\n\n  return {\n    type: 'EcmascriptMergedUpdate',\n    entries,\n    chunks,\n  }\n}\n\nfunction mergeEcmascriptChunkEntries(\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\n): Record<ModuleId, EcmascriptModuleEntry> {\n  return { ...entriesA, ...entriesB }\n}\n\nfunction mergeEcmascriptChunksUpdates(\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\n  if (chunksA == null) {\n    return chunksB\n  }\n\n  if (chunksB == null) {\n    return chunksA\n  }\n\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {}\n\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    const chunkUpdateB = chunksB[chunkPath]\n    if (chunkUpdateB != null) {\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\n        chunkUpdateA,\n        chunkUpdateB\n      )\n      if (mergedUpdate != null) {\n        chunks[chunkPath] = mergedUpdate\n      }\n    } else {\n      chunks[chunkPath] = chunkUpdateA\n    }\n  }\n\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<\n    [ChunkPath, EcmascriptMergedChunkUpdate]\n  >) {\n    if (chunks[chunkPath] == null) {\n      chunks[chunkPath] = chunkUpdateB\n    }\n  }\n\n  if (Object.keys(chunks).length === 0) {\n    return undefined\n  }\n\n  return chunks\n}\n\nfunction mergeEcmascriptChunkUpdates(\n  updateA: EcmascriptMergedChunkUpdate,\n  updateB: EcmascriptMergedChunkUpdate\n): EcmascriptMergedChunkUpdate | undefined {\n  if (updateA.type === 'added' && updateB.type === 'deleted') {\n    // These two completely cancel each other out.\n    return undefined\n  }\n\n  if (updateA.type === 'deleted' && updateB.type === 'added') {\n    const added = []\n    const deleted = []\n    const deletedModules = new Set(updateA.modules ?? [])\n    const addedModules = new Set(updateB.modules ?? [])\n\n    for (const moduleId of addedModules) {\n      if (!deletedModules.has(moduleId)) {\n        added.push(moduleId)\n      }\n    }\n\n    for (const moduleId of deletedModules) {\n      if (!addedModules.has(moduleId)) {\n        deleted.push(moduleId)\n      }\n    }\n\n    if (added.length === 0 && deleted.length === 0) {\n      return undefined\n    }\n\n    return {\n      type: 'partial',\n      added,\n      deleted,\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'partial') {\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])])\n    const deleted = new Set([\n      ...(updateA.deleted ?? []),\n      ...(updateB.deleted ?? []),\n    ])\n\n    if (updateB.added != null) {\n      for (const moduleId of updateB.added) {\n        deleted.delete(moduleId)\n      }\n    }\n\n    if (updateB.deleted != null) {\n      for (const moduleId of updateB.deleted) {\n        added.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'partial',\n      added: [...added],\n      deleted: [...deleted],\n    }\n  }\n\n  if (updateA.type === 'added' && updateB.type === 'partial') {\n    const modules = new Set([\n      ...(updateA.modules ?? []),\n      ...(updateB.added ?? []),\n    ])\n\n    for (const moduleId of updateB.deleted ?? []) {\n      modules.delete(moduleId)\n    }\n\n    return {\n      type: 'added',\n      modules: [...modules],\n    }\n  }\n\n  if (updateA.type === 'partial' && updateB.type === 'deleted') {\n    // We could eagerly return `updateB` here, but this would potentially be\n    // incorrect if `updateA` has added modules.\n\n    const modules = new Set(updateB.modules ?? [])\n\n    if (updateA.added != null) {\n      for (const moduleId of updateA.added) {\n        modules.delete(moduleId)\n      }\n    }\n\n    return {\n      type: 'deleted',\n      modules: [...modules],\n    }\n  }\n\n  // Any other update combination is invalid.\n\n  return undefined\n}\n\nfunction invariant(_: never, message: string): never {\n  throw new Error(`Invariant: ${message}`)\n}\n\nconst CRITICAL = ['bug', 'error', 'fatal']\n\nfunction compareByList(list: any[], a: any, b: any) {\n  const aI = list.indexOf(a) + 1 || list.length\n  const bI = list.indexOf(b) + 1 || list.length\n  return aI - bI\n}\n\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map()\n\nfunction emitIssues() {\n  const issues = []\n  const deduplicationSet = new Set()\n\n  for (const [_, chunkIssues] of chunksWithIssues) {\n    for (const chunkIssue of chunkIssues) {\n      if (deduplicationSet.has(chunkIssue.formatted)) continue\n\n      issues.push(chunkIssue)\n      deduplicationSet.add(chunkIssue.formatted)\n    }\n  }\n\n  sortIssues(issues)\n\n  hooks.issues(issues)\n}\n\nfunction handleIssues(msg: ServerMessage): boolean {\n  const key = resourceKey(msg.resource)\n  let hasCriticalIssues = false\n\n  for (const issue of msg.issues) {\n    if (CRITICAL.includes(issue.severity)) {\n      hasCriticalIssues = true\n    }\n  }\n\n  if (msg.issues.length > 0) {\n    chunksWithIssues.set(key, msg.issues)\n  } else if (chunksWithIssues.has(key)) {\n    chunksWithIssues.delete(key)\n  }\n\n  emitIssues()\n\n  return hasCriticalIssues\n}\n\nconst SEVERITY_ORDER = ['bug', 'fatal', 'error', 'warning', 'info', 'log']\nconst CATEGORY_ORDER = [\n  'parse',\n  'resolve',\n  'code generation',\n  'rendering',\n  'typescript',\n  'other',\n]\n\nfunction sortIssues(issues: Issue[]) {\n  issues.sort((a, b) => {\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity)\n    if (first !== 0) return first\n    return compareByList(CATEGORY_ORDER, a.category, b.category)\n  })\n}\n\nconst hooks = {\n  beforeRefresh: () => {},\n  refresh: () => {},\n  buildOk: () => {},\n  issues: (_issues: Issue[]) => {},\n}\n\nexport function setHooks(newHooks: typeof hooks) {\n  Object.assign(hooks, newHooks)\n}\n\nfunction handleSocketMessage(msg: ServerMessage) {\n  sortIssues(msg.issues)\n\n  handleIssues(msg)\n\n  switch (msg.type) {\n    case 'issues':\n      // issues are already handled\n      break\n    case 'partial':\n      // aggregate updates\n      aggregateUpdates(msg)\n      break\n    default:\n      // run single update\n      const runHooks = chunkListsWithPendingUpdates.size === 0\n      if (runHooks) hooks.beforeRefresh()\n      triggerUpdate(msg)\n      if (runHooks) finalizeUpdate()\n      break\n  }\n}\n\nfunction finalizeUpdate() {\n  hooks.refresh()\n  hooks.buildOk()\n\n  // This is used by the Next.js integration test suite to notify it when HMR\n  // updates have been completed.\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\n  if (globalThis.__NEXT_HMR_CB) {\n    globalThis.__NEXT_HMR_CB()\n    globalThis.__NEXT_HMR_CB = null\n  }\n}\n\nfunction subscribeToChunkUpdate(\n  chunkListPath: ChunkListPath,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n): () => void {\n  return subscribeToUpdate(\n    {\n      path: chunkListPath,\n    },\n    sendMessage,\n    callback\n  )\n}\n\nexport function subscribeToUpdate(\n  resource: ResourceIdentifier,\n  sendMessage: SendMessage,\n  callback: UpdateCallback\n) {\n  const key = resourceKey(resource)\n  let callbackSet: UpdateCallbackSet\n  const existingCallbackSet = updateCallbackSets.get(key)\n  if (!existingCallbackSet) {\n    callbackSet = {\n      callbacks: new Set([callback]),\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\n    }\n    updateCallbackSets.set(key, callbackSet)\n  } else {\n    existingCallbackSet.callbacks.add(callback)\n    callbackSet = existingCallbackSet\n  }\n\n  return () => {\n    callbackSet.callbacks.delete(callback)\n\n    if (callbackSet.callbacks.size === 0) {\n      callbackSet.unsubscribe()\n      updateCallbackSets.delete(key)\n    }\n  }\n}\n\nfunction triggerUpdate(msg: ServerMessage) {\n  const key = resourceKey(msg.resource)\n  const callbackSet = updateCallbackSets.get(key)\n  if (!callbackSet) {\n    return\n  }\n\n  for (const callback of callbackSet.callbacks) {\n    callback(msg)\n  }\n\n  if (msg.type === 'notFound') {\n    // This indicates that the resource which we subscribed to either does not exist or\n    // has been deleted. In either case, we should clear all update callbacks, so if a\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\n    // message to the server.\n    // No need to send an \"unsubscribe\" message to the server, it will have already\n    // dropped the update stream before sending the \"notFound\" message.\n    updateCallbackSets.delete(key)\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAkBtD,SAAS,QAAQ,KAIR;QAJQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf,GAJQ;IAKtB,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM;gBAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAEpD;QACD,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;YACa;QAA/B,MAAM,iBAAiB,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;YACvB;QAA7B,MAAM,eAAe,IAAI,IAAI,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YACjC,gBAA0B;QAArD,MAAM,QAAQ,IAAI,IAAI;eAAK,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;eAAO,CAAA,iBAAA,QAAQ,KAAK,cAAb,4BAAA,iBAAiB,EAAE;SAAE;YAEpE,kBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;eACrB,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;YAEpD,mBACA;QAFN,MAAM,UAAU,IAAI,IAAI;eAClB,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;eACrB,CAAA,kBAAA,QAAQ,KAAK,cAAb,6BAAA,kBAAiB,EAAE;SACxB;YAEsB;QAAvB,KAAK,MAAM,YAAY,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;YAIpC;QAHxB,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,AAAC,cAAqB,OAAR;AAChC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' },\n  { code: 'ru', name: 'Русский', flag: '🇷🇺' },\n];\n\nexport default function LanguageSwitcher() {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];\n\n  const handleLanguageChange = (langCode: string) => {\n    const { pathname, asPath, query } = router;\n    router.push({ pathname, query }, asPath, { locale: langCode });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n        aria-label={t('navigation.language')}\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.name}</span>\n        <span className=\"sm:hidden\">{currentLanguage.flag}</span>\n        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 ${\n                    language.code === router.locale\n                      ? 'bg-blue-50 text-blue-600'\n                      : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {language.code === router.locale && (\n                    <span className=\"ml-auto text-blue-600\">✓</span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;;;AALA;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC,EAAE;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QACpC,OAAO,IAAI,CAAC;YAAE;YAAU;QAAM,GAAG,QAAQ;YAAE,QAAQ;QAAS;QAC5D,UAAU;IACZ;IAEA,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAY,EAAE;;kCAEd,0JAAC,mNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,0JAAC;wBAAK,WAAU;kCAAoB,gBAAgB,IAAI;;;;;;kCACxD,0JAAC;wBAAK,WAAU;kCAAa,gBAAgB,IAAI;;;;;;kCACjD,0JAAC,yNAAA,CAAA,kBAAe;wBAAC,WAAW,AAAC,6CAAuE,OAA3B,SAAS,eAAe;;;;;;;;;;;;YAGlG,wBACC;;kCACE,0JAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,0JAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,AAAC,+FAIX,OAHC,SAAS,IAAI,KAAK,OAAO,MAAM,GAC3B,6BACA;;sDAGN,0JAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,0JAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,SAAS,IAAI,KAAK,OAAO,MAAM,kBAC9B,0JAAC;4CAAK,WAAU;sDAAwB;;;;;;;mCAXrC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC;GAzDwB;;QAEP,0HAAA,CAAA,YAAS;QACV,4JAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/router';\nimport { useTranslation } from 'next-i18next';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport LanguageSwitcher from './LanguageSwitcher';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const { t } = useTranslation('common');\n\n  const navigation = [\n    { name: t('navigation.home'), href: '/' },\n    { name: t('navigation.services'), href: '/services' },\n    { name: t('navigation.features'), href: '/features' },\n    { name: t('navigation.contact'), href: '/contact' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return router.pathname === '/';\n    }\n    return router.pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <span className=\"text-xl font-bold text-gray-900\">{t('brand.name')}</span>\n                <p className=\"text-xs text-gray-500 max-w-xs truncate\">{t('brand.tagline')}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-blue-600 border-b-2 border-blue-600'\n                    : 'text-gray-700 hover:text-blue-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side - Language switcher and CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LanguageSwitcher />\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('buttons.contact_us')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-2\">\n            <LanguageSwitcher />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-2 border-t border-gray-200\">\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  {t('buttons.contact_us')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAoB,MAAM;QAAI;QACxC;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;KACnD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,OAAO,QAAQ,KAAK;QAC7B;QACA,OAAO,OAAO,QAAQ,CAAC,UAAU,CAAC;IACpC;IAEA,qBACE,0JAAC;QAAO,WAAU;kBAChB,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAK,WAAU;0DAAmC,EAAE;;;;;;0DACrD,0JAAC;gDAAE,WAAU;0DAA2C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,0JAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC,wHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,gEAIX,OAHC,SAAS,KAAK,IAAI,IACd,6CACA;8CAGL,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,0JAAC;4BAAI,WAAU;;8CACb,0JAAC,4IAAA,CAAA,UAAgB;;;;;8CACjB,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,0JAAC;4BAAI,WAAU;;8CACb,0JAAC,4IAAA,CAAA,UAAgB;;;;;8CACjB,0JAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BACC,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,0JAAC,6MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,0JAAC,wHAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,AAAC,wEAIX,OAHC,SAAS,KAAK,IAAI,IACd,6BACA;8CAGL,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GAlHwB;;QAEP,0HAAA,CAAA,YAAS;QACV,4JAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  ServerIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const { t } = useTranslation('common');\n\n  const services = [\n    { name: '外贸网络线路', href: '/services/foreign-trade' },\n    { name: '跨境电商线路', href: '/services/ecommerce' },\n    { name: 'VPN服务', href: '/services/vpn' },\n    { name: '定制解决方案', href: '/services/custom' },\n  ];\n\n  const support = [\n    { name: '技术支持', href: '/support' },\n    { name: '服务条款', href: '/terms' },\n    { name: '隐私政策', href: '/privacy' },\n    { name: '常见问题', href: '/faq' },\n  ];\n\n  const company = [\n    { name: '关于我们', href: '/about' },\n    { name: '新闻动态', href: '/news' },\n    { name: '合作伙伴', href: '/partners' },\n    { name: '招聘信息', href: '/careers' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg\">\n                <span className=\"text-white font-bold text-lg\">VPL</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t('brand.name')}</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 text-sm leading-relaxed\">\n              {t('brand.tagline')}\n            </p>\n            \n            {/* Key features */}\n            <div className=\"space-y-2 mb-6\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ShieldCheckIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>AES/RSA/TLS加密</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <GlobeAltIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>全球网络覆盖</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <ServerIcon className=\"h-4 w-4 text-blue-400\" />\n                <span>7x24技术支持</span>\n              </div>\n            </div>\n\n            {/* Contact info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+86 400-xxx-xxxx</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>中国 · 深圳</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.services')}</h3>\n            <ul className=\"space-y-2\">\n              {services.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.support')}</h3>\n            <ul className=\"space-y-2\">\n              {support.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('footer.company')}</h3>\n            <ul className=\"space-y-2\">\n              {company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('footer.copyright')}\n            </p>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                服务条款\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                隐私政策\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                网站地图\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAae,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM;QAA0B;QAClD;YAAE,MAAM;YAAU,MAAM;QAAsB;QAC9C;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAU,MAAM;QAAmB;KAC5C;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAO;KAC9B;IAED,MAAM,UAAU;QACd;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAY;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,0JAAC;QAAO,WAAU;kBAChB,cAAA,0JAAC;YAAI,WAAU;;8BAEb,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,0JAAC;4CAAK,WAAU;sDAAqB,EAAE;;;;;;;;;;;;8CAEzC,0JAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,yNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,mNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,+MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,0JAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,6MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,mNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,0JAAC;8DAAK;;;;;;;;;;;;sDAER,0JAAC;4CAAI,WAAU;;8DACb,0JAAC,+MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,0JAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,qBACb,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,0JAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,0JAAC;sDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,0JAAC;gCAAI,WAAU;;kDACb,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwE;;;;;;kDAGtG,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;kDAGxG,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStH;GApJwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function Layout({ children, className = '' }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,OAAO,KAAyC;QAAzC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAe,GAAzC;IAC7B,qBACE,0JAAC;QAAI,WAAU;;0BACb,0JAAC,kIAAA,CAAA,UAAM;;;;;0BACP,0JAAC;gBAAK,WAAW,AAAC,UAAmB,OAAV;0BACxB;;;;;;0BAEH,0JAAC,kIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KAVwB", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/components/forms/ContactForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { useTranslation } from 'next-i18next';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  ChatBubbleLeftRightIcon,\n  UserIcon\n} from '@heroicons/react/24/outline';\n\n// Form validation schema\nconst contactSchema = z.object({\n  companyName: z.string().min(1, 'Company name is required'),\n  contactPerson: z.string().min(1, 'Contact person is required'),\n  phone: z.string().min(1, 'Phone number is required'),\n  email: z.string().email('Invalid email address'),\n  wechat: z.string().optional(),\n  qq: z.string().optional(),\n  serviceType: z.string().min(1, 'Service type is required'),\n  message: z.string().min(10, 'Message must be at least 10 characters'),\n  verificationCode: z.string().min(1, 'Verification code is required'),\n});\n\ntype ContactFormData = z.infer<typeof contactSchema>;\n\nexport default function ContactForm() {\n  const { t } = useTranslation(['contact', 'common']);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [verificationCode, setVerificationCode] = useState('');\n  const [generatedCode, setGeneratedCode] = useState('');\n  const [showSuccess, setShowSuccess] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ContactFormData>({\n    resolver: zodResolver(contactSchema),\n  });\n\n  // Generate alphanumeric verification code\n  const generateVerificationCode = () => {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = '';\n    for (let i = 0; i < 6; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    setGeneratedCode(result);\n    return result;\n  };\n\n  const onSubmit = async (data: ContactFormData) => {\n    if (data.verificationCode !== generatedCode) {\n      alert(t('contact:error.verification_failed'));\n      return;\n    }\n\n    setIsSubmitting(true);\n    \n    try {\n      const response = await fetch('/api/contact', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (response.ok) {\n        setShowSuccess(true);\n        reset();\n        setGeneratedCode('');\n      } else {\n        throw new Error('Failed to submit form');\n      }\n    } catch (error) {\n      alert(t('contact:error.message'));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const serviceTypes = [\n    { value: 'foreign_trade_lines', label: t('contact:services.foreign_trade_lines') },\n    { value: 'ecommerce_lines', label: t('contact:services.ecommerce_lines') },\n    { value: 'vpn_services', label: t('contact:services.vpn_services') },\n    { value: 'custom_solution', label: t('contact:services.custom_solution') },\n  ];\n\n  if (showSuccess) {\n    return (\n      <div className=\"bg-green-50 border border-green-200 rounded-lg p-8 text-center\">\n        <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 mb-4\">\n          <svg className=\"h-8 w-8 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 12.75l6 6 9-13.5\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-semibold text-green-900 mb-2\">\n          {t('contact:success.title')}\n        </h3>\n        <p className=\"text-green-700 mb-6\">\n          {t('contact:success.message')}\n        </p>\n        <button\n          onClick={() => setShowSuccess(false)}\n          className=\"bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors duration-200\"\n        >\n          {t('common:buttons.back')}\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n      {/* Company Information */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div>\n          <label htmlFor=\"companyName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.company_name')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            {...register('companyName')}\n            type=\"text\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            placeholder={t('contact:form.company_name_placeholder')}\n          />\n          {errors.companyName && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.companyName.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"contactPerson\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.contact_person')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            {...register('contactPerson')}\n            type=\"text\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            placeholder={t('contact:form.contact_person_placeholder')}\n          />\n          {errors.contactPerson && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.contactPerson.message}</p>\n          )}\n        </div>\n      </div>\n\n      {/* Contact Information */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.phone')} <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <PhoneIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('phone')}\n              type=\"tel\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.phone_placeholder')}\n            />\n          </div>\n          {errors.phone && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.email')} <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <EnvelopeIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('email')}\n              type=\"email\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.email_placeholder')}\n            />\n          </div>\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n          )}\n        </div>\n      </div>\n\n      {/* Additional Contact Methods */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div>\n          <label htmlFor=\"wechat\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.wechat')}\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <ChatBubbleLeftRightIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('wechat')}\n              type=\"text\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.wechat_placeholder')}\n            />\n          </div>\n        </div>\n\n        <div>\n          <label htmlFor=\"qq\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('contact:form.qq')}\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <UserIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              {...register('qq')}\n              type=\"text\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={t('contact:form.qq_placeholder')}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Service Type */}\n      <div>\n        <label htmlFor=\"serviceType\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('contact:form.service_type')} <span className=\"text-red-500\">*</span>\n        </label>\n        <select\n          {...register('serviceType')}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n        >\n          <option value=\"\">{t('contact:form.service_type_placeholder')}</option>\n          {serviceTypes.map((type) => (\n            <option key={type.value} value={type.value}>\n              {type.label}\n            </option>\n          ))}\n        </select>\n        {errors.serviceType && (\n          <p className=\"mt-1 text-sm text-red-600\">{errors.serviceType.message}</p>\n        )}\n      </div>\n\n      {/* Message */}\n      <div>\n        <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('contact:form.message')} <span className=\"text-red-500\">*</span>\n        </label>\n        <textarea\n          {...register('message')}\n          rows={4}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n          placeholder={t('contact:form.message_placeholder')}\n        />\n        {errors.message && (\n          <p className=\"mt-1 text-sm text-red-600\">{errors.message.message}</p>\n        )}\n      </div>\n\n      {/* Verification Code */}\n      <div>\n        <label htmlFor=\"verificationCode\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('contact:form.verification_code')} <span className=\"text-red-500\">*</span>\n        </label>\n        <div className=\"flex space-x-3\">\n          <input\n            {...register('verificationCode')}\n            type=\"text\"\n            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            placeholder={t('contact:form.verification_code_placeholder')}\n          />\n          <button\n            type=\"button\"\n            onClick={generateVerificationCode}\n            className=\"px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors duration-200\"\n          >\n            {t('contact:form.get_verification_code')}\n          </button>\n        </div>\n        {generatedCode && (\n          <div className=\"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md\">\n            <p className=\"text-sm text-blue-800\">\n              验证码: <span className=\"font-mono font-bold text-lg\">{generatedCode}</span>\n            </p>\n          </div>\n        )}\n        {errors.verificationCode && (\n          <p className=\"mt-1 text-sm text-red-600\">{errors.verificationCode.message}</p>\n        )}\n      </div>\n\n      {/* Submit Button */}\n      <div>\n        <button\n          type=\"submit\"\n          disabled={isSubmitting}\n          className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n        >\n          {isSubmitting ? t('contact:form.submitting') : t('contact:form.submit')}\n        </button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAcA,yBAAyB;AACzB,MAAM,gBAAgB,yKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,aAAa,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,eAAe,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,OAAO,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,OAAO,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,QAAQ,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,IAAI,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,aAAa,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,SAAS,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC5B,kBAAkB,yKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACtC;AAIe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAW;KAAS;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,0CAA0C;IAC1C,MAAM,2BAA2B;QAC/B,MAAM,QAAQ;QACd,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;QAChE;QACA,iBAAiB;QACjB,OAAO;IACT;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,KAAK,gBAAgB,KAAK,eAAe;YAC3C,MAAM,EAAE;YACR;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf;gBACA,iBAAiB;YACnB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,EAAE;QACV,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB;YAAE,OAAO;YAAuB,OAAO,EAAE;QAAwC;QACjF;YAAE,OAAO;YAAmB,OAAO,EAAE;QAAoC;QACzE;YAAE,OAAO;YAAgB,OAAO,EAAE;QAAiC;QACnE;YAAE,OAAO;YAAmB,OAAO,EAAE;QAAoC;KAC1E;IAED,IAAI,aAAa;QACf,qBACE,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;wBAAyB,MAAK;wBAAO,SAAQ;wBAAY,aAAY;wBAAM,QAAO;kCAC/F,cAAA,0JAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,GAAE;;;;;;;;;;;;;;;;8BAGzD,0JAAC;oBAAG,WAAU;8BACX,EAAE;;;;;;8BAEL,0JAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAEL,0JAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;8BAET,EAAE;;;;;;;;;;;;IAIX;IAEA,qBACE,0JAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAEhD,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;;0CACC,0JAAC;gCAAM,SAAQ;gCAAc,WAAU;;oCACpC,EAAE;oCAA6B;kDAAC,0JAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAElE,0JAAC;gCACE,GAAG,SAAS,cAAc;gCAC3B,MAAK;gCACL,WAAU;gCACV,aAAa,EAAE;;;;;;4BAEhB,OAAO,WAAW,kBACjB,0JAAC;gCAAE,WAAU;0CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAIxE,0JAAC;;0CACC,0JAAC;gCAAM,SAAQ;gCAAgB,WAAU;;oCACtC,EAAE;oCAA+B;kDAAC,0JAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEpE,0JAAC;gCACE,GAAG,SAAS,gBAAgB;gCAC7B,MAAK;gCACL,WAAU;gCACV,aAAa,EAAE;;;;;;4BAEhB,OAAO,aAAa,kBACnB,0JAAC;gCAAE,WAAU;0CAA6B,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0BAM5E,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;;0CACC,0JAAC;gCAAM,SAAQ;gCAAQ,WAAU;;oCAC9B,EAAE;oCAAsB;kDAAC,0JAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE3D,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,6MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,0JAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;4BAGlB,OAAO,KAAK,kBACX,0JAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAIlE,0JAAC;;0CACC,0JAAC;gCAAM,SAAQ;gCAAQ,WAAU;;oCAC9B,EAAE;oCAAsB;kDAAC,0JAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE3D,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,mNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,0JAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;4BAGlB,OAAO,KAAK,kBACX,0JAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;0BAMpE,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;;0CACC,0JAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAC/B,EAAE;;;;;;0CAEL,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,yOAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;;;;;;kDAErC,0JAAC;wCACE,GAAG,SAAS,SAAS;wCACtB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;kCAKrB,0JAAC;;0CACC,0JAAC;gCAAM,SAAQ;gCAAK,WAAU;0CAC3B,EAAE;;;;;;0CAEL,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,2MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,0JAAC;wCACE,GAAG,SAAS,KAAK;wCAClB,MAAK;wCACL,WAAU;wCACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,0JAAC;;kCACC,0JAAC;wBAAM,SAAQ;wBAAc,WAAU;;4BACpC,EAAE;4BAA6B;0CAAC,0JAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAElE,0JAAC;wBACE,GAAG,SAAS,cAAc;wBAC3B,WAAU;;0CAEV,0JAAC;gCAAO,OAAM;0CAAI,EAAE;;;;;;4BACnB,aAAa,GAAG,CAAC,CAAC,qBACjB,0JAAC;oCAAwB,OAAO,KAAK,KAAK;8CACvC,KAAK,KAAK;mCADA,KAAK,KAAK;;;;;;;;;;;oBAK1B,OAAO,WAAW,kBACjB,0JAAC;wBAAE,WAAU;kCAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0BAKxE,0JAAC;;kCACC,0JAAC;wBAAM,SAAQ;wBAAU,WAAU;;4BAChC,EAAE;4BAAwB;0CAAC,0JAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAE7D,0JAAC;wBACE,GAAG,SAAS,UAAU;wBACvB,MAAM;wBACN,WAAU;wBACV,aAAa,EAAE;;;;;;oBAEhB,OAAO,OAAO,kBACb,0JAAC;wBAAE,WAAU;kCAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0BAKpE,0JAAC;;kCACC,0JAAC;wBAAM,SAAQ;wBAAmB,WAAU;;4BACzC,EAAE;4BAAkC;0CAAC,0JAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAEvE,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCACE,GAAG,SAAS,mBAAmB;gCAChC,MAAK;gCACL,WAAU;gCACV,aAAa,EAAE;;;;;;0CAEjB,0JAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,EAAE;;;;;;;;;;;;oBAGN,+BACC,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAE,WAAU;;gCAAwB;8CAC9B,0JAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;;oBAIzD,OAAO,gBAAgB,kBACtB,0JAAC;wBAAE,WAAU;kCAA6B,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;0BAK7E,0JAAC;0BACC,cAAA,0JAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,eAAe,EAAE,6BAA6B,EAAE;;;;;;;;;;;;;;;;;AAK3D;GA7RwB;;QACR,4JAAA,CAAA,iBAAc;QAWxB,0JAAA,CAAA,UAAO;;;KAZW", "debugId": null}}, {"offset": {"line": 2239, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Wis/VPL/KHD/cs/vpl-website/src/pages/contact.tsx"], "sourcesContent": ["import { GetStaticProps } from 'next';\nimport { serverSideTranslations } from 'next-i18next/serverSideTranslations';\nimport { useTranslation } from 'next-i18next';\nimport Head from 'next/head';\nimport Layout from '../components/layout/Layout';\nimport ContactForm from '../components/forms/ContactForm';\nimport {\n  PhoneIcon,\n  EnvelopeIcon,\n  ChatBubbleLeftRightIcon,\n  UserIcon,\n  MapPinIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Contact() {\n  const { t } = useTranslation(['contact', 'common']);\n\n  const contactMethods = [\n    {\n      icon: PhoneIcon,\n      title: '电话咨询',\n      value: '+86 ************',\n      description: '工作日 9:00-18:00'\n    },\n    {\n      icon: EnvelopeIcon,\n      title: '邮件联系',\n      value: '<EMAIL>',\n      description: '24小时内回复'\n    },\n    {\n      icon: ChatBubbleLeftRightIcon,\n      title: '微信客服',\n      value: 'VPL-Service',\n      description: '扫码添加客服微信'\n    },\n    {\n      icon: UserIcon,\n      title: 'QQ咨询',\n      value: '888999000',\n      description: 'QQ在线咨询'\n    }\n  ];\n\n  return (\n    <>\n      <Head>\n        <title>联系我们 - VPL专业网络解决方案</title>\n        <meta name=\"description\" content=\"联系VPL获取专业的B2B网络解决方案咨询，我们提供外贸网络线路、跨境电商外网线路、VPN服务等多种联系方式。\" />\n      </Head>\n      \n      <Layout>\n        {/* Hero Section */}\n        <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl\">\n                联系我们\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\">\n                专业的网络解决方案咨询服务，我们随时为您提供帮助\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Methods */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                多种联系方式\n              </h2>\n            </div>\n            \n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n              {contactMethods.map((method, index) => {\n                const IconComponent = method.icon;\n                return (\n                  <div key={index} className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                    <div className=\"mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 mb-4\">\n                      <IconComponent className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {method.title}\n                    </h3>\n                    <p className=\"text-blue-600 font-medium mb-1\">\n                      {method.value}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      {method.description}\n                    </p>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Form and Info */}\n        <section className=\"py-20 bg-gray-50\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"lg:grid lg:grid-cols-3 lg:gap-16\">\n              {/* Contact Form */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"bg-white rounded-lg shadow-sm p-8\">\n                  <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                    在线咨询表单\n                  </h2>\n                  <ContactForm />\n                </div>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"mt-12 lg:mt-0\">\n                <div className=\"bg-white rounded-lg shadow-sm p-8\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                    联系信息\n                  </h3>\n                  \n                  <div className=\"space-y-6\">\n                    <div className=\"flex items-start\">\n                      <MapPinIcon className=\"flex-shrink-0 h-6 w-6 text-blue-600 mt-1\" />\n                      <div className=\"ml-3\">\n                        <h4 className=\"text-base font-medium text-gray-900\">公司地址</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          中国广东省深圳市南山区<br />\n                          科技园南区软件产业基地\n                        </p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-start\">\n                      <ClockIcon className=\"flex-shrink-0 h-6 w-6 text-blue-600 mt-1\" />\n                      <div className=\"ml-3\">\n                        <h4 className=\"text-base font-medium text-gray-900\">服务时间</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          周一至周五：9:00 - 18:00<br />\n                          周末及节假日：10:00 - 16:00<br />\n                          紧急技术支持：7x24小时\n                        </p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-start\">\n                      <PhoneIcon className=\"flex-shrink-0 h-6 w-6 text-blue-600 mt-1\" />\n                      <div className=\"ml-3\">\n                        <h4 className=\"text-base font-medium text-gray-900\">联系电话</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          销售咨询：+86 400-xxx-xxxx<br />\n                          技术支持：+86 400-xxx-xxxx<br />\n                          投诉建议：+86 400-xxx-xxxx\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-8 p-4 bg-blue-50 rounded-lg\">\n                    <h4 className=\"text-base font-medium text-blue-900 mb-2\">\n                      为什么选择我们？\n                    </h4>\n                    <ul className=\"text-sm text-blue-800 space-y-1\">\n                      <li>• 专业的技术团队支持</li>\n                      <li>• 7x24小时服务保障</li>\n                      <li>• 定制化解决方案</li>\n                      <li>• 银行级安全保护</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-20 bg-white\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                常见问题\n              </h2>\n              <p className=\"text-lg text-gray-600\">\n                以下是客户经常询问的问题，如有其他疑问请联系我们\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-2\">\n              <div className=\"space-y-6\">\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    你们的服务覆盖哪些地区？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    我们的服务覆盖全球主要贸易区域，包括北美、欧洲、东南亚等地区，可以为您提供稳定的国际网络连接。\n                  </p>\n                </div>\n                \n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    如何保证网络连接的稳定性？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    我们采用多线路冗余设计，配备专业的监控系统，确保99.9%的服务可用性，并提供7x24小时技术支持。\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"space-y-6\">\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    你们的加密技术安全吗？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    我们采用军用级AES-256加密、RSA非对称加密、TLS协议等多重安全保障，确保您的数据传输绝对安全。\n                  </p>\n                </div>\n                \n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    如何开始使用你们的服务？\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    您可以通过填写上方的咨询表单或直接联系我们的销售团队，我们会根据您的需求提供最适合的解决方案。\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n      </Layout>\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async ({ locale }) => {\n  return {\n    props: {\n      ...(await serverSideTranslations(locale ?? 'zh', ['contact', 'common'])),\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAW;KAAS;IAElD,MAAM,iBAAiB;QACrB;YACE,MAAM,6MAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,mNAAA,CAAA,eAAY;YAClB,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yOAAA,CAAA,0BAAuB;YAC7B,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,2MAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAGnC,0JAAC,kIAAA,CAAA,UAAM;;kCAEL,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,0JAAC;wCAAE,WAAU;kDAAyD;;;;;;;;;;;;;;;;;;;;;;kCAQ5E,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAG,WAAU;kDAAwC;;;;;;;;;;;8CAKxD,0JAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ;wCAC3B,MAAM,gBAAgB,OAAO,IAAI;wCACjC,qBACE,0JAAC;4CAAgB,WAAU;;8DACzB,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAc,WAAU;;;;;;;;;;;8DAE3B,0JAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;8DAEf,0JAAC;oDAAE,WAAU;8DACV,OAAO,KAAK;;;;;;8DAEf,0JAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;2CAXb;;;;;oCAed;;;;;;;;;;;;;;;;;kCAMN,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAG,WAAU;8DAAwC;;;;;;8DAGtD,0JAAC,sIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;kDAKhB,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAIrD,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;;8EACb,0JAAC,+MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,0JAAC;oEAAI,WAAU;;sFACb,0JAAC;4EAAG,WAAU;sFAAsC;;;;;;sFACpD,0JAAC;4EAAE,WAAU;;gFAAwB;8FACxB,0JAAC;;;;;gFAAK;;;;;;;;;;;;;;;;;;;sEAMvB,0JAAC;4DAAI,WAAU;;8EACb,0JAAC,6MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,0JAAC;oEAAI,WAAU;;sFACb,0JAAC;4EAAG,WAAU;sFAAsC;;;;;;sFACpD,0JAAC;4EAAE,WAAU;;gFAAwB;8FACjB,0JAAC;;;;;gFAAK;8FACJ,0JAAC;;;;;gFAAK;;;;;;;;;;;;;;;;;;;sEAMhC,0JAAC;4DAAI,WAAU;;8EACb,0JAAC,6MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,0JAAC;oEAAI,WAAU;;sFACb,0JAAC;4EAAG,WAAU;sFAAsC;;;;;;sFACpD,0JAAC;4EAAE,WAAU;;gFAAwB;8FACd,0JAAC;;;;;gFAAK;8FACN,0JAAC;;;;;gFAAK;;;;;;;;;;;;;;;;;;;;;;;;;8DAOnC,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,0JAAC;4DAAG,WAAU;;8EACZ,0JAAC;8EAAG;;;;;;8EACJ,0JAAC;8EAAG;;;;;;8EACJ,0JAAC;8EAAG;;;;;;8EACJ,0JAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUlB,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,0JAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,0JAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAK/B,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,0JAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,0JAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAK/B,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,0JAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/C;GA1NwB;;QACR,4JAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 2940, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/contact\";\n\n/// <reference types=\"next/client\" />\n\n// inserted by rust code\ndeclare const PAGE_PATH: string\n\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\n;(window.__NEXT_P = window.__NEXT_P || []).push([\n  PAGE_PATH,\n  () => {\n    return require('PAGE')\n  },\n])\n// @ts-expect-error module.hot exists\nif (module.hot) {\n  // @ts-expect-error module.hot exists\n  module.hot.dispose(function () {\n    window.__NEXT_P.push([PAGE_PATH])\n  })\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}