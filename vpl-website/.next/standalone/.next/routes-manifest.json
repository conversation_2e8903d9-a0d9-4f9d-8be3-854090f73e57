{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "locale": false, "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/login", "regex": "^/admin/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/login(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/services/foreign-trade", "regex": "^/services/foreign\\-trade(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/foreign\\-trade(?:/)?$"}, {"page": "/services/vpn", "regex": "^/services/vpn(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/vpn(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/giw4dPljvtNVsl1SG362c/index\\.json$"}, {"page": "/contact", "dataRouteRegex": "^/_next/data/giw4dPljvtNVsl1SG362c/contact\\.json$"}, {"page": "/services", "dataRouteRegex": "^/_next/data/giw4dPljvtNVsl1SG362c/services\\.json$"}, {"page": "/services/foreign-trade", "dataRouteRegex": "^/_next/data/giw4dPljvtNVsl1SG362c/services/foreign-trade\\.json$"}, {"page": "/services/vpn", "dataRouteRegex": "^/_next/data/giw4dPljvtNVsl1SG362c/services/vpn\\.json$"}], "i18n": {"locales": ["zh", "en", "ru"], "defaultLocale": "zh", "localeDetection": false}, "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}