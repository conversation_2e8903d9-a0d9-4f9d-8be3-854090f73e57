{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "locale": false, "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [{"page": "/admin/inquiries/[id]", "regex": "^/admin/inquiries/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/inquiries/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/email-config/[id]", "regex": "^/api/admin/email\\-config/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/email\\-config/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/submissions/[id]", "regex": "^/api/admin/submissions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/submissions/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/users/[id]", "regex": "^/api/admin/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/users/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/admin/analytics", "regex": "^/admin/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/analytics(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/email-settings", "regex": "^/admin/email\\-settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/email\\-settings(?:/)?$"}, {"page": "/admin/inquiries", "regex": "^/admin/inquiries(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/inquiries(?:/)?$"}, {"page": "/admin/login", "regex": "^/admin/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/login(?:/)?$"}, {"page": "/admin/logs", "regex": "^/admin/logs(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/logs(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/services/foreign-trade", "regex": "^/services/foreign\\-trade(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/foreign\\-trade(?:/)?$"}, {"page": "/services/vpn", "regex": "^/services/vpn(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/vpn(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/zkCDd8KQiiakhxxPniyTS/index\\.json$"}, {"page": "/contact", "dataRouteRegex": "^/_next/data/zkCDd8KQiiakhxxPniyTS/contact\\.json$"}, {"page": "/services", "dataRouteRegex": "^/_next/data/zkCDd8KQiiakhxxPniyTS/services\\.json$"}, {"page": "/services/foreign-trade", "dataRouteRegex": "^/_next/data/zkCDd8KQiiakhxxPniyTS/services/foreign-trade\\.json$"}, {"page": "/services/vpn", "dataRouteRegex": "^/_next/data/zkCDd8KQiiakhxxPniyTS/services/vpn\\.json$"}], "i18n": {"locales": ["zh", "en", "ru"], "defaultLocale": "zh", "localeDetection": false}, "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}