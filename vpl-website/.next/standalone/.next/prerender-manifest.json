{"version": 4, "routes": {"/zh/services/vpn": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services/vpn.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/services/vpn": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services/vpn.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/ru/services/vpn": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services/vpn.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/zh/services": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/services": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/ru/services": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/zh": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/index.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/index.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/ru": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/index.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/zh/services/foreign-trade": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services/foreign-trade.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/services/foreign-trade": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services/foreign-trade.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/ru/services/foreign-trade": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/services/foreign-trade.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/zh/contact": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/contact.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/en/contact": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/contact.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/ru/contact": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/zkCDd8KQiiakhxxPniyTS/contact.json", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "eb9d85790678722a21b8cb31f5877c1c", "previewModeSigningKey": "0d16d00c6f3c46a313a569f44905a7bffadfd73533691121d75adbc196058cc9", "previewModeEncryptionKey": "aca3f7a8a8701ae44deb428916c1b5d2dbc2d3cccdb7dc8bb52a2f9aa053c14a"}}