"use strict";(()=>{var a={};a.id=924,a.ids=[924],a.modules={2912:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{config:()=>o,default:()=>n,handler:()=>m});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(6223),j=c(8112),k=c(8766),l=a([i]);i=(l.then?(await l)():l)[0];let n=(0,h.M)(i,"default"),o=(0,h.M)(i,"config"),p=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/socket",pathname:"/api/socket",bundlePath:"",filename:""},userland:i,distDir:".next",projectDir:""});async function m(a,b,c){let d=await p.prepare(a,b,{srcPage:"/api/socket"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,j.getTracer)(),e=d.getActiveScopeSpan(),i=p.instrumentationOnRequestError.bind(p),l=async e=>p.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:p.isDev,page:"/api/socket",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==k.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await l(e):await d.withPropagatedContext(a.headers,()=>d.trace(k.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:j.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},l))}catch(a){if(p.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}d()}catch(a){d(a)}})},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6223:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{config:()=>e.$,default:()=>g});var e=c(9983),f=a([e]);let g=(e=(f.then?(await f)():f)[0]).A;d()}catch(a){d(a)}})},6287:a=>{a.exports=import("socket.io")},9983:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{$:()=>g,A:()=>h});var e=c(6287),f=a([e]);e=(f.then?(await f)():f)[0];let g={api:{bodyParser:!1}},h=(a,b)=>{if(b.socket.server.io)console.log("Socket is already running");else{console.log("Socket is initializing");let a=new e.Server(b.socket.server);b.socket.server.io=a,a.on("connection",a=>{console.log("Client connected:",a.id),a.on("join-admin",()=>{a.join("admin"),console.log("Admin joined:",a.id)}),a.on("disconnect",()=>{console.log("Client disconnected:",a.id)})})}b.end()};d()}catch(a){d(a)}})}};var b=require("../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=2912));module.exports=c})();