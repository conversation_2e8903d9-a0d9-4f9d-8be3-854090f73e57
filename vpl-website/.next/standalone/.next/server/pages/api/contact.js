"use strict";(()=>{var a={};a.id=409,a.ids=[409],a.modules={4982:(a,b,c)=>{c.r(b),c.d(b,{config:()=>p,default:()=>o,handler:()=>r});var d={};c.r(d),c.d(d,{default:()=>k});var e=c(9046),f=c(8667),g=c(3480),h=c(6435);let i=require("nodemailer");var j=c.n(i);async function k(a,b){if("POST"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let c=a.body;for(let a of["companyName","contactPerson","phone","email","serviceType","message"])if(!c[a])return b.status(400).json({success:!1,message:`Missing required field: ${a}`});let d=j().createTransporter({host:process.env.SMTP_HOST||"smtp.gmail.com",port:parseInt(process.env.SMTP_PORT||"587"),secure:!1,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}}),e=`
      <h2>新的咨询表单提交</h2>
      <p><strong>提交时间:</strong> ${new Date().toLocaleString("zh-CN")}</p>
      
      <h3>公司信息</h3>
      <p><strong>公司名称:</strong> ${c.companyName}</p>
      <p><strong>联系人:</strong> ${c.contactPerson}</p>
      
      <h3>联系方式</h3>
      <p><strong>电话:</strong> ${c.phone}</p>
      <p><strong>邮箱:</strong> ${c.email}</p>
      ${c.wechat?`<p><strong>微信:</strong> ${c.wechat}</p>`:""}
      ${c.qq?`<p><strong>QQ:</strong> ${c.qq}</p>`:""}
      
      <h3>服务需求</h3>
      <p><strong>服务类型:</strong> ${l(c.serviceType)}</p>
      <p><strong>详细需求:</strong></p>
      <p>${c.message.replace(/\n/g,"<br>")}</p>
    `,f=`
      <h2>感谢您的咨询</h2>
      <p>尊敬的 ${c.contactPerson}，</p>
      <p>我们已收到您的咨询，以下是您提交的信息：</p>
      
      <h3>公司信息</h3>
      <p><strong>公司名称:</strong> ${c.companyName}</p>
      <p><strong>联系人:</strong> ${c.contactPerson}</p>
      
      <h3>服务需求</h3>
      <p><strong>服务类型:</strong> ${l(c.serviceType)}</p>
      <p><strong>详细需求:</strong> ${c.message}</p>
      
      <p>我们的专业团队将在24小时内与您联系，为您提供最适合的网络解决方案。</p>
      
      <p>如有紧急需求，请直接拨打我们的服务热线：+86 400-xxx-xxxx</p>
      
      <p>感谢您选择VPL！</p>
      <p>VPL团队</p>
    `;return await d.sendMail({from:process.env.SMTP_FROM||"<EMAIL>",to:process.env.ADMIN_EMAIL||"<EMAIL>",subject:`新咨询 - ${c.companyName} - ${l(c.serviceType)}`,html:e}),await d.sendMail({from:process.env.SMTP_FROM||"<EMAIL>",to:c.email,subject:"感谢您的咨询 - VPL专业网络解决方案",html:f}),console.log("Contact form submission:",{timestamp:new Date().toISOString(),company:c.companyName,email:c.email,serviceType:c.serviceType}),b.socket?.server?.io&&b.socket.server.io.to("admin").emit("new-submission",{id:Date.now().toString(),companyName:c.companyName,contactPerson:c.contactPerson,email:c.email,phone:c.phone,serviceType:c.serviceType,message:c.message,submittedAt:new Date().toISOString(),status:"pending"}),b.status(200).json({success:!0,message:"Contact form submitted successfully"})}catch(a){return console.error("Contact form submission error:",a),b.status(500).json({success:!1,message:"Internal server error"})}}function l(a){return({foreign_trade_lines:"外贸网络线路",ecommerce_lines:"跨境电商外网线路",vpn_services:"VPN服务",custom_solution:"定制解决方案"})[a]||a}var m=c(8112),n=c(8766);let o=(0,h.M)(d,"default"),p=(0,h.M)(d,"config"),q=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/contact",pathname:"/api/contact",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function r(a,b,c){let d=await q.prepare(a,b,{srcPage:"/api/contact"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,m.getTracer)(),e=d.getActiveScopeSpan(),i=q.instrumentationOnRequestError.bind(q),j=async e=>q.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:q.isDev,page:"/api/contact",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==n.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(n.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:m.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(q.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var b=require("../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=4982));module.exports=c})();