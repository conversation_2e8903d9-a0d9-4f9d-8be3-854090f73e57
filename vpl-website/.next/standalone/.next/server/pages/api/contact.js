"use strict";(()=>{var a={};a.id=409,a.ids=[409],a.modules={1553:(a,b,c)=>{c.d(b,{C:()=>l});var d=c(9021),e=c.n(d),f=c(3873),g=c.n(f);let h=g().join(process.cwd(),"data"),i=g().join(h,"submissions.json"),j=g().join(h,"users.json"),k=g().join(h,"config.json");e().existsSync(h)||e().mkdirSync(h,{recursive:!0}),e().existsSync(i)||e().writeFileSync(i,JSON.stringify([],null,2)),e().existsSync(j)||e().writeFileSync(j,JSON.stringify([],null,2)),e().existsSync(k)||e().writeFileSync(k,JSON.stringify({},null,2));class l{static async getAll(a,b){try{let c=e().readFileSync(i,"utf8"),d=JSON.parse(c);a&&(d=this.applyFilters(d,a)),b?.sortBy&&(d=this.applySorting(d,b.sortBy,b.sortOrder||"desc"));let f=d.length,g=b?.page||1,h=b?.limit||10,j=Math.ceil(f/h),k=(g-1)*h,l=d.slice(k,k+h);return{success:!0,message:"Submissions retrieved successfully",data:l,pagination:{page:g,limit:h,total:f,totalPages:j}}}catch(a){return{success:!1,message:"Failed to retrieve submissions",error:a instanceof Error?a.message:"Unknown error"}}}static async getById(a){try{let b=e().readFileSync(i,"utf8"),c=JSON.parse(b).find(b=>b.id===a);if(!c)return{success:!1,message:"Submission not found"};return{success:!0,message:"Submission retrieved successfully",data:c}}catch(a){return{success:!1,message:"Failed to retrieve submission",error:a instanceof Error?a.message:"Unknown error"}}}static async create(a){try{let b=e().readFileSync(i,"utf8"),c=JSON.parse(b),d={...a,id:Date.now().toString(),submittedAt:new Date().toISOString(),status:"pending",priority:"medium",source:"website"};return c.push(d),e().writeFileSync(i,JSON.stringify(c,null,2)),{success:!0,message:"Submission created successfully",data:d}}catch(a){return{success:!1,message:"Failed to create submission",error:a instanceof Error?a.message:"Unknown error"}}}static async update(a,b){try{let c=e().readFileSync(i,"utf8"),d=JSON.parse(c),f=d.findIndex(b=>b.id===a);if(-1===f)return{success:!1,message:"Submission not found"};return d[f]={...d[f],...b},e().writeFileSync(i,JSON.stringify(d,null,2)),{success:!0,message:"Submission updated successfully",data:d[f]}}catch(a){return{success:!1,message:"Failed to update submission",error:a instanceof Error?a.message:"Unknown error"}}}static async delete(a){try{let b=e().readFileSync(i,"utf8"),c=JSON.parse(b),d=c.filter(b=>b.id!==a);if(c.length===d.length)return{success:!1,message:"Submission not found"};return e().writeFileSync(i,JSON.stringify(d,null,2)),{success:!0,message:"Submission deleted successfully"}}catch(a){return{success:!1,message:"Failed to delete submission",error:a instanceof Error?a.message:"Unknown error"}}}static async bulkUpdate(a,b){try{let c=e().readFileSync(i,"utf8"),d=JSON.parse(c),f=[];return d.forEach(c=>{a.includes(c.id)&&(Object.assign(c,b),f.push(c))}),e().writeFileSync(i,JSON.stringify(d,null,2)),{success:!0,message:`${f.length} submissions updated successfully`,data:f}}catch(a){return{success:!1,message:"Failed to update submissions",error:a instanceof Error?a.message:"Unknown error"}}}static applyFilters(a,b){return a.filter(a=>{if(b.status&&b.status.length>0&&!b.status.includes(a.status)||b.serviceType&&b.serviceType.length>0&&!b.serviceType.includes(a.serviceType)||b.priority&&b.priority.length>0&&!b.priority.includes(a.priority))return!1;if(b.dateRange){let c=new Date(a.submittedAt),d=new Date(b.dateRange.start),e=new Date(b.dateRange.end);if(c<d||c>e)return!1}if(b.assignedTo&&a.assignedTo!==b.assignedTo)return!1;if(b.search){let c=b.search.toLowerCase();if(![a.companyName,a.contactPerson,a.email,a.phone,a.message].some(a=>a?.toLowerCase().includes(c)))return!1}return!b.tags||!(b.tags.length>0)||!!a.tags&&!!b.tags.some(b=>a.tags.includes(b))})}static applySorting(a,b,c){return a.sort((a,d)=>{let e=a[b],f=d[b];return((b.includes("At")||b.includes("Date"))&&(e=new Date(e||0).getTime(),f=new Date(f||0).getTime()),"string"==typeof e&&"string"==typeof f&&(e=e.toLowerCase(),f=f.toLowerCase()),e<f)?"asc"===c?-1:1:e>f?"asc"===c?1:-1:0})}static async getAnalytics(){try{let a=e().readFileSync(i,"utf8"),b=JSON.parse(a),c=new Date,d=new Date(c.getFullYear(),c.getMonth(),c.getDate()),f=new Date(d.getTime()-6048e5),g=new Date(d.getTime()-2592e6),h={totalSubmissions:b.length,todaySubmissions:b.filter(a=>new Date(a.submittedAt)>=d).length,weeklySubmissions:b.filter(a=>new Date(a.submittedAt)>=f).length,monthlySubmissions:b.filter(a=>new Date(a.submittedAt)>=g).length,statusDistribution:this.getStatusDistribution(b),serviceTypeDistribution:this.getServiceTypeDistribution(b),submissionTrends:this.getSubmissionTrends(b)};return{success:!0,message:"Analytics retrieved successfully",data:h}}catch(a){return{success:!1,message:"Failed to retrieve analytics",error:a instanceof Error?a.message:"Unknown error"}}}static getStatusDistribution(a){let b={};return a.forEach(a=>{b[a.status]=(b[a.status]||0)+1}),Object.entries(b).map(([b,c])=>({status:b,count:c,percentage:Math.round(c/a.length*100)}))}static getServiceTypeDistribution(a){let b={};return a.forEach(a=>{b[a.serviceType]=(b[a.serviceType]||0)+1}),Object.entries(b).map(([b,c])=>({type:b,count:c,percentage:Math.round(c/a.length*100)}))}static getSubmissionTrends(a){let b={};return Array.from({length:30},(a,b)=>{let c=new Date;return c.setDate(c.getDate()-b),c.toISOString().split("T")[0]}).reverse().forEach(a=>{b[a]=0}),a.forEach(a=>{let c=a.submittedAt.split("T")[0];b.hasOwnProperty(c)&&b[c]++}),Object.entries(b).map(([a,b])=>({date:a,count:b}))}}},3873:a=>{a.exports=require("path")},4982:(a,b,c)=>{c.r(b),c.d(b,{config:()=>q,default:()=>p,handler:()=>s});var d={};c.r(d),c.d(d,{default:()=>l});var e=c(9046),f=c(8667),g=c(3480),h=c(6435);let i=require("nodemailer");var j=c.n(i),k=c(1553);async function l(a,b){if("POST"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let c=a.body;for(let a of["companyName","contactPerson","phone","email","serviceType","message"])if(!c[a])return b.status(400).json({success:!1,message:`Missing required field: ${a}`});let d=j().createTransporter({host:process.env.SMTP_HOST||"smtp.gmail.com",port:parseInt(process.env.SMTP_PORT||"587"),secure:!1,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}}),e=`
      <h2>新的咨询表单提交</h2>
      <p><strong>提交时间:</strong> ${new Date().toLocaleString("zh-CN")}</p>
      
      <h3>公司信息</h3>
      <p><strong>公司名称:</strong> ${c.companyName}</p>
      <p><strong>联系人:</strong> ${c.contactPerson}</p>
      
      <h3>联系方式</h3>
      <p><strong>电话:</strong> ${c.phone}</p>
      <p><strong>邮箱:</strong> ${c.email}</p>
      ${c.wechat?`<p><strong>微信:</strong> ${c.wechat}</p>`:""}
      ${c.qq?`<p><strong>QQ:</strong> ${c.qq}</p>`:""}
      
      <h3>服务需求</h3>
      <p><strong>服务类型:</strong> ${m(c.serviceType)}</p>
      <p><strong>详细需求:</strong></p>
      <p>${c.message.replace(/\n/g,"<br>")}</p>
    `,f=`
      <h2>感谢您的咨询</h2>
      <p>尊敬的 ${c.contactPerson}，</p>
      <p>我们已收到您的咨询，以下是您提交的信息：</p>
      
      <h3>公司信息</h3>
      <p><strong>公司名称:</strong> ${c.companyName}</p>
      <p><strong>联系人:</strong> ${c.contactPerson}</p>
      
      <h3>服务需求</h3>
      <p><strong>服务类型:</strong> ${m(c.serviceType)}</p>
      <p><strong>详细需求:</strong> ${c.message}</p>
      
      <p>我们的专业团队将在24小时内与您联系，为您提供最适合的网络解决方案。</p>
      
      <p>如有紧急需求，请直接拨打我们的服务热线：+86 400-xxx-xxxx</p>
      
      <p>感谢您选择VPL！</p>
      <p>VPL团队</p>
    `;await d.sendMail({from:process.env.SMTP_FROM||"<EMAIL>",to:process.env.ADMIN_EMAIL||"<EMAIL>",subject:`新咨询 - ${c.companyName} - ${m(c.serviceType)}`,html:e}),await d.sendMail({from:process.env.SMTP_FROM||"<EMAIL>",to:c.email,subject:"感谢您的咨询 - VPL专业网络解决方案",html:f});let g=await k.C.create({companyName:c.companyName,contactPerson:c.contactPerson,phone:c.phone,email:c.email,wechat:c.wechat,qq:c.qq,serviceType:c.serviceType,message:c.message,priority:"medium",source:"website"});return console.log("Contact form submission:",{timestamp:new Date().toISOString(),company:c.companyName,email:c.email,serviceType:c.serviceType,stored:g.success}),b.socket?.server?.io&&g.success&&b.socket.server.io.to("admin").emit("new-submission",g.data),b.status(200).json({success:!0,message:"Contact form submitted successfully"})}catch(a){return console.error("Contact form submission error:",a),b.status(500).json({success:!1,message:"Internal server error"})}}function m(a){return({foreign_trade_lines:"外贸网络线路",ecommerce_lines:"跨境电商外网线路",vpn_services:"VPN服务",custom_solution:"定制解决方案"})[a]||a}var n=c(8112),o=c(8766);let p=(0,h.M)(d,"default"),q=(0,h.M)(d,"config"),r=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/contact",pathname:"/api/contact",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function s(a,b,c){let d=await r.prepare(a,b,{srcPage:"/api/contact"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,n.getTracer)(),e=d.getActiveScopeSpan(),i=r.instrumentationOnRequestError.bind(r),j=async e=>r.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:r.isDev,page:"/api/contact",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==o.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(o.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(r.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9021:a=>{a.exports=require("fs")}};var b=require("../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=4982));module.exports=c})();