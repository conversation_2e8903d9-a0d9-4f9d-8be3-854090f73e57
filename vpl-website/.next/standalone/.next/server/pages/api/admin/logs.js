"use strict";(()=>{var a={};a.id=390,a.ids=[390],a.modules={829:a=>{a.exports=require("jsonwebtoken")},3873:a=>{a.exports=require("path")},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9021:a=>{a.exports=require("fs")},9802:(a,b,c)=>{c.r(b),c.d(b,{config:()=>v,default:()=>u,handler:()=>x});var d={};c.r(d),c.d(d,{addLogEntry:()=>r,default:()=>q});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(829),j=c.n(i),k=c(9021),l=c.n(k),m=c(3873),n=c.n(m);let o=n().join(process.cwd(),"data"),p=n().join(o,"system-logs.json");if(l().existsSync(o)||l().mkdirSync(o,{recursive:!0}),!l().existsSync(p)){let a=[{id:"1",timestamp:new Date().toISOString(),level:"info",category:"system",message:"系统启动成功",details:{version:"1.0.0",environment:"production"}},{id:"2",timestamp:new Date(Date.now()-6e4).toISOString(),level:"info",category:"auth",message:"管理员登录成功",details:{username:"admin"},ipAddress:"127.0.0.1"},{id:"3",timestamp:new Date(Date.now()-12e4).toISOString(),level:"warning",category:"email",message:"SMTP连接超时，正在重试",details:{host:"smtp.example.com",port:587}}];l().writeFileSync(p,JSON.stringify(a,null,2))}async function q(a,b){let c=a.headers.authorization;if(!c||!c.startsWith("Bearer "))return b.status(401).json({success:!1,message:"No token provided"});let d=c.substring(7);try{j().verify(d,process.env.JWT_SECRET||"your-secret-key")}catch(a){return b.status(401).json({success:!1,message:"Invalid token"})}if("GET"===a.method)try{let c=l().readFileSync(p,"utf8"),d=JSON.parse(c);d.sort((a,b)=>new Date(b.timestamp).getTime()-new Date(a.timestamp).getTime());let e=parseInt(a.query.page)||1,f=parseInt(a.query.limit)||50,g=(e-1)*f,h=d.slice(g,g+f);return b.status(200).json({success:!0,message:"System logs retrieved successfully",data:h,pagination:{page:e,limit:f,total:d.length,totalPages:Math.ceil(d.length/f)}})}catch(a){return b.status(500).json({success:!1,message:"Failed to retrieve system logs",error:a instanceof Error?a.message:"Unknown error"})}if("POST"===a.method)try{let c=l().readFileSync(p,"utf8"),d=JSON.parse(c),e={id:Date.now().toString(),timestamp:new Date().toISOString(),...a.body};return d.push(e),d.length>1e4&&d.splice(0,d.length-1e4),l().writeFileSync(p,JSON.stringify(d,null,2)),b.status(201).json({success:!0,message:"Log entry created successfully",data:e})}catch(a){return b.status(500).json({success:!1,message:"Failed to create log entry",error:a instanceof Error?a.message:"Unknown error"})}if("DELETE"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{l().writeFileSync(p,JSON.stringify([],null,2));let a={id:Date.now().toString(),timestamp:new Date().toISOString(),level:"info",category:"system",message:"系统日志已清空",details:{clearedBy:"admin"}};return l().writeFileSync(p,JSON.stringify([a],null,2)),b.status(200).json({success:!0,message:"All logs cleared successfully"})}catch(a){return b.status(500).json({success:!1,message:"Failed to clear logs",error:a instanceof Error?a.message:"Unknown error"})}}let r=(a,b,c,d,e,f)=>{try{let g=l().readFileSync(p,"utf8"),h=JSON.parse(g),i={id:Date.now().toString(),timestamp:new Date().toISOString(),level:a,category:b,message:c,details:d,userId:e,ipAddress:f};h.push(i),h.length>1e4&&h.splice(0,h.length-1e4),l().writeFileSync(p,JSON.stringify(h,null,2))}catch(a){console.error("Failed to add log entry:",a)}};var s=c(8112),t=c(8766);let u=(0,h.M)(d,"default"),v=(0,h.M)(d,"config"),w=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/logs",pathname:"/api/admin/logs",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function x(a,b,c){let d=await w.prepare(a,b,{srcPage:"/api/admin/logs"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,s.getTracer)(),e=d.getActiveScopeSpan(),i=w.instrumentationOnRequestError.bind(w),j=async e=>w.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:w.isDev,page:"/api/admin/logs",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==t.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(t.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(w.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}}};var b=require("../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=9802));module.exports=c})();