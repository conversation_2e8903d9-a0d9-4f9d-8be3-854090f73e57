"use strict";(()=>{var a={};a.id=650,a.ids=[650],a.modules={829:a=>{a.exports=require("jsonwebtoken")},3139:a=>{a.exports=import("bcryptjs")},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6715:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>i});var e=c(3139),f=c(829),g=c.n(f),h=a([e]);e=(h.then?(await h)():h)[0];let j={username:process.env.ADMIN_USERNAME||"admin",passwordHash:process.env.ADMIN_PASSWORD_HASH||e.default.hashSync(process.env.ADMIN_PASSWORD||"admin123",10)};async function i(a,b){if("POST"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let{username:c,password:d}=a.body;if(!c||!d)return b.status(400).json({success:!1,message:"Username and password are required"});if(c!==j.username||!await e.default.compare(d,j.passwordHash))return b.status(401).json({success:!1,message:"Invalid credentials"});let f=g().sign({username:c,role:"admin",iat:Math.floor(Date.now()/1e3)},process.env.JWT_SECRET||"your-secret-key",{expiresIn:"24h"});return console.log("Admin login successful:",{username:c,timestamp:new Date().toISOString(),ip:a.headers["x-forwarded-for"]||a.connection.remoteAddress}),b.status(200).json({success:!0,message:"Login successful",token:f})}catch(a){return console.error("Admin login error:",a),b.status(500).json({success:!1,message:"Internal server error"})}}d()}catch(a){d(a)}})},8074:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{config:()=>o,default:()=>n,handler:()=>m});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(6715),j=c(8112),k=c(8766),l=a([i]);i=(l.then?(await l)():l)[0];let n=(0,h.M)(i,"default"),o=(0,h.M)(i,"config"),p=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/login",pathname:"/api/admin/login",bundlePath:"",filename:""},userland:i,distDir:".next",projectDir:""});async function m(a,b,c){let d=await p.prepare(a,b,{srcPage:"/api/admin/login"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,j.getTracer)(),e=d.getActiveScopeSpan(),i=p.instrumentationOnRequestError.bind(p),l=async e=>p.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:p.isDev,page:"/api/admin/login",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==k.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await l(e):await d.withPropagatedContext(a.headers,()=>d.trace(k.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:j.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},l))}catch(a){if(p.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}d()}catch(a){d(a)}})}};var b=require("../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=8074));module.exports=c})();