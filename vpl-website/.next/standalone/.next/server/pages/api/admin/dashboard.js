"use strict";(()=>{var a={};a.id=821,a.ids=[821],a.modules={829:a=>{a.exports=require("jsonwebtoken")},1489:(a,b,c)=>{c.r(b),c.d(b,{config:()=>p,default:()=>o,handler:()=>r});var d={};c.r(d),c.d(d,{default:()=>l});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(829),j=c.n(i);let k=[{id:"1",companyName:"深圳贸易有限公司",contactPerson:"张经理",email:"<EMAIL>",phone:"+86 138-0000-0000",serviceType:"foreign_trade_lines",message:"我们需要稳定的外贸网络线路，主要连接欧美市场，请提供详细方案和报价。",submittedAt:new Date().toISOString(),status:"pending"},{id:"2",companyName:"广州电商科技",contactPerson:"李总",email:"<EMAIL>",phone:"+86 139-0000-0000",serviceType:"ecommerce_lines",message:"跨境电商平台需要优化网络连接，提升用户访问速度。",submittedAt:new Date(Date.now()-864e5).toISOString(),status:"contacted"},{id:"3",companyName:"北京科技集团",contactPerson:"王主管",email:"<EMAIL>",phone:"+86 137-0000-0000",serviceType:"vpn_services",message:"企业需要安全的VPN服务，支持多设备接入。",submittedAt:new Date(Date.now()-1728e5).toISOString(),status:"closed"}];async function l(a,b){if("GET"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let c=a.headers.authorization;if(!c||!c.startsWith("Bearer "))return b.status(401).json({success:!1,message:"No token provided"});let d=c.substring(7);try{j().verify(d,process.env.JWT_SECRET||"your-secret-key")}catch(a){return b.status(401).json({success:!1,message:"Invalid token"})}let e=new Date;e.setHours(0,0,0,0);let f=k.filter(a=>new Date(a.submittedAt)>=e),g=k.filter(a=>"pending"===a.status),h={totalInquiries:k.length,todayInquiries:f.length,pendingInquiries:g.length,totalUsers:156};return b.status(200).json({success:!0,message:"Dashboard data retrieved successfully",stats:h,recentSubmissions:k.slice(0,10)})}catch(a){return console.error("Dashboard data error:",a),b.status(500).json({success:!1,message:"Internal server error"})}}var m=c(8112),n=c(8766);let o=(0,h.M)(d,"default"),p=(0,h.M)(d,"config"),q=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/dashboard",pathname:"/api/admin/dashboard",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function r(a,b,c){let d=await q.prepare(a,b,{srcPage:"/api/admin/dashboard"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,m.getTracer)(),e=d.getActiveScopeSpan(),i=q.instrumentationOnRequestError.bind(q),j=async e=>q.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:q.isDev,page:"/api/admin/dashboard",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==n.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(n.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:m.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(q.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var b=require("../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=1489));module.exports=c})();