"use strict";(()=>{var a={};a.id=940,a.ids=[940],a.modules={829:a=>{a.exports=require("jsonwebtoken")},1553:(a,b,c)=>{c.d(b,{C:()=>l});var d=c(9021),e=c.n(d),f=c(3873),g=c.n(f);let h=g().join(process.cwd(),"data"),i=g().join(h,"submissions.json"),j=g().join(h,"users.json"),k=g().join(h,"config.json");e().existsSync(h)||e().mkdirSync(h,{recursive:!0}),e().existsSync(i)||e().writeFileSync(i,JSON.stringify([],null,2)),e().existsSync(j)||e().writeFileSync(j,JSON.stringify([],null,2)),e().existsSync(k)||e().writeFileSync(k,JSON.stringify({},null,2));class l{static async getAll(a,b){try{let c=e().readFileSync(i,"utf8"),d=JSON.parse(c);a&&(d=this.applyFilters(d,a)),b?.sortBy&&(d=this.applySorting(d,b.sortBy,b.sortOrder||"desc"));let f=d.length,g=b?.page||1,h=b?.limit||10,j=Math.ceil(f/h),k=(g-1)*h,l=d.slice(k,k+h);return{success:!0,message:"Submissions retrieved successfully",data:l,pagination:{page:g,limit:h,total:f,totalPages:j}}}catch(a){return{success:!1,message:"Failed to retrieve submissions",error:a instanceof Error?a.message:"Unknown error"}}}static async getById(a){try{let b=e().readFileSync(i,"utf8"),c=JSON.parse(b).find(b=>b.id===a);if(!c)return{success:!1,message:"Submission not found"};return{success:!0,message:"Submission retrieved successfully",data:c}}catch(a){return{success:!1,message:"Failed to retrieve submission",error:a instanceof Error?a.message:"Unknown error"}}}static async create(a){try{let b=e().readFileSync(i,"utf8"),c=JSON.parse(b),d={...a,id:Date.now().toString(),submittedAt:new Date().toISOString(),status:"pending",priority:"medium",source:"website"};return c.push(d),e().writeFileSync(i,JSON.stringify(c,null,2)),{success:!0,message:"Submission created successfully",data:d}}catch(a){return{success:!1,message:"Failed to create submission",error:a instanceof Error?a.message:"Unknown error"}}}static async update(a,b){try{let c=e().readFileSync(i,"utf8"),d=JSON.parse(c),f=d.findIndex(b=>b.id===a);if(-1===f)return{success:!1,message:"Submission not found"};return d[f]={...d[f],...b},e().writeFileSync(i,JSON.stringify(d,null,2)),{success:!0,message:"Submission updated successfully",data:d[f]}}catch(a){return{success:!1,message:"Failed to update submission",error:a instanceof Error?a.message:"Unknown error"}}}static async delete(a){try{let b=e().readFileSync(i,"utf8"),c=JSON.parse(b),d=c.filter(b=>b.id!==a);if(c.length===d.length)return{success:!1,message:"Submission not found"};return e().writeFileSync(i,JSON.stringify(d,null,2)),{success:!0,message:"Submission deleted successfully"}}catch(a){return{success:!1,message:"Failed to delete submission",error:a instanceof Error?a.message:"Unknown error"}}}static async bulkUpdate(a,b){try{let c=e().readFileSync(i,"utf8"),d=JSON.parse(c),f=[];return d.forEach(c=>{a.includes(c.id)&&(Object.assign(c,b),f.push(c))}),e().writeFileSync(i,JSON.stringify(d,null,2)),{success:!0,message:`${f.length} submissions updated successfully`,data:f}}catch(a){return{success:!1,message:"Failed to update submissions",error:a instanceof Error?a.message:"Unknown error"}}}static applyFilters(a,b){return a.filter(a=>{if(b.status&&b.status.length>0&&!b.status.includes(a.status)||b.serviceType&&b.serviceType.length>0&&!b.serviceType.includes(a.serviceType)||b.priority&&b.priority.length>0&&!b.priority.includes(a.priority))return!1;if(b.dateRange){let c=new Date(a.submittedAt),d=new Date(b.dateRange.start),e=new Date(b.dateRange.end);if(c<d||c>e)return!1}if(b.assignedTo&&a.assignedTo!==b.assignedTo)return!1;if(b.search){let c=b.search.toLowerCase();if(![a.companyName,a.contactPerson,a.email,a.phone,a.message].some(a=>a?.toLowerCase().includes(c)))return!1}return!b.tags||!(b.tags.length>0)||!!a.tags&&!!b.tags.some(b=>a.tags.includes(b))})}static applySorting(a,b,c){return a.sort((a,d)=>{let e=a[b],f=d[b];return((b.includes("At")||b.includes("Date"))&&(e=new Date(e||0).getTime(),f=new Date(f||0).getTime()),"string"==typeof e&&"string"==typeof f&&(e=e.toLowerCase(),f=f.toLowerCase()),e<f)?"asc"===c?-1:1:e>f?"asc"===c?1:-1:0})}static async getAnalytics(){try{let a=e().readFileSync(i,"utf8"),b=JSON.parse(a),c=new Date,d=new Date(c.getFullYear(),c.getMonth(),c.getDate()),f=new Date(d.getTime()-6048e5),g=new Date(d.getTime()-2592e6),h={totalSubmissions:b.length,todaySubmissions:b.filter(a=>new Date(a.submittedAt)>=d).length,weeklySubmissions:b.filter(a=>new Date(a.submittedAt)>=f).length,monthlySubmissions:b.filter(a=>new Date(a.submittedAt)>=g).length,statusDistribution:this.getStatusDistribution(b),serviceTypeDistribution:this.getServiceTypeDistribution(b),submissionTrends:this.getSubmissionTrends(b)};return{success:!0,message:"Analytics retrieved successfully",data:h}}catch(a){return{success:!1,message:"Failed to retrieve analytics",error:a instanceof Error?a.message:"Unknown error"}}}static getStatusDistribution(a){let b={};return a.forEach(a=>{b[a.status]=(b[a.status]||0)+1}),Object.entries(b).map(([b,c])=>({status:b,count:c,percentage:Math.round(c/a.length*100)}))}static getServiceTypeDistribution(a){let b={};return a.forEach(a=>{b[a.serviceType]=(b[a.serviceType]||0)+1}),Object.entries(b).map(([b,c])=>({type:b,count:c,percentage:Math.round(c/a.length*100)}))}static getSubmissionTrends(a){let b={};return Array.from({length:30},(a,b)=>{let c=new Date;return c.setDate(c.getDate()-b),c.toISOString().split("T")[0]}).reverse().forEach(a=>{b[a]=0}),a.forEach(a=>{let c=a.submittedAt.split("T")[0];b.hasOwnProperty(c)&&b[c]++}),Object.entries(b).map(([a,b])=>({date:a,count:b}))}}},3765:(a,b,c)=>{c.r(b),c.d(b,{config:()=>p,default:()=>o,handler:()=>r});var d={};c.r(d),c.d(d,{default:()=>l});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(829),j=c.n(i),k=c(1553);async function l(a,b){let c=a.headers.authorization;if(!c||!c.startsWith("Bearer "))return b.status(401).json({success:!1,message:"No token provided"});let d=c.substring(7);try{j().verify(d,process.env.JWT_SECRET||"your-secret-key")}catch(a){return b.status(401).json({success:!1,message:"Invalid token"})}let{id:e}=a.query;if(!e||"string"!=typeof e)return b.status(400).json({success:!1,message:"Invalid submission ID"});if("GET"===a.method)try{let a=await k.C.getById(e);return b.status(a.success?200:404).json(a)}catch(a){return b.status(500).json({success:!1,message:"Failed to retrieve submission",error:a instanceof Error?a.message:"Unknown error"})}if("PUT"===a.method)try{let c=await k.C.update(e,a.body);return b.status(c.success?200:404).json(c)}catch(a){return b.status(500).json({success:!1,message:"Failed to update submission",error:a instanceof Error?a.message:"Unknown error"})}if("DELETE"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let a=await k.C.delete(e);return b.status(a.success?200:404).json(a)}catch(a){return b.status(500).json({success:!1,message:"Failed to delete submission",error:a instanceof Error?a.message:"Unknown error"})}}var m=c(8112),n=c(8766);let o=(0,h.M)(d,"default"),p=(0,h.M)(d,"config"),q=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/submissions/[id]",pathname:"/api/admin/submissions/[id]",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function r(a,b,c){let d=await q.prepare(a,b,{srcPage:"/api/admin/submissions/[id]"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,m.getTracer)(),e=d.getActiveScopeSpan(),i=q.instrumentationOnRequestError.bind(q),j=async e=>q.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:q.isDev,page:"/api/admin/submissions/[id]",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==n.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(n.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:m.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(q.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}},3873:a=>{a.exports=require("path")},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9021:a=>{a.exports=require("fs")}};var b=require("../../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=3765));module.exports=c})();