"use strict";(()=>{var a={};a.id=201,a.ids=[201],a.modules={829:a=>{a.exports=require("jsonwebtoken")},3139:a=>{a.exports=import("bcryptjs")},3873:a=>{a.exports=require("path")},4146:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>m});var e=c(829),f=c.n(e),g=c(3139),h=c(9021),i=c.n(h),j=c(3873),k=c.n(j),l=a([g]);g=(l.then?(await l)():l)[0];let n=k().join(process.cwd(),"data"),o=k().join(n,"admin-users.json");async function m(a,b){let c=a.headers.authorization;if(!c||!c.startsWith("Bearer "))return b.status(401).json({success:!1,message:"No token provided"});let d=c.substring(7);try{f().verify(d,process.env.JWT_SECRET||"your-secret-key")}catch(a){return b.status(401).json({success:!1,message:"Invalid token"})}let{id:e}=a.query;if(!e||"string"!=typeof e)return b.status(400).json({success:!1,message:"Invalid user ID"});if("PUT"===a.method)try{let c=i().readFileSync(o,"utf8"),d=JSON.parse(c),f=d.findIndex(a=>a.id===e);if(-1===f)return b.status(404).json({success:!1,message:"User not found"});let{username:h,email:j,fullName:k,role:l,password:m,isActive:n}=a.body;if(d.find((a,b)=>b!==f&&(a.username===h||a.email===j)))return b.status(400).json({success:!1,message:"用户名或邮箱已存在"});let p={...d[f],username:h||d[f].username,email:j||d[f].email,fullName:k||d[f].fullName,role:l||d[f].role,isActive:void 0!==n?n:d[f].isActive,updatedAt:new Date().toISOString()};m&&""!==m.trim()&&(p.password=g.default.hashSync(m,10)),d[f]=p,i().writeFileSync(o,JSON.stringify(d,null,2));let{password:q,...r}=p;return b.status(200).json({success:!0,message:"User updated successfully",data:r})}catch(a){return b.status(500).json({success:!1,message:"Failed to update user",error:a instanceof Error?a.message:"Unknown error"})}if("DELETE"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let a=i().readFileSync(o,"utf8"),c=JSON.parse(a),d=c.find(a=>a.id===e);if(!d)return b.status(404).json({success:!1,message:"User not found"});let f=c.filter(a=>"super_admin"===a.role&&a.isActive);if("super_admin"===d.role&&1===f.length)return b.status(400).json({success:!1,message:"不能删除最后一个超级管理员"});let g=c.filter(a=>a.id!==e);return i().writeFileSync(o,JSON.stringify(g,null,2)),b.status(200).json({success:!0,message:"User deleted successfully"})}catch(a){return b.status(500).json({success:!1,message:"Failed to delete user",error:a instanceof Error?a.message:"Unknown error"})}}d()}catch(a){d(a)}})},4204:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{config:()=>o,default:()=>n,handler:()=>m});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(4146),j=c(8112),k=c(8766),l=a([i]);i=(l.then?(await l)():l)[0];let n=(0,h.M)(i,"default"),o=(0,h.M)(i,"config"),p=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/users/[id]",pathname:"/api/admin/users/[id]",bundlePath:"",filename:""},userland:i,distDir:".next",projectDir:""});async function m(a,b,c){let d=await p.prepare(a,b,{srcPage:"/api/admin/users/[id]"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,j.getTracer)(),e=d.getActiveScopeSpan(),i=p.instrumentationOnRequestError.bind(p),l=async e=>p.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:p.isDev,page:"/api/admin/users/[id]",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==k.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await l(e):await d.withPropagatedContext(a.headers,()=>d.trace(k.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:j.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},l))}catch(a){if(p.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}d()}catch(a){d(a)}})},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9021:a=>{a.exports=require("fs")}};var b=require("../../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=4204));module.exports=c})();