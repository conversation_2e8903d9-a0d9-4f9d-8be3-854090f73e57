"use strict";(()=>{var a={};a.id=676,a.ids=[676],a.modules={829:a=>{a.exports=require("jsonwebtoken")},2670:(a,b,c)=>{c.r(b),c.d(b,{config:()=>u,default:()=>t,handler:()=>w});var d={};c.r(d),c.d(d,{default:()=>q});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(829),j=c.n(i),k=c(9021),l=c.n(k),m=c(3873),n=c.n(m);let o=n().join(process.cwd(),"data"),p=n().join(o,"email-config.json");async function q(a,b){let c=a.headers.authorization;if(!c||!c.startsWith("Bearer "))return b.status(401).json({success:!1,message:"No token provided"});let d=c.substring(7);try{j().verify(d,process.env.JWT_SECRET||"your-secret-key")}catch(a){return b.status(401).json({success:!1,message:"Invalid token"})}if("GET"===a.method)try{let a=l().readFileSync(p,"utf8"),c=JSON.parse(a);return b.status(200).json({success:!0,message:"Email configurations retrieved successfully",data:c})}catch(a){return b.status(500).json({success:!1,message:"Failed to retrieve email configurations",error:a instanceof Error?a.message:"Unknown error"})}if("POST"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let c=l().readFileSync(p,"utf8"),d=JSON.parse(c),e={id:Date.now().toString(),...a.body,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.isDefault&&d.forEach(a=>{a.isDefault=!1}),d.push(e),l().writeFileSync(p,JSON.stringify(d,null,2)),b.status(201).json({success:!0,message:"Email configuration created successfully",data:e})}catch(a){return b.status(500).json({success:!1,message:"Failed to create email configuration",error:a instanceof Error?a.message:"Unknown error"})}}l().existsSync(o)||l().mkdirSync(o,{recursive:!0}),l().existsSync(p)||l().writeFileSync(p,JSON.stringify([],null,2));var r=c(8112),s=c(8766);let t=(0,h.M)(d,"default"),u=(0,h.M)(d,"config"),v=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/email-config",pathname:"/api/admin/email-config",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function w(a,b,c){let d=await v.prepare(a,b,{srcPage:"/api/admin/email-config"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,r.getTracer)(),e=d.getActiveScopeSpan(),i=v.instrumentationOnRequestError.bind(v),j=async e=>v.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:v.isDev,page:"/api/admin/email-config",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==s.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(s.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:r.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(v.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}},3873:a=>{a.exports=require("path")},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9021:a=>{a.exports=require("fs")}};var b=require("../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=2670));module.exports=c})();