"use strict";(()=>{var a={};a.id=676,a.ids=[676],a.modules={829:a=>{a.exports=require("jsonwebtoken")},4457:(a,b,c)=>{c.r(b),c.d(b,{config:()=>t,default:()=>s,handler:()=>v});var d={};c.r(d),c.d(d,{default:()=>p});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(829),j=c.n(i);let k=require("fs");var l=c.n(k);let m=require("path"),n=c.n(m)().join(process.cwd(),"email-config.json"),o=()=>{try{if(l().existsSync(n)){let a=l().readFileSync(n,"utf8");return JSON.parse(a)}}catch(a){console.error("Error reading email config:",a)}return null};async function p(a,b){let c=a.headers.authorization;if(!c||!c.startsWith("Bearer "))return b.status(401).json({success:!1,message:"No token provided"});let d=c.substring(7);try{j().verify(d,process.env.JWT_SECRET||"your-secret-key")}catch(a){return b.status(401).json({success:!1,message:"Invalid token"})}if("GET"===a.method){let a=o();if(!a)return b.status(200).json({success:!0,message:"No email configuration found",config:{smtpHost:process.env.SMTP_HOST||"",smtpPort:parseInt(process.env.SMTP_PORT||"587"),smtpUser:process.env.SMTP_USER||"",smtpFrom:process.env.SMTP_FROM||"",adminEmail:process.env.ADMIN_EMAIL||""}});{let c={smtpHost:a.smtpHost,smtpPort:a.smtpPort,smtpUser:a.smtpUser,smtpFrom:a.smtpFrom,adminEmail:a.adminEmail};return b.status(200).json({success:!0,message:"Email configuration retrieved",config:c})}}if("POST"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});{let{smtpHost:c,smtpPort:d,smtpUser:e,smtpPass:f,smtpFrom:g,adminEmail:h}=a.body;if(!c||!d||!e||!g||!h)return b.status(400).json({success:!1,message:"Missing required fields"});let i={smtpHost:c,smtpPort:parseInt(d),smtpUser:e,smtpPass:f||"",smtpFrom:g,adminEmail:h};if(!f){let a=o();a&&a.smtpPass&&(i.smtpPass=a.smtpPass)}return(a=>{try{return l().writeFileSync(n,JSON.stringify(a,null,2)),!0}catch(a){return console.error("Error writing email config:",a),!1}})(i)?b.status(200).json({success:!0,message:"Email configuration updated successfully"}):b.status(500).json({success:!1,message:"Failed to save email configuration"})}}var q=c(8112),r=c(8766);let s=(0,h.M)(d,"default"),t=(0,h.M)(d,"config"),u=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/email-config",pathname:"/api/admin/email-config",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function v(a,b,c){let d=await u.prepare(a,b,{srcPage:"/api/admin/email-config"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,q.getTracer)(),e=d.getActiveScopeSpan(),i=u.instrumentationOnRequestError.bind(u),j=async e=>u.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:u.isDev,page:"/api/admin/email-config",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==r.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(r.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:q.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(u.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var b=require("../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=4457));module.exports=c})();