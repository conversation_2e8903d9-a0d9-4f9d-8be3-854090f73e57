"use strict";(()=>{var a={};a.id=438,a.ids=[438],a.modules={829:a=>{a.exports=require("jsonwebtoken")},3873:a=>{a.exports=require("path")},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9021:a=>{a.exports=require("fs")},9279:(a,b,c)=>{c.r(b),c.d(b,{config:()=>u,default:()=>t,handler:()=>w});var d={};c.r(d),c.d(d,{default:()=>q});var e=c(9046),f=c(8667),g=c(3480),h=c(6435),i=c(829),j=c.n(i),k=c(9021),l=c.n(k),m=c(3873),n=c.n(m);let o=n().join(process.cwd(),"data"),p=n().join(o,"email-config.json");async function q(a,b){let c=a.headers.authorization;if(!c||!c.startsWith("Bearer "))return b.status(401).json({success:!1,message:"No token provided"});let d=c.substring(7);try{j().verify(d,process.env.JWT_SECRET||"your-secret-key")}catch(a){return b.status(401).json({success:!1,message:"Invalid token"})}let{id:e}=a.query;if(!e||"string"!=typeof e)return b.status(400).json({success:!1,message:"Invalid configuration ID"});if("PUT"===a.method)try{let c=l().readFileSync(p,"utf8"),d=JSON.parse(c),f=d.findIndex(a=>a.id===e);if(-1===f)return b.status(404).json({success:!1,message:"Email configuration not found"});return a.body.isDefault&&d.forEach(a=>{a.isDefault=!1}),d[f]={...d[f],...a.body,updatedAt:new Date().toISOString()},l().writeFileSync(p,JSON.stringify(d,null,2)),b.status(200).json({success:!0,message:"Email configuration updated successfully",data:d[f]})}catch(a){return b.status(500).json({success:!1,message:"Failed to update email configuration",error:a instanceof Error?a.message:"Unknown error"})}if("DELETE"!==a.method)return b.status(405).json({success:!1,message:"Method not allowed"});try{let a=l().readFileSync(p,"utf8"),c=JSON.parse(a),d=c.filter(a=>a.id!==e);if(c.length===d.length)return b.status(404).json({success:!1,message:"Email configuration not found"});return l().writeFileSync(p,JSON.stringify(d,null,2)),b.status(200).json({success:!0,message:"Email configuration deleted successfully"})}catch(a){return b.status(500).json({success:!1,message:"Failed to delete email configuration",error:a instanceof Error?a.message:"Unknown error"})}}var r=c(8112),s=c(8766);let t=(0,h.M)(d,"default"),u=(0,h.M)(d,"config"),v=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/admin/email-config/[id]",pathname:"/api/admin/email-config/[id]",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function w(a,b,c){let d=await v.prepare(a,b,{srcPage:"/api/admin/email-config/[id]"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,r.getTracer)(),e=d.getActiveScopeSpan(),i=v.instrumentationOnRequestError.bind(v),j=async e=>v.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:v.isDev,page:"/api/admin/email-config/[id]",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==s.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await j(e):await d.withPropagatedContext(a.headers,()=>d.trace(s.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:r.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},j))}catch(a){if(v.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}}};var b=require("../../../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=9279));module.exports=c})();