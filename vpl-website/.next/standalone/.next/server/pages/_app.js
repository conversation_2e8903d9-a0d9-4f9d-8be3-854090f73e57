(()=>{var a={};a.id=636,a.ids=[636],a.modules={866:a=>{a.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},979:()=>{},2081:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(8732),e=c(8751),f=c(866),g=c.n(f),h=c(8534),i=c.n(h);c(979);let j=(0,e.appWithTranslation)(function({Component:a,pageProps:b}){return(0,d.jsx)("div",{className:`${g().variable} ${i().variable} antialiased`,children:(0,d.jsx)(a,{...b})})})},8534:a=>{a.exports={style:{fontFamily:"'Geist Mono', '<PERSON>eist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},8732:a=>{"use strict";a.exports=require("react/jsx-runtime")},8751:a=>{"use strict";a.exports=require("next-i18next")}};var b=require("../webpack-runtime.js");b.C(a);var c=b(b.s=2081);module.exports=c})();