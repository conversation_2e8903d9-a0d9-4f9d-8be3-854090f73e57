"use strict";(()=>{var a={};a.id=965,a.ids=[965],a.modules={156:a=>{a.exports=require("next/dist/shared/lib/utils")},361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1322:a=>{a.exports=require("next/dist/shared/lib/router/utils/format-url")},1738:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},2015:a=>{a.exports=require("react")},2326:a=>{a.exports=require("react-dom")},3103:a=>{a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},3873:a=>{a.exports=require("path")},4075:a=>{a.exports=require("zlib")},5576:a=>{a.exports=require("next-i18next/serverSideTranslations")},5952:a=>{a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},6439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7909:a=>{a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},7910:a=>{a.exports=require("stream")},7990:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},8732:a=>{a.exports=require("react/jsx-runtime")},8751:a=>{a.exports=require("next-i18next")},9021:a=>{a.exports=require("fs")},9982:(a,b,c)=>{c.r(b),c.d(b,{config:()=>X,default:()=>T,getServerSideProps:()=>W,getStaticPaths:()=>V,getStaticProps:()=>U,handler:()=>ad,reportWebVitals:()=>Y,routeModule:()=>ac,unstable_getServerProps:()=>aa,unstable_getServerSideProps:()=>ab,unstable_getStaticParams:()=>_,unstable_getStaticPaths:()=>$,unstable_getStaticProps:()=>Z});var d={};c.r(d),c.d(d,{default:()=>F,getStaticProps:()=>G});var e=c(3885),f=c(237),g=c(772),h=c(2410),i=c(1322),j=c(5124),k=c(8647),l=c(3709),m=c(7909),n=c(5122),o=c(1413),p=c(1779),q=c(2081),r=c(8732),s=c(5576),t=c(8751),u=c(9788),v=c.n(u),w=c(9918),x=c.n(w),y=c(897),z=c(5559),A=c(7990),B=c(2015);let C=B.forwardRef(function({title:a,titleId:b,...c},d){return B.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?B.createElement("title",{id:b},a):null,B.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.288 15.038a5.25 5.25 0 0 1 7.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 0 1 1.06 0Z"}))}),D=B.forwardRef(function({title:a,titleId:b,...c},d){return B.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?B.createElement("title",{id:b},a):null,B.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"}))});var E=c(1738);function F(){let{t:a}=(0,t.useTranslation)(["common","services"]),b=[{icon:z.A,title:a("services:vpn.features.multi_protocol.title"),description:a("services:vpn.features.multi_protocol.description")},{icon:A.A,title:a("services:vpn.features.no_logs.title"),description:a("services:vpn.features.no_logs.description")},{icon:C,title:a("services:vpn.features.kill_switch.title"),description:a("services:vpn.features.kill_switch.description")},{icon:D,title:a("services:vpn.features.multi_device.title"),description:a("services:vpn.features.multi_device.description")}],c=[{name:"OpenVPN",description:a("services:vpn.protocols.openvpn"),icon:"\uD83D\uDD12"},{name:"IKEv2",description:a("services:vpn.protocols.ikev2"),icon:"⚡"},{name:"WireGuard",description:a("services:vpn.protocols.wireguard"),icon:"\uD83D\uDE80"},{name:"Shadowsocks",description:a("services:vpn.protocols.shadowsocks"),icon:"\uD83C\uDF10"}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(v(),{children:[(0,r.jsx)("title",{children:"VPN服务 - VPL专业网络解决方案"}),(0,r.jsx)("meta",{name:"description",content:"VPL企业级VPN服务，支持OpenVPN、IKEv2、WireGuard等多种协议，零日志政策，多设备支持，确保您的网络通信安全。"})]}),(0,r.jsxs)(y.A,{children:[(0,r.jsx)("section",{className:"bg-gradient-to-br from-purple-50 to-indigo-100 py-20",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-lg bg-purple-100 mb-8",children:(0,r.jsx)(z.A,{className:"h-10 w-10 text-purple-600"})}),(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl",children:a("services:vpn.title")}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto",children:a("services:vpn.subtitle")}),(0,r.jsx)("p",{className:"mt-4 text-base text-gray-500 max-w-2xl mx-auto",children:a("services:vpn.description")}),(0,r.jsx)("div",{className:"mt-10",children:(0,r.jsx)(x(),{href:"/contact",className:"rounded-md bg-purple-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-purple-500 transition-colors duration-200",children:a("common:buttons.contact_us")})})]})})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"text-center mb-16",children:(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:a("services:vpn.features.title")})}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",children:b.map((a,b)=>{let c=a.icon;return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-purple-100 mb-6",children:(0,r.jsx)(c,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:a.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:a.description})]},b)})})]})}),(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"text-center mb-16",children:(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:a("services:vpn.protocols.title")})}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:c.map((a,b)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mr-4",children:(0,r.jsx)("span",{className:"text-2xl",children:a.icon})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:a.name})]}),(0,r.jsx)("p",{className:"text-gray-600",children:a.description})]},b))})]})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"lg:grid lg:grid-cols-2 lg:gap-16 lg:items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"企业级安全保障"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"我们的VPN服务采用最先进的加密技术和安全协议，为您的企业提供银行级别的安全保护。"}),(0,r.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(E.A,{className:"flex-shrink-0 h-6 w-6 text-green-500 mt-1"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-base font-medium text-gray-900",children:"军用级加密"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"采用AES-256加密算法，确保数据传输的绝对安全。"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(E.A,{className:"flex-shrink-0 h-6 w-6 text-green-500 mt-1"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-base font-medium text-gray-900",children:"DNS泄露保护"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"防止DNS泄露，确保您的网络活动完全匿名。"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(E.A,{className:"flex-shrink-0 h-6 w-6 text-green-500 mt-1"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-base font-medium text-gray-900",children:"完美前向保密"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"即使密钥被破解，历史通信数据仍然安全。"})]})]})]})]}),(0,r.jsx)("div",{className:"mt-12 lg:mt-0",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg p-8 text-white",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"支持的平台"}),(0,r.jsx)("p",{className:"mb-6",children:"我们的VPN服务支持所有主流操作系统和设备，确保您在任何地方都能安全上网。"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDDA5️"}),(0,r.jsx)("span",{children:"Windows"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-2xl mr-2",children:"\uD83C\uDF4E"}),(0,r.jsx)("span",{children:"macOS"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCF1"}),(0,r.jsx)("span",{children:"iOS"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-2xl mr-2",children:"\uD83E\uDD16"}),(0,r.jsx)("span",{children:"Android"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDC27"}),(0,r.jsx)("span",{children:"Linux"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCE1"}),(0,r.jsx)("span",{children:"路由器"})]})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(x(),{href:"/contact",className:"inline-block bg-white text-purple-600 px-6 py-3 rounded-md font-semibold hover:bg-gray-50 transition-colors duration-200",children:"立即体验"})})]})})]})})})]})]})}let G=async({locale:a})=>({props:{...await (0,s.serverSideTranslations)(a??"zh",["common","services"])}});var H=c(6755),I=c(156),J=c(12),K=c(2072),L=c(8164),M=c(4971),N=c(8737),O=c(6439),P=c(5735),Q=c(6713),R=c(3103),S=c(5952);let T=(0,o.M)(d,"default"),U=(0,o.M)(d,"getStaticProps"),V=(0,o.M)(d,"getStaticPaths"),W=(0,o.M)(d,"getServerSideProps"),X=(0,o.M)(d,"config"),Y=(0,o.M)(d,"reportWebVitals"),Z=(0,o.M)(d,"unstable_getStaticProps"),$=(0,o.M)(d,"unstable_getStaticPaths"),_=(0,o.M)(d,"unstable_getStaticParams"),aa=(0,o.M)(d,"unstable_getServerProps"),ab=(0,o.M)(d,"unstable_getServerSideProps"),ac=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/services/vpn",pathname:"/services/vpn",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:q.default,Document:p.default},userland:d});async function ad(a,b,c){var e,o;let p="/services/vpn";"/index"===p&&(p="/");let q="false",r=await ac.prepare(a,b,{srcPage:p,multiZoneDraftMode:q});if(!r){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:s,query:t,params:u,parsedUrl:v,originalQuery:w,originalPathname:x,buildManifest:y,nextFontManifest:z,serverFilesManifest:A,reactLoadableManifest:B,prerenderManifest:C,isDraftMode:D,isOnDemandRevalidate:E,revalidateOnlyGenerated:G,locale:T,locales:Y,defaultLocale:Z,routerServerContext:$,nextConfig:_,resolvedPathname:aa}=r,ab=null==A||null==(o=A.config)||null==(e=o.experimental)?void 0:e.isExperimentalCompile,ad=!!W,ae=!!U,af=!!V,ag=!!(F||d).getInitialProps,ah=t.amp&&X.amp,ai=null,aj=!1,ak=r.isNextDataRequest&&(ae||ad),al="/404"===p,am="/500"===p,an="/_error"===p;if(ac.isDev||D||!ae||(ai=`${T?`/${T}`:""}${("/"===p||"/"===aa)&&T?"":aa}${ah?".amp":""}`,(al||am||an)&&(ai=`${T?`/${T}`:""}${p}${ah?".amp":""}`),ai="/index"===ai?"/":ai),af&&!D){let a=(0,S.removeTrailingSlash)(T?(0,R.addPathPrefix)(aa,`/${T}`):aa),b=!!C.routes[a]||C.notFoundRoutes.includes(a),c=C.dynamicRoutes[p];if(c){if(!1===c.fallback&&!b)throw new O.NoFallbackError;"string"!=typeof c.fallback||b||ak||(aj=!0)}}(aj&&(0,Q.isBot)(a.headers["user-agent"]||"")||(0,j.getRequestMeta)(a,"minimalMode"))&&(aj=!1);let ao=(0,h.getTracer)(),ap=ao.getActiveScopeSpan();try{let e=a.method||"GET",o=(0,i.formatUrl)({pathname:_.trailingSlash?v.pathname:(0,S.removeTrailingSlash)(v.pathname||"/"),query:ae?{}:w}),r=(null==$?void 0:$.publicRuntimeConfig)||_.publicRuntimeConfig,A=async h=>{var A,F;let O,Q=async({previousCacheEntry:v})=>{var A;let F=async()=>{try{var c,f,A;return await ac.render(a,b,{query:ae&&!ab?{...u,...ah?{amp:t.amp}:{}}:{...t,...u},params:u,page:p,renderContext:{isDraftMode:D,isFallback:aj,developmentNotFoundSourcePage:(0,j.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:s,customServer:!!(null==$?void 0:$.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:u,routeModule:ac,page:p,pageConfig:X||{},Component:(0,k.T)(d),ComponentMod:d,getStaticProps:U,getStaticPaths:V,getServerSideProps:W,supportsDynamicResponse:!ae,buildManifest:y,nextFontManifest:z,reactLoadableManifest:B,assetPrefix:_.assetPrefix,strictNextHead:_.experimental.strictNextHead??!0,previewProps:C.preview,images:_.images,nextConfigOutput:_.output,optimizeCss:!!_.experimental.optimizeCss,nextScriptWorkers:!!_.experimental.nextScriptWorkers,domainLocales:null==(c=_.i18n)?void 0:c.domains,crossOrigin:_.crossOrigin,multiZoneDraftMode:q,basePath:_.basePath,canonicalBase:_.amp.canonicalBase||"",ampOptimizerConfig:null==(f=_.experimental.amp)?void 0:f.optimizer,disableOptimizedLoading:_.experimental.disableOptimizedLoading,largePageDataBytes:_.experimental.largePageDataBytes,runtimeConfig:Object.keys(r).length>0?r:void 0,isExperimentalCompile:ab,experimental:{clientTraceMetadata:_.experimental.clientTraceMetadata||[]},locale:T,locales:Y,defaultLocale:Z,setIsrStatus:null==$?void 0:$.setIsrStatus,isNextDataRequest:ak&&(ad||ae),resolvedUrl:o,resolvedAsPath:ad||ag?(0,i.formatUrl)({pathname:ak?(0,m.normalizeDataPath)(x):x,query:w}):o,isOnDemandRevalidate:E,ErrorDebug:(0,j.getRequestMeta)(a,"PagesErrorDebug"),err:(0,j.getRequestMeta)(a,"invokeError"),dev:ac.isDev,distDir:`${ac.projectDir}/${ac.distDir}`,ampSkipValidation:null==(A=_.experimental.amp)?void 0:A.skipValidation,ampValidator:(0,j.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:n.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:n.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!h)return;h.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=ao.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==g.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let d=c.get("next.route");if(d){let a=`${e} ${d}`;h.setAttributes({"next.route":d,"http.route":d,"next.span_name":a}),h.updateName(a)}else h.updateName(`${e} ${a.url}`)})}catch(b){throw(null==v?void 0:v.isStale)&&await ac.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:E})},$),b}};if(v&&(aj=!1),aj){let b=await ac.getResponseCache(a).get(ac.isDev?null:T?`/${T}${p}`:p,async({previousCacheEntry:a=null})=>ac.isDev?F():(0,N.toResponseCacheEntry)(a),{routeKind:f.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await ac.getIncrementalCache(a,_,C),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,j.getRequestMeta)(a,"minimalMode")&&E&&G&&!v?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):aj&&(null==v||null==(A=v.value)?void 0:A.kind)===n.CachedRouteKind.PAGES?{value:{kind:n.CachedRouteKind.PAGES,html:new M.default(Buffer.from(v.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:v.value.status,headers:v.value.headers}}),pageData:{},status:v.value.status,headers:v.value.headers},cacheControl:{revalidate:0,expire:void 0}}:F()},R=await ac.handleResponse({cacheKey:ai,req:a,nextConfig:_,routeKind:f.RouteKind.PAGES,isOnDemandRevalidate:E,revalidateOnlyGenerated:G,waitUntil:c.waitUntil,responseGenerator:Q,prerenderManifest:C});if(!aj||(null==R?void 0:R.isMiss)||(aj=!1),R){if(ae&&!(0,j.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",E?"REVALIDATED":R.isMiss?"MISS":R.isStale?"STALE":"HIT"),!ae||aj)b.getHeader("Cache-Control")||(O={revalidate:0,expire:void 0});else if(al){let b=(0,j.getRequestMeta)(a,"notFoundRevalidate");O={revalidate:void 0===b?0:b,expire:void 0}}else if(am)O={revalidate:0,expire:void 0};else if(R.cacheControl)if("number"==typeof R.cacheControl.revalidate){if(R.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${R.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});O={revalidate:R.cacheControl.revalidate,expire:(null==(A=R.cacheControl)?void 0:A.expire)??_.expireTime}}else O={revalidate:K.CACHE_ONE_YEAR,expire:void 0};if(O&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,H.getCacheControlHeader)(O)),!R.value)return((0,j.addRequestMeta)(a,"notFoundRevalidate",null==(F=R.cacheControl)?void 0:F.revalidate),b.statusCode=404,ak)?void b.end('{"notFound":true}'):void((null==$?void 0:$.render404)?await $.render404(a,b,v,!1):b.end("This page could not be found"));if(R.value.kind===n.CachedRouteKind.REDIRECT)if(!ak)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,J.getRedirectStatus)(c),{basePath:e}=_;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,I.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===P.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(R.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(R.value.props));return}if(R.value.kind!==n.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(ac.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),D&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,j.getRequestMeta)(a,"customErrorRender")||an&&(0,j.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,L.sendRenderResult)({req:a,res:b,result:!ak||an||am?R.value.html:new M.default(Buffer.from(JSON.stringify(R.value.pageData)),{contentType:"application/json",metadata:R.value.html.metadata}),generateEtags:_.generateEtags,poweredByHeader:_.poweredByHeader,cacheControl:ac.isDev?void 0:O,type:ak?"json":"html"})}};ap?await A():await ao.withPropagatedContext(a.headers,()=>ao.trace(g.BaseServerSpan.handleRequest,{spanName:`${e} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":e,"http.target":a.url}},A))}catch(b){throw await ac.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:E})},$),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[341,508,190,535,283],()=>b(b.s=9982));module.exports=c})();