"use strict";(()=>{var a={};a.id=736,a.ids=[736],a.modules={156:a=>{a.exports=require("next/dist/shared/lib/utils")},361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1272:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},1322:a=>{a.exports=require("next/dist/shared/lib/router/utils/format-url")},1643:(a,b,c)=>{c.r(b),c.d(b,{config:()=>W,default:()=>S,getServerSideProps:()=>V,getStaticPaths:()=>U,getStaticProps:()=>T,handler:()=>ac,reportWebVitals:()=>X,routeModule:()=>ab,unstable_getServerProps:()=>_,unstable_getServerSideProps:()=>aa,unstable_getStaticParams:()=>$,unstable_getStaticPaths:()=>Z,unstable_getStaticProps:()=>Y});var d={};c.r(d),c.d(d,{default:()=>E,getStaticProps:()=>F});var e=c(3885),f=c(237),g=c(772),h=c(2410),i=c(1322),j=c(5124),k=c(8647),l=c(3709),m=c(7909),n=c(5122),o=c(1413),p=c(1779),q=c(2081),r=c(8732),s=c(5576),t=c(8751),u=c(9788),v=c.n(u),w=c(9918),x=c.n(w),y=c(897),z=c(8549),A=c(9540),B=c(1738),C=c(6386),D=c(1272);function E(){let{t:a}=(0,t.useTranslation)(["common","services"]),b=[{icon:z.A,title:a("services:foreign_trade.features.dedicated_lines.title"),description:a("services:foreign_trade.features.dedicated_lines.description")},{icon:A.A,title:a("services:foreign_trade.features.global_coverage.title"),description:a("services:foreign_trade.features.global_coverage.description")},{icon:B.A,title:a("services:foreign_trade.features.high_availability.title"),description:a("services:foreign_trade.features.high_availability.description")},{icon:C.A,title:a("services:foreign_trade.features.technical_support.title"),description:a("services:foreign_trade.features.technical_support.description")}],c=[{name:"AES",description:a("services:foreign_trade.encryption.aes")},{name:"RSA",description:a("services:foreign_trade.encryption.rsa")},{name:"TLS",description:a("services:foreign_trade.encryption.tls")},{name:"Tunnel",description:a("services:foreign_trade.encryption.tunnel")}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(v(),{children:[(0,r.jsx)("title",{children:"外贸网络线路 - VPL专业网络解决方案"}),(0,r.jsx)("meta",{name:"description",content:"VPL外贸网络线路服务，专为外贸企业设计，提供专线服务、全球覆盖、高可用性和7x24技术支持，采用AES、RSA、TLS等多重加密技术。"})]}),(0,r.jsxs)(y.A,{children:[(0,r.jsx)("section",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-lg bg-blue-100 mb-8",children:(0,r.jsx)(A.A,{className:"h-10 w-10 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl",children:a("services:foreign_trade.title")}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto",children:a("services:foreign_trade.subtitle")}),(0,r.jsx)("p",{className:"mt-4 text-base text-gray-500 max-w-2xl mx-auto",children:a("services:foreign_trade.description")}),(0,r.jsx)("div",{className:"mt-10",children:(0,r.jsx)(x(),{href:"/contact",className:"rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors duration-200",children:a("common:buttons.contact_us")})})]})})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"text-center mb-16",children:(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:a("services:foreign_trade.features.title")})}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",children:b.map((a,b)=>{let c=a.icon;return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-blue-100 mb-6",children:(0,r.jsx)(c,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:a.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:a.description})]},b)})})]})}),(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"text-center mb-16",children:(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:a("services:foreign_trade.encryption.title")})}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:c.map((a,b)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg mr-4",children:(0,r.jsx)("span",{className:"text-blue-600 font-bold text-sm",children:a.name})}),(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:[a.name," 加密技术"]})]}),(0,r.jsx)("p",{className:"text-gray-600",children:a.description})]},b))})]})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"lg:grid lg:grid-cols-2 lg:gap-16 lg:items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"为什么选择我们的外贸网络线路？"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"我们专注于为外贸企业提供最优质的网络连接服务，确保您的国际业务顺利进行。"}),(0,r.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(B.A,{className:"flex-shrink-0 h-6 w-6 text-green-500 mt-1"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-base font-medium text-gray-900",children:"专业团队支持"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"拥有丰富经验的网络工程师团队，提供专业的技术支持和咨询服务。"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(B.A,{className:"flex-shrink-0 h-6 w-6 text-green-500 mt-1"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-base font-medium text-gray-900",children:"定制化解决方案"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"根据您的具体业务需求，提供量身定制的网络解决方案。"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(B.A,{className:"flex-shrink-0 h-6 w-6 text-green-500 mt-1"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-base font-medium text-gray-900",children:"成本效益优化"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"合理的价格策略，帮助您在保证质量的同时控制成本。"})]})]})]})]}),(0,r.jsx)("div",{className:"mt-12 lg:mt-0",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-8 text-white",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"立即开始"}),(0,r.jsx)("p",{className:"mb-6",children:"联系我们的专业团队，获取适合您业务的外贸网络线路解决方案。"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(C.A,{className:"h-5 w-5 mr-3"}),(0,r.jsx)("span",{children:"电话咨询：+86 400-xxx-xxxx"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(D.A,{className:"h-5 w-5 mr-3"}),(0,r.jsx)("span",{children:"服务时间：7x24小时"})]})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(x(),{href:"/contact",className:"inline-block bg-white text-blue-600 px-6 py-3 rounded-md font-semibold hover:bg-gray-50 transition-colors duration-200",children:"立即咨询"})})]})})]})})})]})]})}let F=async({locale:a})=>({props:{...await (0,s.serverSideTranslations)(a??"zh",["common","services"])}});var G=c(6755),H=c(156),I=c(12),J=c(2072),K=c(8164),L=c(4971),M=c(8737),N=c(6439),O=c(5735),P=c(6713),Q=c(3103),R=c(5952);let S=(0,o.M)(d,"default"),T=(0,o.M)(d,"getStaticProps"),U=(0,o.M)(d,"getStaticPaths"),V=(0,o.M)(d,"getServerSideProps"),W=(0,o.M)(d,"config"),X=(0,o.M)(d,"reportWebVitals"),Y=(0,o.M)(d,"unstable_getStaticProps"),Z=(0,o.M)(d,"unstable_getStaticPaths"),$=(0,o.M)(d,"unstable_getStaticParams"),_=(0,o.M)(d,"unstable_getServerProps"),aa=(0,o.M)(d,"unstable_getServerSideProps"),ab=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/services/foreign-trade",pathname:"/services/foreign-trade",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:q.default,Document:p.default},userland:d});async function ac(a,b,c){var e,o;let p="/services/foreign-trade";"/index"===p&&(p="/");let q="false",r=await ab.prepare(a,b,{srcPage:p,multiZoneDraftMode:q});if(!r){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:s,query:t,params:u,parsedUrl:v,originalQuery:w,originalPathname:x,buildManifest:y,nextFontManifest:z,serverFilesManifest:A,reactLoadableManifest:B,prerenderManifest:C,isDraftMode:D,isOnDemandRevalidate:F,revalidateOnlyGenerated:S,locale:X,locales:Y,defaultLocale:Z,routerServerContext:$,nextConfig:_,resolvedPathname:aa}=r,ac=null==A||null==(o=A.config)||null==(e=o.experimental)?void 0:e.isExperimentalCompile,ad=!!V,ae=!!T,af=!!U,ag=!!(E||d).getInitialProps,ah=t.amp&&W.amp,ai=null,aj=!1,ak=r.isNextDataRequest&&(ae||ad),al="/404"===p,am="/500"===p,an="/_error"===p;if(ab.isDev||D||!ae||(ai=`${X?`/${X}`:""}${("/"===p||"/"===aa)&&X?"":aa}${ah?".amp":""}`,(al||am||an)&&(ai=`${X?`/${X}`:""}${p}${ah?".amp":""}`),ai="/index"===ai?"/":ai),af&&!D){let a=(0,R.removeTrailingSlash)(X?(0,Q.addPathPrefix)(aa,`/${X}`):aa),b=!!C.routes[a]||C.notFoundRoutes.includes(a),c=C.dynamicRoutes[p];if(c){if(!1===c.fallback&&!b)throw new N.NoFallbackError;"string"!=typeof c.fallback||b||ak||(aj=!0)}}(aj&&(0,P.isBot)(a.headers["user-agent"]||"")||(0,j.getRequestMeta)(a,"minimalMode"))&&(aj=!1);let ao=(0,h.getTracer)(),ap=ao.getActiveScopeSpan();try{let e=a.method||"GET",o=(0,i.formatUrl)({pathname:_.trailingSlash?v.pathname:(0,R.removeTrailingSlash)(v.pathname||"/"),query:ae?{}:w}),r=(null==$?void 0:$.publicRuntimeConfig)||_.publicRuntimeConfig,A=async h=>{var A,E;let N,P=async({previousCacheEntry:v})=>{var A;let E=async()=>{try{var c,f,A;return await ab.render(a,b,{query:ae&&!ac?{...u,...ah?{amp:t.amp}:{}}:{...t,...u},params:u,page:p,renderContext:{isDraftMode:D,isFallback:aj,developmentNotFoundSourcePage:(0,j.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:s,customServer:!!(null==$?void 0:$.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:u,routeModule:ab,page:p,pageConfig:W||{},Component:(0,k.T)(d),ComponentMod:d,getStaticProps:T,getStaticPaths:U,getServerSideProps:V,supportsDynamicResponse:!ae,buildManifest:y,nextFontManifest:z,reactLoadableManifest:B,assetPrefix:_.assetPrefix,strictNextHead:_.experimental.strictNextHead??!0,previewProps:C.preview,images:_.images,nextConfigOutput:_.output,optimizeCss:!!_.experimental.optimizeCss,nextScriptWorkers:!!_.experimental.nextScriptWorkers,domainLocales:null==(c=_.i18n)?void 0:c.domains,crossOrigin:_.crossOrigin,multiZoneDraftMode:q,basePath:_.basePath,canonicalBase:_.amp.canonicalBase||"",ampOptimizerConfig:null==(f=_.experimental.amp)?void 0:f.optimizer,disableOptimizedLoading:_.experimental.disableOptimizedLoading,largePageDataBytes:_.experimental.largePageDataBytes,runtimeConfig:Object.keys(r).length>0?r:void 0,isExperimentalCompile:ac,experimental:{clientTraceMetadata:_.experimental.clientTraceMetadata||[]},locale:X,locales:Y,defaultLocale:Z,setIsrStatus:null==$?void 0:$.setIsrStatus,isNextDataRequest:ak&&(ad||ae),resolvedUrl:o,resolvedAsPath:ad||ag?(0,i.formatUrl)({pathname:ak?(0,m.normalizeDataPath)(x):x,query:w}):o,isOnDemandRevalidate:F,ErrorDebug:(0,j.getRequestMeta)(a,"PagesErrorDebug"),err:(0,j.getRequestMeta)(a,"invokeError"),dev:ab.isDev,distDir:`${ab.projectDir}/${ab.distDir}`,ampSkipValidation:null==(A=_.experimental.amp)?void 0:A.skipValidation,ampValidator:(0,j.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:n.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:n.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!h)return;h.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=ao.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==g.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let d=c.get("next.route");if(d){let a=`${e} ${d}`;h.setAttributes({"next.route":d,"http.route":d,"next.span_name":a}),h.updateName(a)}else h.updateName(`${e} ${a.url}`)})}catch(b){throw(null==v?void 0:v.isStale)&&await ab.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:F})},$),b}};if(v&&(aj=!1),aj){let b=await ab.getResponseCache(a).get(ab.isDev?null:X?`/${X}${p}`:p,async({previousCacheEntry:a=null})=>ab.isDev?E():(0,M.toResponseCacheEntry)(a),{routeKind:f.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await ab.getIncrementalCache(a,_,C),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,j.getRequestMeta)(a,"minimalMode")&&F&&S&&!v?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):aj&&(null==v||null==(A=v.value)?void 0:A.kind)===n.CachedRouteKind.PAGES?{value:{kind:n.CachedRouteKind.PAGES,html:new L.default(Buffer.from(v.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:v.value.status,headers:v.value.headers}}),pageData:{},status:v.value.status,headers:v.value.headers},cacheControl:{revalidate:0,expire:void 0}}:E()},Q=await ab.handleResponse({cacheKey:ai,req:a,nextConfig:_,routeKind:f.RouteKind.PAGES,isOnDemandRevalidate:F,revalidateOnlyGenerated:S,waitUntil:c.waitUntil,responseGenerator:P,prerenderManifest:C});if(!aj||(null==Q?void 0:Q.isMiss)||(aj=!1),Q){if(ae&&!(0,j.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",F?"REVALIDATED":Q.isMiss?"MISS":Q.isStale?"STALE":"HIT"),!ae||aj)b.getHeader("Cache-Control")||(N={revalidate:0,expire:void 0});else if(al){let b=(0,j.getRequestMeta)(a,"notFoundRevalidate");N={revalidate:void 0===b?0:b,expire:void 0}}else if(am)N={revalidate:0,expire:void 0};else if(Q.cacheControl)if("number"==typeof Q.cacheControl.revalidate){if(Q.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${Q.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});N={revalidate:Q.cacheControl.revalidate,expire:(null==(A=Q.cacheControl)?void 0:A.expire)??_.expireTime}}else N={revalidate:J.CACHE_ONE_YEAR,expire:void 0};if(N&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,G.getCacheControlHeader)(N)),!Q.value)return((0,j.addRequestMeta)(a,"notFoundRevalidate",null==(E=Q.cacheControl)?void 0:E.revalidate),b.statusCode=404,ak)?void b.end('{"notFound":true}'):void((null==$?void 0:$.render404)?await $.render404(a,b,v,!1):b.end("This page could not be found"));if(Q.value.kind===n.CachedRouteKind.REDIRECT)if(!ak)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,I.getRedirectStatus)(c),{basePath:e}=_;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,H.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===O.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(Q.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(Q.value.props));return}if(Q.value.kind!==n.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(ab.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),D&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,j.getRequestMeta)(a,"customErrorRender")||an&&(0,j.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,K.sendRenderResult)({req:a,res:b,result:!ak||an||am?Q.value.html:new L.default(Buffer.from(JSON.stringify(Q.value.pageData)),{contentType:"application/json",metadata:Q.value.html.metadata}),generateEtags:_.generateEtags,poweredByHeader:_.poweredByHeader,cacheControl:ab.isDev?void 0:N,type:ak?"json":"html"})}};ap?await A():await ao.withPropagatedContext(a.headers,()=>ao.trace(g.BaseServerSpan.handleRequest,{spanName:`${e} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":e,"http.target":a.url}},A))}catch(b){throw await ab.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:F})},$),b}}},1738:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},2015:a=>{a.exports=require("react")},2326:a=>{a.exports=require("react-dom")},3103:a=>{a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},3873:a=>{a.exports=require("path")},4075:a=>{a.exports=require("zlib")},5576:a=>{a.exports=require("next-i18next/serverSideTranslations")},5952:a=>{a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},6439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7909:a=>{a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},7910:a=>{a.exports=require("stream")},8732:a=>{a.exports=require("react/jsx-runtime")},8751:a=>{a.exports=require("next-i18next")},9021:a=>{a.exports=require("fs")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[341,508,190,535,283],()=>b(b.s=1643));module.exports=c})();