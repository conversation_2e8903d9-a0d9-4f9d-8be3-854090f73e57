"use strict";(()=>{var a={};a.id=332,a.ids=[332],a.modules={156:a=>{a.exports=require("next/dist/shared/lib/utils")},361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1322:a=>{a.exports=require("next/dist/shared/lib/router/utils/format-url")},2015:a=>{a.exports=require("react")},2326:a=>{a.exports=require("react-dom")},3103:a=>{a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},3873:a=>{a.exports=require("path")},4075:a=>{a.exports=require("zlib")},5432:(a,b,c)=>{c.r(b),c.d(b,{config:()=>R,default:()=>N,getServerSideProps:()=>Q,getStaticPaths:()=>P,getStaticProps:()=>O,handler:()=>Z,reportWebVitals:()=>S,routeModule:()=>Y,unstable_getServerProps:()=>W,unstable_getServerSideProps:()=>X,unstable_getStaticParams:()=>V,unstable_getStaticPaths:()=>U,unstable_getStaticProps:()=>T});var d={};c.r(d),c.d(d,{default:()=>z,getStaticProps:()=>A});var e=c(3885),f=c(237),g=c(772),h=c(2410),i=c(1322),j=c(5124),k=c(8647),l=c(3709),m=c(7909),n=c(5122),o=c(1413),p=c(1779),q=c(2081),r=c(8732),s=c(5576),t=c(8751),u=c(9788),v=c.n(u),w=c(9918),x=c.n(w),y=c(897);function z(){let{t:a}=(0,t.useTranslation)(["common","home"]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(v(),{children:[(0,r.jsx)("title",{children:"VPL - 专业外贸网络线路与VPN服务"}),(0,r.jsx)("meta",{name:"description",content:"VPL专业提供外贸网络线路、跨境电商外网线路、VPN服务，采用AES、RSA、TLS等多重加密技术，确保您的业务安全稳定运行。"}),(0,r.jsx)("meta",{name:"keywords",content:"外贸网络线路,跨境电商,VPN服务,网络加密,AES加密,RSA加密,TLS加密"})]}),(0,r.jsxs)(y.A,{children:[(0,r.jsx)("section",{className:"relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 lg:py-32",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:a("home:hero.title")}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto",children:a("home:hero.subtitle")}),(0,r.jsx)("p",{className:"mt-4 text-base text-gray-500 max-w-2xl mx-auto",children:a("home:hero.description")}),(0,r.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[(0,r.jsx)(x(),{href:"/contact",className:"rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors duration-200",children:a("common:buttons.contact_us")}),(0,r.jsxs)(x(),{href:"/services",className:"text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-200",children:[a("common:buttons.learn_more")," ",(0,r.jsx)("span",{"aria-hidden":"true",children:"→"})]})]})]})})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:a("home:features.title")}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:a("home:features.subtitle")})]}),(0,r.jsxs)("div",{className:"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-blue-100",children:(0,r.jsx)("svg",{className:"h-8 w-8 text-blue-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z"})})}),(0,r.jsx)("h3",{className:"mt-6 text-lg font-semibold text-gray-900",children:a("home:features.security.title")}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:a("home:features.security.description")})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-green-100",children:(0,r.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"})})}),(0,r.jsx)("h3",{className:"mt-6 text-lg font-semibold text-gray-900",children:a("home:features.speed.title")}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:a("home:features.speed.description")})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-purple-100",children:(0,r.jsx)("svg",{className:"h-8 w-8 text-purple-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3V6a3 3 0 013-3h13.5a3 3 0 013 3v5.25a3 3 0 01-3 3m-16.5 0a3 3 0 013 3v6.75a3 3 0 003 3h7.5a3 3 0 003-3v-6.75a3 3 0 013-3"})})}),(0,r.jsx)("h3",{className:"mt-6 text-lg font-semibold text-gray-900",children:a("home:features.reliability.title")}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:a("home:features.reliability.description")})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-orange-100",children:(0,r.jsx)("svg",{className:"h-8 w-8 text-orange-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"})})}),(0,r.jsx)("h3",{className:"mt-6 text-lg font-semibold text-gray-900",children:a("home:features.professional.title")}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:a("home:features.professional.description")})]})]})]})}),(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:a("home:services.title")}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:a("home:services.subtitle")})]}),(0,r.jsxs)("div",{className:"mt-16 grid grid-cols-1 gap-8 lg:grid-cols-3",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8 hover:shadow-md transition-shadow duration-200",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-6",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12.75 3.03v.568c0 .334.148.65.405.864l1.068.89c.442.369.535 1.01.216 1.49l-.51.766a2.25 2.25 0 01-1.161.886l-.143.048a1.107 1.107 0 00-.57 1.664c.369.555.169 1.307-.427 1.605L9 13.125l.423 1.059a.956.956 0 01-1.652.928l-.679-.906a1.125 1.125 0 00-1.906.172L4.5 15.75l-.612.153M12.75 3.031a9 9 0 00-8.862 12.872M12.75 3.031a9 9 0 016.69 14.036m0 0l-.177-.529A2.25 2.25 0 0017.128 15H16.5l-.324-.324a1.453 1.453 0 00-2.328.377l-.036.073a1.586 1.586 0 01-.982.816l-.99.282c-.55.157-.894.702-.8 1.267l.073.438c.08.474.49.821.97.821.846 0 1.598.542 1.865 1.345l.215.643m5.276-3.67a9.012 9.012 0 01-5.276 3.67m0 0a9 9 0 01-10.275-4.835M15.75 9c0 .896-.393 1.7-1.016 2.25"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:a("home:services.foreign_trade.title")}),(0,r.jsx)("p",{className:"text-gray-600",children:a("home:services.foreign_trade.description")})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8 hover:shadow-md transition-shadow duration-200",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-6",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 21v-7.5a.75.75 0 01.75-.75h3a.75.75 0 01.75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349m-16.5 11.65V9.35m0 0a3.001 3.001 0 003.75-.615A2.993 2.993 0 009.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 002.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 003.75.614m-16.5 0a3.004 3.004 0 01-.621-4.72L4.318 3.44A1.5 1.5 0 015.378 3h13.243a1.5 1.5 0 011.06.44l1.19 1.189a3 3 0 01-.621 4.72m-13.5 8.65h3.75a.75.75 0 00.75-.75V13.5a.75.75 0 00-.75-.75H6.75a.75.75 0 00-.75.75v3.75c0 .415.336.75.75.75z"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:a("home:services.ecommerce.title")}),(0,r.jsx)("p",{className:"text-gray-600",children:a("home:services.ecommerce.description")})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8 hover:shadow-md transition-shadow duration-200",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-6",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-purple-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:a("home:services.vpn.title")}),(0,r.jsx)("p",{className:"text-gray-600",children:a("home:services.vpn.description")})]})]})]})}),(0,r.jsx)("section",{className:"py-20 bg-blue-600",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl",children:a("home:cta.title")}),(0,r.jsx)("p",{className:"mt-4 text-lg text-blue-100",children:a("home:cta.subtitle")}),(0,r.jsx)("p",{className:"mt-2 text-base text-blue-200",children:a("home:cta.description")}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)(x(),{href:"/contact",className:"inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-blue-600 shadow-sm hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors duration-200",children:a("common:buttons.contact_us")})})]})})})]})]})}let A=async({locale:a})=>({props:{...await (0,s.serverSideTranslations)(a??"zh",["common","home"])}});var B=c(6755),C=c(156),D=c(12),E=c(2072),F=c(8164),G=c(4971),H=c(8737),I=c(6439),J=c(5735),K=c(6713),L=c(3103),M=c(5952);let N=(0,o.M)(d,"default"),O=(0,o.M)(d,"getStaticProps"),P=(0,o.M)(d,"getStaticPaths"),Q=(0,o.M)(d,"getServerSideProps"),R=(0,o.M)(d,"config"),S=(0,o.M)(d,"reportWebVitals"),T=(0,o.M)(d,"unstable_getStaticProps"),U=(0,o.M)(d,"unstable_getStaticPaths"),V=(0,o.M)(d,"unstable_getStaticParams"),W=(0,o.M)(d,"unstable_getServerProps"),X=(0,o.M)(d,"unstable_getServerSideProps"),Y=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/index",pathname:"/",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:q.default,Document:p.default},userland:d});async function Z(a,b,c){var e,o;let p="/index";"/index"===p&&(p="/");let q="false",r=await Y.prepare(a,b,{srcPage:p,multiZoneDraftMode:q});if(!r){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:s,query:t,params:u,parsedUrl:v,originalQuery:w,originalPathname:x,buildManifest:y,nextFontManifest:A,serverFilesManifest:N,reactLoadableManifest:S,prerenderManifest:T,isDraftMode:U,isOnDemandRevalidate:V,revalidateOnlyGenerated:W,locale:X,locales:Z,defaultLocale:$,routerServerContext:_,nextConfig:aa,resolvedPathname:ab}=r,ac=null==N||null==(o=N.config)||null==(e=o.experimental)?void 0:e.isExperimentalCompile,ad=!!Q,ae=!!O,af=!!P,ag=!!(z||d).getInitialProps,ah=t.amp&&R.amp,ai=null,aj=!1,ak=r.isNextDataRequest&&(ae||ad),al="/404"===p,am="/500"===p,an="/_error"===p;if(Y.isDev||U||!ae||(ai=`${X?`/${X}`:""}${("/"===p||"/"===ab)&&X?"":ab}${ah?".amp":""}`,(al||am||an)&&(ai=`${X?`/${X}`:""}${p}${ah?".amp":""}`),ai="/index"===ai?"/":ai),af&&!U){let a=(0,M.removeTrailingSlash)(X?(0,L.addPathPrefix)(ab,`/${X}`):ab),b=!!T.routes[a]||T.notFoundRoutes.includes(a),c=T.dynamicRoutes[p];if(c){if(!1===c.fallback&&!b)throw new I.NoFallbackError;"string"!=typeof c.fallback||b||ak||(aj=!0)}}(aj&&(0,K.isBot)(a.headers["user-agent"]||"")||(0,j.getRequestMeta)(a,"minimalMode"))&&(aj=!1);let ao=(0,h.getTracer)(),ap=ao.getActiveScopeSpan();try{let e=a.method||"GET",o=(0,i.formatUrl)({pathname:aa.trailingSlash?v.pathname:(0,M.removeTrailingSlash)(v.pathname||"/"),query:ae?{}:w}),r=(null==_?void 0:_.publicRuntimeConfig)||aa.publicRuntimeConfig,z=async h=>{var z,I;let K,L=async({previousCacheEntry:v})=>{var z;let B=async()=>{try{var c,f,z;return await Y.render(a,b,{query:ae&&!ac?{...u,...ah?{amp:t.amp}:{}}:{...t,...u},params:u,page:p,renderContext:{isDraftMode:U,isFallback:aj,developmentNotFoundSourcePage:(0,j.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:s,customServer:!!(null==_?void 0:_.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:u,routeModule:Y,page:p,pageConfig:R||{},Component:(0,k.T)(d),ComponentMod:d,getStaticProps:O,getStaticPaths:P,getServerSideProps:Q,supportsDynamicResponse:!ae,buildManifest:y,nextFontManifest:A,reactLoadableManifest:S,assetPrefix:aa.assetPrefix,strictNextHead:aa.experimental.strictNextHead??!0,previewProps:T.preview,images:aa.images,nextConfigOutput:aa.output,optimizeCss:!!aa.experimental.optimizeCss,nextScriptWorkers:!!aa.experimental.nextScriptWorkers,domainLocales:null==(c=aa.i18n)?void 0:c.domains,crossOrigin:aa.crossOrigin,multiZoneDraftMode:q,basePath:aa.basePath,canonicalBase:aa.amp.canonicalBase||"",ampOptimizerConfig:null==(f=aa.experimental.amp)?void 0:f.optimizer,disableOptimizedLoading:aa.experimental.disableOptimizedLoading,largePageDataBytes:aa.experimental.largePageDataBytes,runtimeConfig:Object.keys(r).length>0?r:void 0,isExperimentalCompile:ac,experimental:{clientTraceMetadata:aa.experimental.clientTraceMetadata||[]},locale:X,locales:Z,defaultLocale:$,setIsrStatus:null==_?void 0:_.setIsrStatus,isNextDataRequest:ak&&(ad||ae),resolvedUrl:o,resolvedAsPath:ad||ag?(0,i.formatUrl)({pathname:ak?(0,m.normalizeDataPath)(x):x,query:w}):o,isOnDemandRevalidate:V,ErrorDebug:(0,j.getRequestMeta)(a,"PagesErrorDebug"),err:(0,j.getRequestMeta)(a,"invokeError"),dev:Y.isDev,distDir:`${Y.projectDir}/${Y.distDir}`,ampSkipValidation:null==(z=aa.experimental.amp)?void 0:z.skipValidation,ampValidator:(0,j.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:n.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:n.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!h)return;h.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=ao.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==g.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let d=c.get("next.route");if(d){let a=`${e} ${d}`;h.setAttributes({"next.route":d,"http.route":d,"next.span_name":a}),h.updateName(a)}else h.updateName(`${e} ${a.url}`)})}catch(b){throw(null==v?void 0:v.isStale)&&await Y.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:V})},_),b}};if(v&&(aj=!1),aj){let b=await Y.getResponseCache(a).get(Y.isDev?null:X?`/${X}${p}`:p,async({previousCacheEntry:a=null})=>Y.isDev?B():(0,H.toResponseCacheEntry)(a),{routeKind:f.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await Y.getIncrementalCache(a,aa,T),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,j.getRequestMeta)(a,"minimalMode")&&V&&W&&!v?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):aj&&(null==v||null==(z=v.value)?void 0:z.kind)===n.CachedRouteKind.PAGES?{value:{kind:n.CachedRouteKind.PAGES,html:new G.default(Buffer.from(v.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:v.value.status,headers:v.value.headers}}),pageData:{},status:v.value.status,headers:v.value.headers},cacheControl:{revalidate:0,expire:void 0}}:B()},M=await Y.handleResponse({cacheKey:ai,req:a,nextConfig:aa,routeKind:f.RouteKind.PAGES,isOnDemandRevalidate:V,revalidateOnlyGenerated:W,waitUntil:c.waitUntil,responseGenerator:L,prerenderManifest:T});if(!aj||(null==M?void 0:M.isMiss)||(aj=!1),M){if(ae&&!(0,j.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",V?"REVALIDATED":M.isMiss?"MISS":M.isStale?"STALE":"HIT"),!ae||aj)b.getHeader("Cache-Control")||(K={revalidate:0,expire:void 0});else if(al){let b=(0,j.getRequestMeta)(a,"notFoundRevalidate");K={revalidate:void 0===b?0:b,expire:void 0}}else if(am)K={revalidate:0,expire:void 0};else if(M.cacheControl)if("number"==typeof M.cacheControl.revalidate){if(M.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${M.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});K={revalidate:M.cacheControl.revalidate,expire:(null==(z=M.cacheControl)?void 0:z.expire)??aa.expireTime}}else K={revalidate:E.CACHE_ONE_YEAR,expire:void 0};if(K&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,B.getCacheControlHeader)(K)),!M.value)return((0,j.addRequestMeta)(a,"notFoundRevalidate",null==(I=M.cacheControl)?void 0:I.revalidate),b.statusCode=404,ak)?void b.end('{"notFound":true}'):void((null==_?void 0:_.render404)?await _.render404(a,b,v,!1):b.end("This page could not be found"));if(M.value.kind===n.CachedRouteKind.REDIRECT)if(!ak)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,D.getRedirectStatus)(c),{basePath:e}=aa;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,C.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===J.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(M.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(M.value.props));return}if(M.value.kind!==n.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(Y.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),U&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,j.getRequestMeta)(a,"customErrorRender")||an&&(0,j.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,F.sendRenderResult)({req:a,res:b,result:!ak||an||am?M.value.html:new G.default(Buffer.from(JSON.stringify(M.value.pageData)),{contentType:"application/json",metadata:M.value.html.metadata}),generateEtags:aa.generateEtags,poweredByHeader:aa.poweredByHeader,cacheControl:Y.isDev?void 0:K,type:ak?"json":"html"})}};ap?await z():await ao.withPropagatedContext(a.headers,()=>ao.trace(g.BaseServerSpan.handleRequest,{spanName:`${e} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":e,"http.target":a.url}},z))}catch(b){throw await Y.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:V})},_),b}}},5576:a=>{a.exports=require("next-i18next/serverSideTranslations")},5952:a=>{a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},6439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7909:a=>{a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},7910:a=>{a.exports=require("stream")},8732:a=>{a.exports=require("react/jsx-runtime")},8751:a=>{a.exports=require("next-i18next")},9021:a=>{a.exports=require("fs")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[341,508,190,535,283],()=>b(b.s=5432));module.exports=c})();