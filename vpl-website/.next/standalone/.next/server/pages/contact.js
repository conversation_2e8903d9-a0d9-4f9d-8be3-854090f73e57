"use strict";(()=>{var a={};a.id=364,a.ids=[364],a.modules={156:a=>{a.exports=require("next/dist/shared/lib/utils")},361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1272:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},1322:a=>{a.exports=require("next/dist/shared/lib/router/utils/format-url")},2015:a=>{a.exports=require("react")},2326:a=>{a.exports=require("react-dom")},2971:a=>{a.exports=import("zod")},3103:a=>{a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},3257:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{config:()=>K,default:()=>G,getServerSideProps:()=>J,getStaticPaths:()=>I,getStaticProps:()=>H,handler:()=>F,reportWebVitals:()=>L,routeModule:()=>R,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>Q,unstable_getStaticParams:()=>O,unstable_getStaticPaths:()=>N,unstable_getStaticProps:()=>M});var e=c(3885),f=c(237),g=c(772),h=c(2410),i=c(1322),j=c(5124),k=c(8647),l=c(3709),m=c(7909),n=c(5122),o=c(1413),p=c(1779),q=c(2081),r=c(4323),s=c(6755),t=c(156),u=c(12),v=c(2072),w=c(8164),x=c(4971),y=c(8737),z=c(6439),A=c(5735),B=c(6713),C=c(3103),D=c(5952),E=a([r]);r=(E.then?(await E)():E)[0];let G=(0,o.M)(r,"default"),H=(0,o.M)(r,"getStaticProps"),I=(0,o.M)(r,"getStaticPaths"),J=(0,o.M)(r,"getServerSideProps"),K=(0,o.M)(r,"config"),L=(0,o.M)(r,"reportWebVitals"),M=(0,o.M)(r,"unstable_getStaticProps"),N=(0,o.M)(r,"unstable_getStaticPaths"),O=(0,o.M)(r,"unstable_getStaticParams"),P=(0,o.M)(r,"unstable_getServerProps"),Q=(0,o.M)(r,"unstable_getServerSideProps"),R=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/contact",pathname:"/contact",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:q.default,Document:p.default},userland:r});async function F(a,b,c){var d,e;let o="/contact";"/index"===o&&(o="/");let p="false",q=await R.prepare(a,b,{srcPage:o,multiZoneDraftMode:p});if(!q){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:E,query:F,params:G,parsedUrl:L,originalQuery:M,originalPathname:N,buildManifest:O,nextFontManifest:P,serverFilesManifest:Q,reactLoadableManifest:S,prerenderManifest:T,isDraftMode:U,isOnDemandRevalidate:V,revalidateOnlyGenerated:W,locale:X,locales:Y,defaultLocale:Z,routerServerContext:$,nextConfig:_,resolvedPathname:aa}=q,ab=null==Q||null==(e=Q.config)||null==(d=e.experimental)?void 0:d.isExperimentalCompile,ac=!!J,ad=!!H,ae=!!I,af=!!(r.default||r).getInitialProps,ag=F.amp&&K.amp,ah=null,ai=!1,aj=q.isNextDataRequest&&(ad||ac),ak="/404"===o,al="/500"===o,am="/_error"===o;if(R.isDev||U||!ad||(ah=`${X?`/${X}`:""}${("/"===o||"/"===aa)&&X?"":aa}${ag?".amp":""}`,(ak||al||am)&&(ah=`${X?`/${X}`:""}${o}${ag?".amp":""}`),ah="/index"===ah?"/":ah),ae&&!U){let a=(0,D.removeTrailingSlash)(X?(0,C.addPathPrefix)(aa,`/${X}`):aa),b=!!T.routes[a]||T.notFoundRoutes.includes(a),c=T.dynamicRoutes[o];if(c){if(!1===c.fallback&&!b)throw new z.NoFallbackError;"string"!=typeof c.fallback||b||aj||(ai=!0)}}(ai&&(0,B.isBot)(a.headers["user-agent"]||"")||(0,j.getRequestMeta)(a,"minimalMode"))&&(ai=!1);let an=(0,h.getTracer)(),ao=an.getActiveScopeSpan();try{let d=a.method||"GET",e=(0,i.formatUrl)({pathname:_.trailingSlash?L.pathname:(0,D.removeTrailingSlash)(L.pathname||"/"),query:ad?{}:M}),q=(null==$?void 0:$.publicRuntimeConfig)||_.publicRuntimeConfig,z=async h=>{var z,B;let C,D=async({previousCacheEntry:s})=>{var t;let u=async()=>{try{var c,f,t;return await R.render(a,b,{query:ad&&!ab?{...G,...ag?{amp:F.amp}:{}}:{...F,...G},params:G,page:o,renderContext:{isDraftMode:U,isFallback:ai,developmentNotFoundSourcePage:(0,j.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:E,customServer:!!(null==$?void 0:$.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:G,routeModule:R,page:o,pageConfig:K||{},Component:(0,k.T)(r),ComponentMod:r,getStaticProps:H,getStaticPaths:I,getServerSideProps:J,supportsDynamicResponse:!ad,buildManifest:O,nextFontManifest:P,reactLoadableManifest:S,assetPrefix:_.assetPrefix,strictNextHead:_.experimental.strictNextHead??!0,previewProps:T.preview,images:_.images,nextConfigOutput:_.output,optimizeCss:!!_.experimental.optimizeCss,nextScriptWorkers:!!_.experimental.nextScriptWorkers,domainLocales:null==(c=_.i18n)?void 0:c.domains,crossOrigin:_.crossOrigin,multiZoneDraftMode:p,basePath:_.basePath,canonicalBase:_.amp.canonicalBase||"",ampOptimizerConfig:null==(f=_.experimental.amp)?void 0:f.optimizer,disableOptimizedLoading:_.experimental.disableOptimizedLoading,largePageDataBytes:_.experimental.largePageDataBytes,runtimeConfig:Object.keys(q).length>0?q:void 0,isExperimentalCompile:ab,experimental:{clientTraceMetadata:_.experimental.clientTraceMetadata||[]},locale:X,locales:Y,defaultLocale:Z,setIsrStatus:null==$?void 0:$.setIsrStatus,isNextDataRequest:aj&&(ac||ad),resolvedUrl:e,resolvedAsPath:ac||af?(0,i.formatUrl)({pathname:aj?(0,m.normalizeDataPath)(N):N,query:M}):e,isOnDemandRevalidate:V,ErrorDebug:(0,j.getRequestMeta)(a,"PagesErrorDebug"),err:(0,j.getRequestMeta)(a,"invokeError"),dev:R.isDev,distDir:`${R.projectDir}/${R.distDir}`,ampSkipValidation:null==(t=_.experimental.amp)?void 0:t.skipValidation,ampValidator:(0,j.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:n.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:n.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!h)return;h.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=an.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==g.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=c.get("next.route");if(e){let a=`${d} ${e}`;h.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),h.updateName(a)}else h.updateName(`${d} ${a.url}`)})}catch(b){throw(null==s?void 0:s.isStale)&&await R.onRequestError(a,b,{routerKind:"Pages Router",routePath:o,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ad,isOnDemandRevalidate:V})},$),b}};if(s&&(ai=!1),ai){let b=await R.getResponseCache(a).get(R.isDev?null:X?`/${X}${o}`:o,async({previousCacheEntry:a=null})=>R.isDev?u():(0,y.toResponseCacheEntry)(a),{routeKind:f.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await R.getIncrementalCache(a,_,T),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,j.getRequestMeta)(a,"minimalMode")&&V&&W&&!s?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):ai&&(null==s||null==(t=s.value)?void 0:t.kind)===n.CachedRouteKind.PAGES?{value:{kind:n.CachedRouteKind.PAGES,html:new x.default(Buffer.from(s.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:s.value.status,headers:s.value.headers}}),pageData:{},status:s.value.status,headers:s.value.headers},cacheControl:{revalidate:0,expire:void 0}}:u()},Q=await R.handleResponse({cacheKey:ah,req:a,nextConfig:_,routeKind:f.RouteKind.PAGES,isOnDemandRevalidate:V,revalidateOnlyGenerated:W,waitUntil:c.waitUntil,responseGenerator:D,prerenderManifest:T});if(!ai||(null==Q?void 0:Q.isMiss)||(ai=!1),Q){if(ad&&!(0,j.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",V?"REVALIDATED":Q.isMiss?"MISS":Q.isStale?"STALE":"HIT"),!ad||ai)b.getHeader("Cache-Control")||(C={revalidate:0,expire:void 0});else if(ak){let b=(0,j.getRequestMeta)(a,"notFoundRevalidate");C={revalidate:void 0===b?0:b,expire:void 0}}else if(al)C={revalidate:0,expire:void 0};else if(Q.cacheControl)if("number"==typeof Q.cacheControl.revalidate){if(Q.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${Q.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});C={revalidate:Q.cacheControl.revalidate,expire:(null==(z=Q.cacheControl)?void 0:z.expire)??_.expireTime}}else C={revalidate:v.CACHE_ONE_YEAR,expire:void 0};if(C&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,s.getCacheControlHeader)(C)),!Q.value)return((0,j.addRequestMeta)(a,"notFoundRevalidate",null==(B=Q.cacheControl)?void 0:B.revalidate),b.statusCode=404,aj)?void b.end('{"notFound":true}'):void((null==$?void 0:$.render404)?await $.render404(a,b,L,!1):b.end("This page could not be found"));if(Q.value.kind===n.CachedRouteKind.REDIRECT)if(!aj)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,u.getRedirectStatus)(c),{basePath:e}=_;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,t.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===A.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(Q.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(Q.value.props));return}if(Q.value.kind!==n.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(R.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),U&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,j.getRequestMeta)(a,"customErrorRender")||am&&(0,j.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,w.sendRenderResult)({req:a,res:b,result:!aj||am||al?Q.value.html:new x.default(Buffer.from(JSON.stringify(Q.value.pageData)),{contentType:"application/json",metadata:Q.value.html.metadata}),generateEtags:_.generateEtags,poweredByHeader:_.poweredByHeader,cacheControl:R.isDev?void 0:C,type:aj?"json":"html"})}};ao?await z():await an.withPropagatedContext(a.headers,()=>an.trace(g.BaseServerSpan.handleRequest,{spanName:`${d} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":d,"http.target":a.url}},z))}catch(b){throw await R.onRequestError(a,b,{routerKind:"Pages Router",routePath:o,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ad,isOnDemandRevalidate:V})},$),b}}d()}catch(a){d(a)}})},3873:a=>{a.exports=require("path")},4075:a=>{a.exports=require("zlib")},4323:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>s,getStaticProps:()=>t});var e=c(8732),f=c(5576),g=c(8751),h=c(9788),i=c.n(h),j=c(897),k=c(6545),l=c(6386),m=c(7936),n=c(9094),o=c(7493),p=c(7623),q=c(1272),r=a([k]);function s(){let{t:a}=(0,g.useTranslation)(["contact","common"]),b=[{icon:l.A,title:a("contact:contact_methods.phone"),value:"+86 400-xxx-xxxx",description:"工作日 9:00-18:00"},{icon:m.A,title:a("contact:contact_methods.email"),value:"<EMAIL>",description:"24小时内回复"},{icon:n.A,title:a("contact:contact_methods.wechat"),value:"VPL-Service",description:"扫码添加客服微信"},{icon:o.A,title:a("contact:contact_methods.qq"),value:"12345678",description:"QQ在线咨询"}];return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(i(),{children:[(0,e.jsx)("title",{children:"联系我们 - VPL专业网络解决方案"}),(0,e.jsx)("meta",{name:"description",content:"联系VPL获取专业的B2B网络解决方案咨询，我们提供外贸网络线路、跨境电商外网线路、VPN服务等多种联系方式。"})]}),(0,e.jsxs)(j.A,{children:[(0,e.jsx)("section",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",children:(0,e.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,e.jsxs)("div",{className:"text-center",children:[(0,e.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl",children:a("contact:title")}),(0,e.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto",children:a("contact:subtitle")})]})})}),(0,e.jsx)("section",{className:"py-16 bg-white",children:(0,e.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,e.jsx)("div",{className:"text-center mb-12",children:(0,e.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:a("contact:contact_methods.title")})}),(0,e.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:b.map((a,b)=>{let c=a.icon;return(0,e.jsxs)("div",{className:"text-center p-6 bg-gray-50 rounded-lg",children:[(0,e.jsx)("div",{className:"mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 mb-4",children:(0,e.jsx)(c,{className:"h-6 w-6 text-blue-600"})}),(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.title}),(0,e.jsx)("p",{className:"text-blue-600 font-medium mb-1",children:a.value}),(0,e.jsx)("p",{className:"text-sm text-gray-500",children:a.description})]},b)})})]})}),(0,e.jsx)("section",{className:"py-20 bg-gray-50",children:(0,e.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,e.jsxs)("div",{className:"lg:grid lg:grid-cols-3 lg:gap-16",children:[(0,e.jsx)("div",{className:"lg:col-span-2",children:(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8",children:[(0,e.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"在线咨询表单"}),(0,e.jsx)(k.A,{})]})}),(0,e.jsx)("div",{className:"mt-12 lg:mt-0",children:(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8",children:[(0,e.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"联系信息"}),(0,e.jsxs)("div",{className:"space-y-6",children:[(0,e.jsxs)("div",{className:"flex items-start",children:[(0,e.jsx)(p.A,{className:"flex-shrink-0 h-6 w-6 text-blue-600 mt-1"}),(0,e.jsxs)("div",{className:"ml-3",children:[(0,e.jsx)("h4",{className:"text-base font-medium text-gray-900",children:"公司地址"}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["中国广东省深圳市南山区",(0,e.jsx)("br",{}),"科技园南区软件产业基地"]})]})]}),(0,e.jsxs)("div",{className:"flex items-start",children:[(0,e.jsx)(q.A,{className:"flex-shrink-0 h-6 w-6 text-blue-600 mt-1"}),(0,e.jsxs)("div",{className:"ml-3",children:[(0,e.jsx)("h4",{className:"text-base font-medium text-gray-900",children:"服务时间"}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["周一至周五：9:00 - 18:00",(0,e.jsx)("br",{}),"周末及节假日：10:00 - 16:00",(0,e.jsx)("br",{}),"紧急技术支持：7x24小时"]})]})]}),(0,e.jsxs)("div",{className:"flex items-start",children:[(0,e.jsx)(l.A,{className:"flex-shrink-0 h-6 w-6 text-blue-600 mt-1"}),(0,e.jsxs)("div",{className:"ml-3",children:[(0,e.jsx)("h4",{className:"text-base font-medium text-gray-900",children:"联系电话"}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["销售咨询：+86 400-xxx-xxxx",(0,e.jsx)("br",{}),"技术支持：+86 400-xxx-xxxx",(0,e.jsx)("br",{}),"投诉建议：+86 400-xxx-xxxx"]})]})]})]}),(0,e.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[(0,e.jsx)("h4",{className:"text-base font-medium text-blue-900 mb-2",children:"为什么选择我们？"}),(0,e.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,e.jsx)("li",{children:"• 专业的技术团队支持"}),(0,e.jsx)("li",{children:"• 7x24小时服务保障"}),(0,e.jsx)("li",{children:"• 定制化解决方案"}),(0,e.jsx)("li",{children:"• 银行级安全保护"})]})]})]})})]})})}),(0,e.jsx)("section",{className:"py-20 bg-white",children:(0,e.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,e.jsxs)("div",{className:"text-center mb-12",children:[(0,e.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"常见问题"}),(0,e.jsx)("p",{className:"text-lg text-gray-600",children:"以下是客户经常询问的问题，如有其他疑问请联系我们"})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-2",children:[(0,e.jsxs)("div",{className:"space-y-6",children:[(0,e.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"你们的服务覆盖哪些地区？"}),(0,e.jsx)("p",{className:"text-gray-600",children:"我们的服务覆盖全球主要贸易区域，包括北美、欧洲、东南亚等地区，可以为您提供稳定的国际网络连接。"})]}),(0,e.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"如何保证网络连接的稳定性？"}),(0,e.jsx)("p",{className:"text-gray-600",children:"我们采用多线路冗余设计，配备专业的监控系统，确保99.9%的服务可用性，并提供7x24小时技术支持。"})]})]}),(0,e.jsxs)("div",{className:"space-y-6",children:[(0,e.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"你们的加密技术安全吗？"}),(0,e.jsx)("p",{className:"text-gray-600",children:"我们采用军用级AES-256加密、RSA非对称加密、TLS协议等多重安全保障，确保您的数据传输绝对安全。"})]}),(0,e.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"如何开始使用你们的服务？"}),(0,e.jsx)("p",{className:"text-gray-600",children:"您可以通过填写上方的咨询表单或直接联系我们的销售团队，我们会根据您的需求提供最适合的解决方案。"})]})]})]})]})})]})]})}k=(r.then?(await r)():r)[0];let t=async({locale:a})=>({props:{...await (0,f.serverSideTranslations)(a??"zh",["contact","common"])}});d()}catch(a){d(a)}})},5576:a=>{a.exports=require("next-i18next/serverSideTranslations")},5952:a=>{a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},6439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6545:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{A:()=>p});var e=c(8732),f=c(2015),g=c(6884),h=c(8825),i=c(2971),j=c(8751),k=c(6386),l=c(7936),m=c(9094),n=c(7493),o=a([g,h,i]);[g,h,i]=o.then?(await o)():o;let q=i.z.object({companyName:i.z.string().min(1,"Company name is required"),contactPerson:i.z.string().min(1,"Contact person is required"),phone:i.z.string().min(1,"Phone number is required"),email:i.z.string().email("Invalid email address"),wechat:i.z.string().optional(),qq:i.z.string().optional(),serviceType:i.z.string().min(1,"Service type is required"),message:i.z.string().min(10,"Message must be at least 10 characters"),verificationCode:i.z.string().min(1,"Verification code is required")});function p(){let{t:a}=(0,j.useTranslation)(["contact","common"]),[b,c]=(0,f.useState)(!1),[d,i]=(0,f.useState)(""),[o,p]=(0,f.useState)(""),[r,s]=(0,f.useState)(!1),{register:t,handleSubmit:u,formState:{errors:v},reset:w}=(0,g.useForm)({resolver:(0,h.zodResolver)(q)}),x=async b=>{if(b.verificationCode!==o)return void alert(a("contact:error.verification_failed"));c(!0);try{if((await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)})).ok)s(!0),w(),p("");else throw Error("Failed to submit form")}catch(b){alert(a("contact:error.message"))}finally{c(!1)}},y=[{value:"foreign_trade_lines",label:a("contact:services.foreign_trade_lines")},{value:"ecommerce_lines",label:a("contact:services.ecommerce_lines")},{value:"vpn_services",label:a("contact:services.vpn_services")},{value:"custom_solution",label:a("contact:services.custom_solution")}];return r?(0,e.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-8 text-center",children:[(0,e.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 mb-4",children:(0,e.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,e.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12.75l6 6 9-13.5"})})}),(0,e.jsx)("h3",{className:"text-lg font-semibold text-green-900 mb-2",children:a("contact:success.title")}),(0,e.jsx)("p",{className:"text-green-700 mb-6",children:a("contact:success.message")}),(0,e.jsx)("button",{onClick:()=>s(!1),className:"bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors duration-200",children:a("common:buttons.back")})]}):(0,e.jsxs)("form",{onSubmit:u(x),className:"space-y-6",children:[(0,e.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,e.jsxs)("div",{children:[(0,e.jsxs)("label",{htmlFor:"companyName",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("contact:form.company_name")," ",(0,e.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,e.jsx)("input",{...t("companyName"),type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.company_name_placeholder")}),v.companyName&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.companyName.message})]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("label",{htmlFor:"contactPerson",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("contact:form.contact_person")," ",(0,e.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,e.jsx)("input",{...t("contactPerson"),type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.contact_person_placeholder")}),v.contactPerson&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.contactPerson.message})]})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,e.jsxs)("div",{children:[(0,e.jsxs)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("contact:form.phone")," ",(0,e.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,e.jsxs)("div",{className:"relative",children:[(0,e.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,e.jsx)(k.A,{className:"h-5 w-5 text-gray-400"})}),(0,e.jsx)("input",{...t("phone"),type:"tel",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.phone_placeholder")})]}),v.phone&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.phone.message})]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("contact:form.email")," ",(0,e.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,e.jsxs)("div",{className:"relative",children:[(0,e.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,e.jsx)(l.A,{className:"h-5 w-5 text-gray-400"})}),(0,e.jsx)("input",{...t("email"),type:"email",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.email_placeholder")})]}),v.email&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.email.message})]})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("label",{htmlFor:"wechat",className:"block text-sm font-medium text-gray-700 mb-2",children:a("contact:form.wechat")}),(0,e.jsxs)("div",{className:"relative",children:[(0,e.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,e.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,e.jsx)("input",{...t("wechat"),type:"text",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.wechat_placeholder")})]})]}),(0,e.jsxs)("div",{children:[(0,e.jsx)("label",{htmlFor:"qq",className:"block text-sm font-medium text-gray-700 mb-2",children:a("contact:form.qq")}),(0,e.jsxs)("div",{className:"relative",children:[(0,e.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,e.jsx)(n.A,{className:"h-5 w-5 text-gray-400"})}),(0,e.jsx)("input",{...t("qq"),type:"text",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.qq_placeholder")})]})]})]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("label",{htmlFor:"serviceType",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("contact:form.service_type")," ",(0,e.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,e.jsxs)("select",{...t("serviceType"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,e.jsx)("option",{value:"",children:a("contact:form.service_type_placeholder")}),y.map(a=>(0,e.jsx)("option",{value:a.value,children:a.label},a.value))]}),v.serviceType&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.serviceType.message})]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("contact:form.message")," ",(0,e.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,e.jsx)("textarea",{...t("message"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.message_placeholder")}),v.message&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.message.message})]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("label",{htmlFor:"verificationCode",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("contact:form.verification_code")," ",(0,e.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,e.jsxs)("div",{className:"flex space-x-3",children:[(0,e.jsx)("input",{...t("verificationCode"),type:"text",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:a("contact:form.verification_code_placeholder")}),(0,e.jsx)("button",{type:"button",onClick:()=>{let a="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",b="";for(let c=0;c<6;c++)b+=a.charAt(Math.floor(Math.random()*a.length));return p(b),b},className:"px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors duration-200",children:a("contact:form.get_verification_code")})]}),o&&(0,e.jsx)("div",{className:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,e.jsxs)("p",{className:"text-sm text-blue-800",children:["验证码: ",(0,e.jsx)("span",{className:"font-mono font-bold text-lg",children:o})]})}),v.verificationCode&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.verificationCode.message})]}),(0,e.jsx)("div",{children:(0,e.jsx)("button",{type:"submit",disabled:b,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:b?a("contact:form.submitting"):a("contact:form.submit")})})]})}d()}catch(a){d(a)}})},6713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6884:a=>{a.exports=import("react-hook-form")},7493:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},7909:a=>{a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},7910:a=>{a.exports=require("stream")},8732:a=>{a.exports=require("react/jsx-runtime")},8751:a=>{a.exports=require("next-i18next")},8825:a=>{a.exports=import("@hookform/resolvers/zod")},9021:a=>{a.exports=require("fs")},9094:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[341,508,190,535,283],()=>b(b.s=3257));module.exports=c})();