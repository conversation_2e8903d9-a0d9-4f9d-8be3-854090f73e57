"use strict";(()=>{var a={};a.id=712,a.ids=[712],a.modules={156:a=>{a.exports=require("next/dist/shared/lib/utils")},361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1322:a=>{a.exports=require("next/dist/shared/lib/router/utils/format-url")},1707:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(2015);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))})},2015:a=>{a.exports=require("react")},2326:a=>{a.exports=require("react-dom")},3103:a=>{a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},3873:a=>{a.exports=require("path")},4075:a=>{a.exports=require("zlib")},5576:a=>{a.exports=require("next-i18next/serverSideTranslations")},5745:(a,b,c)=>{c.r(b),c.d(b,{config:()=>X,default:()=>T,getServerSideProps:()=>W,getStaticPaths:()=>V,getStaticProps:()=>U,handler:()=>ad,reportWebVitals:()=>Y,routeModule:()=>ac,unstable_getServerProps:()=>aa,unstable_getServerSideProps:()=>ab,unstable_getStaticParams:()=>_,unstable_getStaticPaths:()=>$,unstable_getStaticProps:()=>Z});var d={};c.r(d),c.d(d,{default:()=>F,getStaticProps:()=>G});var e=c(3885),f=c(237),g=c(772),h=c(2410),i=c(1322),j=c(5124),k=c(8647),l=c(3709),m=c(7909),n=c(5122),o=c(1413),p=c(1779),q=c(2081),r=c(8732),s=c(5576),t=c(8751),u=c(9788),v=c.n(u),w=c(9918),x=c.n(w),y=c(897),z=c(9540),A=c(2015);let B=A.forwardRef(function({title:a,titleId:b,...c},d){return A.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?A.createElement("title",{id:b},a):null,A.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))});var C=c(5559),D=c(1707);let E=A.forwardRef(function({title:a,titleId:b,...c},d){return A.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?A.createElement("title",{id:b},a):null,A.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))});function F(){let{t:a}=(0,t.useTranslation)(["common","services"]),b=[{id:"foreign-trade",title:a("services:foreign_trade.title"),description:a("services:foreign_trade.description"),icon:z.A,color:"blue",href:"/services/foreign-trade"},{id:"ecommerce",title:a("services:ecommerce.title"),description:a("services:ecommerce.description"),icon:B,color:"green",href:"/services/ecommerce"},{id:"vpn",title:a("services:vpn.title"),description:a("services:vpn.description"),icon:C.A,color:"purple",href:"/services/vpn"},{id:"custom",title:a("services:custom.title"),description:a("services:custom.description"),icon:D.A,color:"orange",href:"/services/custom"}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(v(),{children:[(0,r.jsx)("title",{children:"服务 - VPL专业网络解决方案"}),(0,r.jsx)("meta",{name:"description",content:"VPL提供外贸网络线路、跨境电商外网线路、VPN服务和定制解决方案，采用先进的加密技术，确保您的业务安全稳定运行。"})]}),(0,r.jsxs)(y.A,{children:[(0,r.jsx)("section",{className:"bg-gradient-to-br from-gray-50 to-blue-50 py-20",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl",children:a("services:title")}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto",children:a("services:subtitle")})]})})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-2",children:b.map(a=>{let b=a.icon;return(0,r.jsx)("div",{className:"relative bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-md transition-all duration-200 group",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center transition-colors duration-200 ${(a=>{let b={blue:"bg-blue-100 text-blue-600 hover:bg-blue-200",green:"bg-green-100 text-green-600 hover:bg-green-200",purple:"bg-purple-100 text-purple-600 hover:bg-purple-200",orange:"bg-orange-100 text-orange-600 hover:bg-orange-200"};return b[a]||b.blue})(a.color)}`,children:(0,r.jsx)(b,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:a.title}),(0,r.jsx)("p",{className:"text-gray-600 mb-6 leading-relaxed",children:a.description}),(0,r.jsxs)(x(),{href:a.href,className:"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200",children:["了解更多",(0,r.jsx)(E,{className:"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-200"})]})]})]})},a.id)})})})}),(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"先进的加密技术"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"采用多重加密技术，确保您的数据传输安全"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-blue-100 mb-4",children:(0,r.jsx)("span",{className:"text-blue-600 font-bold text-lg",children:"AES"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"AES加密"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"AES-256高级加密标准，军用级别的数据保护"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-green-100 mb-4",children:(0,r.jsx)("span",{className:"text-green-600 font-bold text-lg",children:"RSA"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"RSA加密"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"RSA非对称加密算法，确保密钥交换安全"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-purple-100 mb-4",children:(0,r.jsx)("span",{className:"text-purple-600 font-bold text-lg",children:"TLS"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"TLS加密"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"传输层安全协议，保护数据传输过程"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-orange-100 mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-orange-600",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"})})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"隧道加密"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"多层隧道加密技术，构建安全通信通道"})]})]})]})}),(0,r.jsx)("section",{className:"py-20 bg-blue-600",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl",children:a("services:contact_cta.title")}),(0,r.jsx)("p",{className:"mt-4 text-lg text-blue-100",children:a("services:contact_cta.subtitle")}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)(x(),{href:"/contact",className:"inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-blue-600 shadow-sm hover:bg-gray-50 transition-colors duration-200",children:a("services:contact_cta.button")})})]})})})]})]})}let G=async({locale:a})=>({props:{...await (0,s.serverSideTranslations)(a??"zh",["common","services"])}});var H=c(6755),I=c(156),J=c(12),K=c(2072),L=c(8164),M=c(4971),N=c(8737),O=c(6439),P=c(5735),Q=c(6713),R=c(3103),S=c(5952);let T=(0,o.M)(d,"default"),U=(0,o.M)(d,"getStaticProps"),V=(0,o.M)(d,"getStaticPaths"),W=(0,o.M)(d,"getServerSideProps"),X=(0,o.M)(d,"config"),Y=(0,o.M)(d,"reportWebVitals"),Z=(0,o.M)(d,"unstable_getStaticProps"),$=(0,o.M)(d,"unstable_getStaticPaths"),_=(0,o.M)(d,"unstable_getStaticParams"),aa=(0,o.M)(d,"unstable_getServerProps"),ab=(0,o.M)(d,"unstable_getServerSideProps"),ac=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/services",pathname:"/services",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:q.default,Document:p.default},userland:d});async function ad(a,b,c){var e,o;let p="/services";"/index"===p&&(p="/");let q="false",r=await ac.prepare(a,b,{srcPage:p,multiZoneDraftMode:q});if(!r){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:s,query:t,params:u,parsedUrl:v,originalQuery:w,originalPathname:x,buildManifest:y,nextFontManifest:z,serverFilesManifest:A,reactLoadableManifest:B,prerenderManifest:C,isDraftMode:D,isOnDemandRevalidate:E,revalidateOnlyGenerated:G,locale:T,locales:Y,defaultLocale:Z,routerServerContext:$,nextConfig:_,resolvedPathname:aa}=r,ab=null==A||null==(o=A.config)||null==(e=o.experimental)?void 0:e.isExperimentalCompile,ad=!!W,ae=!!U,af=!!V,ag=!!(F||d).getInitialProps,ah=t.amp&&X.amp,ai=null,aj=!1,ak=r.isNextDataRequest&&(ae||ad),al="/404"===p,am="/500"===p,an="/_error"===p;if(ac.isDev||D||!ae||(ai=`${T?`/${T}`:""}${("/"===p||"/"===aa)&&T?"":aa}${ah?".amp":""}`,(al||am||an)&&(ai=`${T?`/${T}`:""}${p}${ah?".amp":""}`),ai="/index"===ai?"/":ai),af&&!D){let a=(0,S.removeTrailingSlash)(T?(0,R.addPathPrefix)(aa,`/${T}`):aa),b=!!C.routes[a]||C.notFoundRoutes.includes(a),c=C.dynamicRoutes[p];if(c){if(!1===c.fallback&&!b)throw new O.NoFallbackError;"string"!=typeof c.fallback||b||ak||(aj=!0)}}(aj&&(0,Q.isBot)(a.headers["user-agent"]||"")||(0,j.getRequestMeta)(a,"minimalMode"))&&(aj=!1);let ao=(0,h.getTracer)(),ap=ao.getActiveScopeSpan();try{let e=a.method||"GET",o=(0,i.formatUrl)({pathname:_.trailingSlash?v.pathname:(0,S.removeTrailingSlash)(v.pathname||"/"),query:ae?{}:w}),r=(null==$?void 0:$.publicRuntimeConfig)||_.publicRuntimeConfig,A=async h=>{var A,F;let O,Q=async({previousCacheEntry:v})=>{var A;let F=async()=>{try{var c,f,A;return await ac.render(a,b,{query:ae&&!ab?{...u,...ah?{amp:t.amp}:{}}:{...t,...u},params:u,page:p,renderContext:{isDraftMode:D,isFallback:aj,developmentNotFoundSourcePage:(0,j.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:s,customServer:!!(null==$?void 0:$.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:u,routeModule:ac,page:p,pageConfig:X||{},Component:(0,k.T)(d),ComponentMod:d,getStaticProps:U,getStaticPaths:V,getServerSideProps:W,supportsDynamicResponse:!ae,buildManifest:y,nextFontManifest:z,reactLoadableManifest:B,assetPrefix:_.assetPrefix,strictNextHead:_.experimental.strictNextHead??!0,previewProps:C.preview,images:_.images,nextConfigOutput:_.output,optimizeCss:!!_.experimental.optimizeCss,nextScriptWorkers:!!_.experimental.nextScriptWorkers,domainLocales:null==(c=_.i18n)?void 0:c.domains,crossOrigin:_.crossOrigin,multiZoneDraftMode:q,basePath:_.basePath,canonicalBase:_.amp.canonicalBase||"",ampOptimizerConfig:null==(f=_.experimental.amp)?void 0:f.optimizer,disableOptimizedLoading:_.experimental.disableOptimizedLoading,largePageDataBytes:_.experimental.largePageDataBytes,runtimeConfig:Object.keys(r).length>0?r:void 0,isExperimentalCompile:ab,experimental:{clientTraceMetadata:_.experimental.clientTraceMetadata||[]},locale:T,locales:Y,defaultLocale:Z,setIsrStatus:null==$?void 0:$.setIsrStatus,isNextDataRequest:ak&&(ad||ae),resolvedUrl:o,resolvedAsPath:ad||ag?(0,i.formatUrl)({pathname:ak?(0,m.normalizeDataPath)(x):x,query:w}):o,isOnDemandRevalidate:E,ErrorDebug:(0,j.getRequestMeta)(a,"PagesErrorDebug"),err:(0,j.getRequestMeta)(a,"invokeError"),dev:ac.isDev,distDir:`${ac.projectDir}/${ac.distDir}`,ampSkipValidation:null==(A=_.experimental.amp)?void 0:A.skipValidation,ampValidator:(0,j.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:n.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:n.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!h)return;h.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=ao.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==g.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let d=c.get("next.route");if(d){let a=`${e} ${d}`;h.setAttributes({"next.route":d,"http.route":d,"next.span_name":a}),h.updateName(a)}else h.updateName(`${e} ${a.url}`)})}catch(b){throw(null==v?void 0:v.isStale)&&await ac.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:E})},$),b}};if(v&&(aj=!1),aj){let b=await ac.getResponseCache(a).get(ac.isDev?null:T?`/${T}${p}`:p,async({previousCacheEntry:a=null})=>ac.isDev?F():(0,N.toResponseCacheEntry)(a),{routeKind:f.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await ac.getIncrementalCache(a,_,C),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,j.getRequestMeta)(a,"minimalMode")&&E&&G&&!v?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):aj&&(null==v||null==(A=v.value)?void 0:A.kind)===n.CachedRouteKind.PAGES?{value:{kind:n.CachedRouteKind.PAGES,html:new M.default(Buffer.from(v.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:v.value.status,headers:v.value.headers}}),pageData:{},status:v.value.status,headers:v.value.headers},cacheControl:{revalidate:0,expire:void 0}}:F()},R=await ac.handleResponse({cacheKey:ai,req:a,nextConfig:_,routeKind:f.RouteKind.PAGES,isOnDemandRevalidate:E,revalidateOnlyGenerated:G,waitUntil:c.waitUntil,responseGenerator:Q,prerenderManifest:C});if(!aj||(null==R?void 0:R.isMiss)||(aj=!1),R){if(ae&&!(0,j.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",E?"REVALIDATED":R.isMiss?"MISS":R.isStale?"STALE":"HIT"),!ae||aj)b.getHeader("Cache-Control")||(O={revalidate:0,expire:void 0});else if(al){let b=(0,j.getRequestMeta)(a,"notFoundRevalidate");O={revalidate:void 0===b?0:b,expire:void 0}}else if(am)O={revalidate:0,expire:void 0};else if(R.cacheControl)if("number"==typeof R.cacheControl.revalidate){if(R.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${R.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});O={revalidate:R.cacheControl.revalidate,expire:(null==(A=R.cacheControl)?void 0:A.expire)??_.expireTime}}else O={revalidate:K.CACHE_ONE_YEAR,expire:void 0};if(O&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,H.getCacheControlHeader)(O)),!R.value)return((0,j.addRequestMeta)(a,"notFoundRevalidate",null==(F=R.cacheControl)?void 0:F.revalidate),b.statusCode=404,ak)?void b.end('{"notFound":true}'):void((null==$?void 0:$.render404)?await $.render404(a,b,v,!1):b.end("This page could not be found"));if(R.value.kind===n.CachedRouteKind.REDIRECT)if(!ak)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,J.getRedirectStatus)(c),{basePath:e}=_;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,I.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===P.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(R.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(R.value.props));return}if(R.value.kind!==n.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(ac.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),D&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,j.getRequestMeta)(a,"customErrorRender")||an&&(0,j.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,L.sendRenderResult)({req:a,res:b,result:!ak||an||am?R.value.html:new M.default(Buffer.from(JSON.stringify(R.value.pageData)),{contentType:"application/json",metadata:R.value.html.metadata}),generateEtags:_.generateEtags,poweredByHeader:_.poweredByHeader,cacheControl:ac.isDev?void 0:O,type:ak?"json":"html"})}};ap?await A():await ao.withPropagatedContext(a.headers,()=>ao.trace(g.BaseServerSpan.handleRequest,{spanName:`${e} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":e,"http.target":a.url}},A))}catch(b){throw await ac.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:E})},$),b}}},5952:a=>{a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},6439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7909:a=>{a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},7910:a=>{a.exports=require("stream")},8732:a=>{a.exports=require("react/jsx-runtime")},8751:a=>{a.exports=require("next-i18next")},9021:a=>{a.exports=require("fs")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[341,508,190,535,283],()=>b(b.s=5745));module.exports=c})();