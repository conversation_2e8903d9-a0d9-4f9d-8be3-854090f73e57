{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/_document": "pages/_document.js", "/api/admin/analytics": "pages/api/admin/analytics.js", "/api/admin/dashboard": "pages/api/admin/dashboard.js", "/api/admin/email-config": "pages/api/admin/email-config.js", "/api/admin/email-config/[id]": "pages/api/admin/email-config/[id].js", "/api/admin/login": "pages/api/admin/login.js", "/api/admin/submissions/[id]": "pages/api/admin/submissions/[id].js", "/api/admin/submissions": "pages/api/admin/submissions.js", "/api/admin/system-config": "pages/api/admin/system-config.js", "/api/admin/users/[id]": "pages/api/admin/users/[id].js", "/api/admin/users": "pages/api/admin/users.js", "/api/admin/verify": "pages/api/admin/verify.js", "/api/contact": "pages/api/contact.js", "/api/socket": "pages/api/socket.js", "/api/admin/logs": "pages/api/admin/logs.js", "/contact": "pages/contact.js", "/services/foreign-trade": "pages/services/foreign-trade.js", "/": "pages/index.js", "/services": "pages/services.js", "/services/vpn": "pages/services/vpn.js", "/zh/404": "pages/zh/404.html", "/en/404": "pages/en/404.html", "/ru/404": "pages/ru/404.html", "/zh/500": "pages/zh/500.html", "/en/500": "pages/en/500.html", "/ru/500": "pages/ru/500.html", "/zh/admin/inquiries/[id]": "pages/zh/admin/inquiries/[id].html", "/en/admin/inquiries/[id]": "pages/en/admin/inquiries/[id].html", "/ru/admin/inquiries/[id]": "pages/ru/admin/inquiries/[id].html", "/zh/admin/users": "pages/zh/admin/users.html", "/en/admin/users": "pages/en/admin/users.html", "/ru/admin/users": "pages/ru/admin/users.html", "/zh/admin/email-settings": "pages/zh/admin/email-settings.html", "/en/admin/email-settings": "pages/en/admin/email-settings.html", "/ru/admin/email-settings": "pages/ru/admin/email-settings.html", "/zh/admin/inquiries": "pages/zh/admin/inquiries.html", "/en/admin/inquiries": "pages/en/admin/inquiries.html", "/ru/admin/inquiries": "pages/ru/admin/inquiries.html", "/zh/admin/settings": "pages/zh/admin/settings.html", "/en/admin/settings": "pages/en/admin/settings.html", "/ru/admin/settings": "pages/ru/admin/settings.html", "/zh/admin/logs": "pages/zh/admin/logs.html", "/en/admin/logs": "pages/en/admin/logs.html", "/ru/admin/logs": "pages/ru/admin/logs.html", "/zh/admin/login": "pages/zh/admin/login.html", "/en/admin/login": "pages/en/admin/login.html", "/ru/admin/login": "pages/ru/admin/login.html", "/zh/admin/dashboard": "pages/zh/admin/dashboard.html", "/en/admin/dashboard": "pages/en/admin/dashboard.html", "/ru/admin/dashboard": "pages/ru/admin/dashboard.html", "/zh/admin/analytics": "pages/zh/admin/analytics.html", "/en/admin/analytics": "pages/en/admin/analytics.html", "/ru/admin/analytics": "pages/ru/admin/analytics.html"}