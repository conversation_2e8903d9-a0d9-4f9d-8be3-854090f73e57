{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "locale": false, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/:nextInternalLocale(zh|en|ru)/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:\\/(zh|en|ru))(?:\\/(.*))(?:\\/)?$"}, {"source": "/:nextInternalLocale(zh|en|ru)/uploads/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^(?:\\/(zh|en|ru))\\/uploads(?:\\/(.*))(?:\\/)?$"}], "i18n": {"locales": ["zh", "en", "ru"], "defaultLocale": "zh", "localeDetection": false}}