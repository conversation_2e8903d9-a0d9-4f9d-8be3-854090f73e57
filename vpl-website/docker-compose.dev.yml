version: '3.8'

services:
  # Next.js Application (Development)
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: vpl-website-app-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://vpl_user:${POSTGRES_PASSWORD:-dev_password}@postgres:5432/vpl_website_dev
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-dev-secret-key}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret}
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - SMTP_FROM=${SMTP_FROM}
      - ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
      - uploads_dev:/app/public/uploads
      - logs_dev:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - vpl-network-dev
    command: npm run dev

  # PostgreSQL Database (Development)
  postgres:
    image: postgres:15-alpine
    container_name: vpl-website-postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=vpl_website_dev
      - POSTGRES_USER=vpl_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-dev_password}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"  # Expose for external access in dev
    networks:
      - vpl-network-dev
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vpl_user -d vpl_website_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Cache (Development)
  redis:
    image: redis:7-alpine
    container_name: vpl-website-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data_dev:/data
    ports:
      - "6379:6379"  # Expose for external access in dev
    networks:
      - vpl-network-dev
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # pgAdmin (Development only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: vpl-website-pgadmin-dev
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data_dev:/var/lib/pgadmin
    ports:
      - "8080:80"
    networks:
      - vpl-network-dev
    depends_on:
      postgres:
        condition: service_healthy

  # Redis Commander (Development only)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: vpl-website-redis-commander-dev
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - vpl-network-dev
    depends_on:
      redis:
        condition: service_healthy

# Named volumes for data persistence (Development)
volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  pgadmin_data_dev:
    driver: local
  uploads_dev:
    driver: local
  logs_dev:
    driver: local

# Custom network for container communication (Development)
networks:
  vpl-network-dev:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
