import { NextApiRequest, NextApiResponse } from 'next';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: 'connected' | 'disconnected' | 'unknown';
    redis: 'connected' | 'disconnected' | 'unknown';
    filesystem: 'accessible' | 'inaccessible';
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<HealthStatus>) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: 'unknown',
        redis: 'unknown',
        filesystem: 'inaccessible'
      },
      memory: {
        used: 0,
        total: 0,
        percentage: 0
      }
    } as HealthStatus);
  }

  try {
    // Check filesystem access
    const fs = require('fs');
    const path = require('path');
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    
    let filesystemStatus: 'accessible' | 'inaccessible' = 'accessible';
    try {
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }
      // Test write access
      const testFile = path.join(uploadsDir, '.health-check');
      fs.writeFileSync(testFile, 'health-check');
      fs.unlinkSync(testFile);
    } catch (error) {
      filesystemStatus = 'inaccessible';
    }

    // Check database connection (mock for now)
    let databaseStatus: 'connected' | 'disconnected' | 'unknown' = 'unknown';
    try {
      // In a real application, you would check the actual database connection
      // For now, we'll assume it's connected if DATABASE_URL is set
      if (process.env.DATABASE_URL) {
        databaseStatus = 'connected';
      } else {
        databaseStatus = 'disconnected';
      }
    } catch (error) {
      databaseStatus = 'disconnected';
    }

    // Check Redis connection (mock for now)
    let redisStatus: 'connected' | 'disconnected' | 'unknown' = 'unknown';
    try {
      // In a real application, you would check the actual Redis connection
      // For now, we'll assume it's connected if REDIS_URL is set
      if (process.env.REDIS_URL) {
        redisStatus = 'connected';
      } else {
        redisStatus = 'disconnected';
      }
    } catch (error) {
      redisStatus = 'disconnected';
    }

    // Get memory usage
    const memUsage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const usedMemory = memUsage.heapUsed;
    const memoryPercentage = (usedMemory / totalMemory) * 100;

    // Determine overall health status
    const isHealthy = filesystemStatus === 'accessible' && 
                     databaseStatus !== 'disconnected' && 
                     redisStatus !== 'disconnected' &&
                     memoryPercentage < 90; // Consider unhealthy if memory usage > 90%

    const healthStatus: HealthStatus = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: databaseStatus,
        redis: redisStatus,
        filesystem: filesystemStatus
      },
      memory: {
        used: Math.round(usedMemory / 1024 / 1024), // MB
        total: Math.round(totalMemory / 1024 / 1024), // MB
        percentage: Math.round(memoryPercentage * 100) / 100
      }
    };

    // Set appropriate HTTP status code
    const statusCode = isHealthy ? 200 : 503;
    
    // Set cache headers to prevent caching of health checks
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    return res.status(statusCode).json(healthStatus);

  } catch (error) {
    console.error('Health check error:', error);
    
    return res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: 'unknown',
        redis: 'unknown',
        filesystem: 'inaccessible'
      },
      memory: {
        used: 0,
        total: 0,
        percentage: 0
      }
    });
  }
}
