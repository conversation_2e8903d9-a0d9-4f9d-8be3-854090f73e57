import type { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';
import { Server as ServerIO } from 'socket.io';
import { Server as NetServer } from 'http';
import { ContactSubmissionService } from '../../lib/dataService';

interface ContactFormData {
  companyName: string;
  contactPerson: string;
  phone: string;
  email: string;
  wechat?: string;
  qq?: string;
  serviceType: string;
  message: string;
  verificationCode: string;
}

interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
}

type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: ServerIO;
    };
  };
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponseServerIO
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const formData: ContactFormData = req.body;

    // Validate required fields
    const requiredFields = ['companyName', 'contactPerson', 'phone', 'email', 'serviceType', 'message'];
    for (const field of requiredFields) {
      if (!formData[field as keyof ContactFormData]) {
        return res.status(400).json({
          success: false,
          message: `Missing required field: ${field}`
        });
      }
    }

    // Create email transporter (configure with your SMTP settings)
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Email content for admin notification
    const adminEmailContent = `
      <h2>新的咨询表单提交</h2>
      <p><strong>提交时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
      
      <h3>公司信息</h3>
      <p><strong>公司名称:</strong> ${formData.companyName}</p>
      <p><strong>联系人:</strong> ${formData.contactPerson}</p>
      
      <h3>联系方式</h3>
      <p><strong>电话:</strong> ${formData.phone}</p>
      <p><strong>邮箱:</strong> ${formData.email}</p>
      ${formData.wechat ? `<p><strong>微信:</strong> ${formData.wechat}</p>` : ''}
      ${formData.qq ? `<p><strong>QQ:</strong> ${formData.qq}</p>` : ''}
      
      <h3>服务需求</h3>
      <p><strong>服务类型:</strong> ${getServiceTypeName(formData.serviceType)}</p>
      <p><strong>详细需求:</strong></p>
      <p>${formData.message.replace(/\n/g, '<br>')}</p>
    `;

    // Email content for customer confirmation
    const customerEmailContent = `
      <h2>感谢您的咨询</h2>
      <p>尊敬的 ${formData.contactPerson}，</p>
      <p>我们已收到您的咨询，以下是您提交的信息：</p>
      
      <h3>公司信息</h3>
      <p><strong>公司名称:</strong> ${formData.companyName}</p>
      <p><strong>联系人:</strong> ${formData.contactPerson}</p>
      
      <h3>服务需求</h3>
      <p><strong>服务类型:</strong> ${getServiceTypeName(formData.serviceType)}</p>
      <p><strong>详细需求:</strong> ${formData.message}</p>
      
      <p>我们的专业团队将在24小时内与您联系，为您提供最适合的网络解决方案。</p>
      
      <p>如有紧急需求，请直接拨打我们的服务热线：+86 400-xxx-xxxx</p>
      
      <p>感谢您选择VPL！</p>
      <p>VPL团队</p>
    `;

    // Email functionality temporarily disabled for testing
    // TODO: Configure SMTP settings in production
    console.log('Email sending temporarily disabled. Admin notification would be sent to:', process.env.ADMIN_EMAIL || '<EMAIL>');
    console.log('Customer confirmation would be sent to:', formData.email);

    // Simulate successful email sending
    // await transporter.sendMail({
    //   from: process.env.SMTP_FROM || '<EMAIL>',
    //   to: process.env.ADMIN_EMAIL || '<EMAIL>',
    //   subject: `新咨询 - ${formData.companyName} - ${getServiceTypeName(formData.serviceType)}`,
    //   html: adminEmailContent,
    // });

    // await transporter.sendMail({
    //   from: process.env.SMTP_FROM || '<EMAIL>',
    //   to: formData.email,
    //   subject: '感谢您的咨询 - VPL专业网络解决方案',
    //   html: customerEmailContent,
    // });

    // Store in data service
    const submissionResult = await ContactSubmissionService.create({
      companyName: formData.companyName,
      contactPerson: formData.contactPerson,
      phone: formData.phone,
      email: formData.email,
      wechat: formData.wechat,
      qq: formData.qq,
      serviceType: formData.serviceType as 'foreign_trade_lines' | 'ecommerce_lines' | 'vpn_services' | 'custom_solution',
      message: formData.message,
      priority: 'medium',
      source: 'website',
    });

    // Log the submission for monitoring
    console.log('Contact form submission:', {
      timestamp: new Date().toISOString(),
      company: formData.companyName,
      email: formData.email,
      serviceType: formData.serviceType,
      stored: submissionResult.success,
    });

    // Send real-time notification to admin dashboard
    if (res.socket?.server?.io && submissionResult.success) {
      res.socket.server.io.to('admin').emit('new-submission', submissionResult.data);
    }

    return res.status(200).json({
      success: true,
      message: 'Contact form submitted successfully',
    });

  } catch (error) {
    console.error('Contact form submission error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

function getServiceTypeName(serviceType: string): string {
  const serviceTypes: { [key: string]: string } = {
    'foreign_trade_lines': '外贸网络线路',
    'ecommerce_lines': '跨境电商外网线路',
    'vpn_services': 'VPN服务',
    'custom_solution': '定制解决方案',
  };
  return serviceTypes[serviceType] || serviceType;
}

// Optional: Function to store contact submissions in database
// async function storeContactSubmission(formData: ContactFormData) {
//   // Implement database storage logic here
//   // Example with Prisma:
//   // const submission = await prisma.contactSubmission.create({
//   //   data: {
//   //     companyName: formData.companyName,
//   //     contactPerson: formData.contactPerson,
//   //     phone: formData.phone,
//   //     email: formData.email,
//   //     wechat: formData.wechat,
//   //     qq: formData.qq,
//   //     serviceType: formData.serviceType,
//   //     message: formData.message,
//   //     submittedAt: new Date(),
//   //   },
//   // });
//   // return submission;
// }
