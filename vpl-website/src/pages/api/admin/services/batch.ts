import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';

// Import the same mock data (in production, this would be from database)
let services = [
  {
    id: '1',
    name: 'VPN服务',
    slug: 'vpn_services',
    title: 'VPN服务 - 安全可靠的网络连接',
    description: '军用级加密的VPN连接服务，保护您的数据安全',
    content: '<h2>VPN服务详情</h2><p>我们提供军用级加密的VPN连接服务...</p>',
    features: ['军用级加密', '零日志政策', '全球服务器', '无限带宽'],
    status: 'active' as const,
    sortOrder: 1,
    seoTitle: 'VPN服务 - 安全可靠的网络连接 | VPL',
    seoDescription: '军用级加密的VPN连接服务，保护您的数据安全，零日志政策，全球服务器覆盖',
    seoKeywords: 'VPN,网络安全,加密,隐私保护',
    images: ['/images/services/vpn-hero.jpg'],
    tags: ['网络安全', 'VPN', '加密'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    version: 1
  },
  {
    id: '2',
    name: '跨境电商专线',
    slug: 'ecommerce_lines',
    title: '跨境电商专线 - 优化购物体验',
    description: '优化的跨境电商网络连接解决方案，提升用户购物体验',
    content: '<h2>跨境电商专线详情</h2><p>专为跨境电商设计的网络解决方案...</p>',
    features: ['多平台支持', '高可用性', '流量优化', '实时监控'],
    status: 'active' as const,
    sortOrder: 2,
    seoTitle: '跨境电商专线 - 优化购物体验 | VPL',
    seoDescription: '优化的跨境电商网络连接解决方案，提升用户购物体验，支持多平台',
    seoKeywords: '跨境电商,网络专线,购物体验,电商优化',
    images: ['/images/services/ecommerce-hero.jpg'],
    tags: ['跨境电商', '网络专线', '购物优化'],
    createdAt: '2024-01-14T10:00:00Z',
    updatedAt: '2024-01-14T10:00:00Z',
    version: 1
  },
  {
    id: '3',
    name: '外贸专线',
    slug: 'foreign_trade_lines',
    title: '外贸专线 - 全球贸易网络',
    description: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
    content: '<h2>外贸专线详情</h2><p>专为外贸企业设计的高速稳定网络线路...</p>',
    features: ['专用带宽', '全球覆盖', '低延迟', '企业级支持'],
    status: 'active' as const,
    sortOrder: 3,
    seoTitle: '外贸专线 - 全球贸易网络 | VPL',
    seoDescription: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
    seoKeywords: '外贸专线,全球贸易,网络线路,企业网络',
    images: ['/images/services/trade-hero.jpg'],
    tags: ['外贸', '专线', '全球网络'],
    createdAt: '2024-01-13T10:00:00Z',
    updatedAt: '2024-01-13T10:00:00Z',
    version: 1
  },
  {
    id: '4',
    name: '定制解决方案',
    slug: 'custom_solution',
    title: '定制解决方案 - 专属网络方案',
    description: '根据您的特殊需求定制专属网络解决方案',
    content: '<h2>定制解决方案详情</h2><p>根据您的特殊需求定制专属网络解决方案...</p>',
    features: ['定制设计', '专家咨询', '可扩展架构', '持续支持'],
    status: 'active' as const,
    sortOrder: 4,
    seoTitle: '定制解决方案 - 专属网络方案 | VPL',
    seoDescription: '根据您的特殊需求定制专属网络解决方案，专家咨询，可扩展架构',
    seoKeywords: '定制方案,网络解决方案,专家咨询,企业定制',
    images: ['/images/services/custom-hero.jpg'],
    tags: ['定制', '解决方案', '专家咨询'],
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    version: 1
  }
];

const BatchOperationSchema = z.object({
  action: z.enum(['activate', 'deactivate', 'delete', 'updateOrder']),
  ids: z.array(z.string()).min(1, '请选择至少一个服务'),
  data: z.record(z.any()).optional() // For additional data like new sort orders
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Simple auth check - in production, use proper JWT verification
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: '未授权访问' });
    }

    if (req.method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: `方法 ${req.method} 不被允许` });
    }

    const validatedData = BatchOperationSchema.parse(req.body);
    const { action, ids, data } = validatedData;

    switch (action) {
      case 'activate':
        return handleBatchActivate(req, res, ids);
      case 'deactivate':
        return handleBatchDeactivate(req, res, ids);
      case 'delete':
        return handleBatchDelete(req, res, ids);
      case 'updateOrder':
        return handleBatchUpdateOrder(req, res, ids, data);
      default:
        return res.status(400).json({ error: '不支持的批量操作' });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: '数据验证失败',
        details: error.errors
      });
    }
    console.error('Batch operation error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
}

async function handleBatchActivate(req: NextApiRequest, res: NextApiResponse, ids: string[]) {
  const updatedServices = [];
  const notFoundIds = [];

  for (const id of ids) {
    const serviceIndex = services.findIndex(s => s.id === id);
    if (serviceIndex !== -1) {
      services[serviceIndex] = {
        ...services[serviceIndex],
        status: 'active',
        updatedAt: new Date().toISOString(),
        version: services[serviceIndex].version + 1
      };
      updatedServices.push(services[serviceIndex]);
    } else {
      notFoundIds.push(id);
    }
  }

  return res.status(200).json({
    message: `成功启用 ${updatedServices.length} 个服务`,
    updatedServices,
    notFoundIds: notFoundIds.length > 0 ? notFoundIds : undefined
  });
}

async function handleBatchDeactivate(req: NextApiRequest, res: NextApiResponse, ids: string[]) {
  const updatedServices = [];
  const notFoundIds = [];

  for (const id of ids) {
    const serviceIndex = services.findIndex(s => s.id === id);
    if (serviceIndex !== -1) {
      services[serviceIndex] = {
        ...services[serviceIndex],
        status: 'inactive',
        updatedAt: new Date().toISOString(),
        version: services[serviceIndex].version + 1
      };
      updatedServices.push(services[serviceIndex]);
    } else {
      notFoundIds.push(id);
    }
  }

  return res.status(200).json({
    message: `成功禁用 ${updatedServices.length} 个服务`,
    updatedServices,
    notFoundIds: notFoundIds.length > 0 ? notFoundIds : undefined
  });
}

async function handleBatchDelete(req: NextApiRequest, res: NextApiResponse, ids: string[]) {
  const deletedServices = [];
  const notFoundIds = [];

  // Sort ids by index in descending order to avoid index shifting issues
  const sortedIds = ids.sort((a, b) => {
    const indexA = services.findIndex(s => s.id === a);
    const indexB = services.findIndex(s => s.id === b);
    return indexB - indexA;
  });

  for (const id of sortedIds) {
    const serviceIndex = services.findIndex(s => s.id === id);
    if (serviceIndex !== -1) {
      const deletedService = services.splice(serviceIndex, 1)[0];
      deletedServices.push(deletedService);
    } else {
      notFoundIds.push(id);
    }
  }

  return res.status(200).json({
    message: `成功删除 ${deletedServices.length} 个服务`,
    deletedServices,
    notFoundIds: notFoundIds.length > 0 ? notFoundIds : undefined
  });
}

async function handleBatchUpdateOrder(req: NextApiRequest, res: NextApiResponse, ids: string[], data: any) {
  if (!data || !data.orders) {
    return res.status(400).json({ error: '缺少排序数据' });
  }

  const updatedServices = [];
  const notFoundIds = [];

  for (const id of ids) {
    const serviceIndex = services.findIndex(s => s.id === id);
    if (serviceIndex !== -1 && data.orders[id] !== undefined) {
      services[serviceIndex] = {
        ...services[serviceIndex],
        sortOrder: data.orders[id],
        updatedAt: new Date().toISOString(),
        version: services[serviceIndex].version + 1
      };
      updatedServices.push(services[serviceIndex]);
    } else {
      notFoundIds.push(id);
    }
  }

  // Sort services by sortOrder
  services.sort((a, b) => a.sortOrder - b.sortOrder);

  return res.status(200).json({
    message: `成功更新 ${updatedServices.length} 个服务的排序`,
    updatedServices,
    notFoundIds: notFoundIds.length > 0 ? notFoundIds : undefined
  });
}
