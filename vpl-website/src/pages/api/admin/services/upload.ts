import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

// Disable body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

const UPLOAD_DIR = path.join(process.cwd(), 'public', 'uploads', 'services');
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Simple auth check - in production, use proper JWT verification
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: '未授权访问' });
    }

    if (req.method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: `方法 ${req.method} 不被允许` });
    }

    const form = formidable({
      uploadDir: UPLOAD_DIR,
      keepExtensions: true,
      maxFileSize: MAX_FILE_SIZE,
      filter: ({ mimetype }) => {
        return ALLOWED_TYPES.includes(mimetype || '');
      },
    });

    const [fields, files] = await form.parse(req);
    
    if (!files.file || !Array.isArray(files.file) || files.file.length === 0) {
      return res.status(400).json({ error: '请选择要上传的文件' });
    }

    const uploadedFiles = [];
    
    for (const file of files.file) {
      if (!file.mimetype || !ALLOWED_TYPES.includes(file.mimetype)) {
        // Clean up invalid file
        if (fs.existsSync(file.filepath)) {
          fs.unlinkSync(file.filepath);
        }
        continue;
      }

      // Generate unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const extension = path.extname(file.originalFilename || '');
      const filename = `${timestamp}_${randomString}${extension}`;
      const newPath = path.join(UPLOAD_DIR, filename);

      // Move file to final location
      fs.renameSync(file.filepath, newPath);

      // Generate public URL
      const publicUrl = `/uploads/services/${filename}`;

      uploadedFiles.push({
        filename,
        originalName: file.originalFilename,
        size: file.size,
        mimetype: file.mimetype,
        url: publicUrl
      });
    }

    if (uploadedFiles.length === 0) {
      return res.status(400).json({ error: '没有有效的图片文件被上传' });
    }

    return res.status(200).json({
      message: `成功上传 ${uploadedFiles.length} 个文件`,
      files: uploadedFiles
    });

  } catch (error) {
    console.error('Upload error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('maxFileSize')) {
        return res.status(400).json({ error: '文件大小超过限制（最大5MB）' });
      }
      if (error.message.includes('filter')) {
        return res.status(400).json({ error: '不支持的文件类型，请上传JPG、PNG、WebP或GIF格式的图片' });
      }
    }
    
    return res.status(500).json({ error: '文件上传失败' });
  }
}

// Helper function to delete uploaded files (for cleanup)
export async function deleteUploadedFile(filename: string): Promise<boolean> {
  try {
    const filePath = path.join(UPLOAD_DIR, filename);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
}
