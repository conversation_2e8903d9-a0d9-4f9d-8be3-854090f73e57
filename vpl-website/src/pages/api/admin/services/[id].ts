import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';

// Import the same mock data from index.ts (in production, this would be from database)
let services = [
  {
    id: '1',
    name: 'VPN服务',
    slug: 'vpn_services',
    title: 'VPN服务 - 安全可靠的网络连接',
    description: '军用级加密的VPN连接服务，保护您的数据安全',
    content: '<h2>VPN服务详情</h2><p>我们提供军用级加密的VPN连接服务，确保您的网络连接安全可靠。</p><h3>主要特性</h3><ul><li>AES-256军用级加密</li><li>零日志政策，保护隐私</li><li>全球50+国家服务器</li><li>无限带宽，高速连接</li><li>24/7技术支持</li></ul>',
    features: ['军用级加密', '零日志政策', '全球服务器', '无限带宽'],
    status: 'active' as const,
    sortOrder: 1,
    seoTitle: 'VPN服务 - 安全可靠的网络连接 | VPL',
    seoDescription: '军用级加密的VPN连接服务，保护您的数据安全，零日志政策，全球服务器覆盖',
    seoKeywords: 'VPN,网络安全,加密,隐私保护',
    images: ['/images/services/vpn-hero.jpg'],
    tags: ['网络安全', 'VPN', '加密'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    version: 1,
    history: [
      {
        version: 1,
        updatedAt: '2024-01-15T10:00:00Z',
        updatedBy: 'admin',
        changes: '创建服务'
      }
    ]
  },
  {
    id: '2',
    name: '跨境电商专线',
    slug: 'ecommerce_lines',
    title: '跨境电商专线 - 优化购物体验',
    description: '优化的跨境电商网络连接解决方案，提升用户购物体验',
    content: '<h2>跨境电商专线详情</h2><p>专为跨境电商设计的网络解决方案，显著提升用户购物体验。</p><h3>核心优势</h3><ul><li>多平台无缝对接</li><li>99.9%高可用性保障</li><li>智能流量优化</li><li>实时性能监控</li><li>专业技术团队支持</li></ul>',
    features: ['多平台支持', '高可用性', '流量优化', '实时监控'],
    status: 'active' as const,
    sortOrder: 2,
    seoTitle: '跨境电商专线 - 优化购物体验 | VPL',
    seoDescription: '优化的跨境电商网络连接解决方案，提升用户购物体验，支持多平台',
    seoKeywords: '跨境电商,网络专线,购物体验,电商优化',
    images: ['/images/services/ecommerce-hero.jpg'],
    tags: ['跨境电商', '网络专线', '购物优化'],
    createdAt: '2024-01-14T10:00:00Z',
    updatedAt: '2024-01-14T10:00:00Z',
    version: 1,
    history: [
      {
        version: 1,
        updatedAt: '2024-01-14T10:00:00Z',
        updatedBy: 'admin',
        changes: '创建服务'
      }
    ]
  },
  {
    id: '3',
    name: '外贸专线',
    slug: 'foreign_trade_lines',
    title: '外贸专线 - 全球贸易网络',
    description: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
    content: '<h2>外贸专线详情</h2><p>专为外贸企业设计的高速稳定网络线路，全面支持全球贸易业务。</p><h3>服务特色</h3><ul><li>专用带宽保障</li><li>全球网络覆盖</li><li>超低延迟连接</li><li>企业级技术支持</li><li>7x24小时监控</li></ul>',
    features: ['专用带宽', '全球覆盖', '低延迟', '企业级支持'],
    status: 'active' as const,
    sortOrder: 3,
    seoTitle: '外贸专线 - 全球贸易网络 | VPL',
    seoDescription: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
    seoKeywords: '外贸专线,全球贸易,网络线路,企业网络',
    images: ['/images/services/trade-hero.jpg'],
    tags: ['外贸', '专线', '全球网络'],
    createdAt: '2024-01-13T10:00:00Z',
    updatedAt: '2024-01-13T10:00:00Z',
    version: 1,
    history: [
      {
        version: 1,
        updatedAt: '2024-01-13T10:00:00Z',
        updatedBy: 'admin',
        changes: '创建服务'
      }
    ]
  },
  {
    id: '4',
    name: '定制解决方案',
    slug: 'custom_solution',
    title: '定制解决方案 - 专属网络方案',
    description: '根据您的特殊需求定制专属网络解决方案',
    content: '<h2>定制解决方案详情</h2><p>根据您的特殊需求定制专属网络解决方案，提供个性化服务。</p><h3>定制服务</h3><ul><li>个性化方案设计</li><li>专业技术咨询</li><li>可扩展系统架构</li><li>持续技术支持</li><li>定期优化升级</li></ul>',
    features: ['定制设计', '专家咨询', '可扩展架构', '持续支持'],
    status: 'active' as const,
    sortOrder: 4,
    seoTitle: '定制解决方案 - 专属网络方案 | VPL',
    seoDescription: '根据您的特殊需求定制专属网络解决方案，专家咨询，可扩展架构',
    seoKeywords: '定制方案,网络解决方案,专家咨询,企业定制',
    images: ['/images/services/custom-hero.jpg'],
    tags: ['定制', '解决方案', '专家咨询'],
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    version: 1,
    history: [
      {
        version: 1,
        updatedAt: '2024-01-12T10:00:00Z',
        updatedBy: 'admin',
        changes: '创建服务'
      }
    ]
  }
];

const UpdateServiceSchema = z.object({
  name: z.string().min(1, '服务名称不能为空').optional(),
  slug: z.string().min(1, 'URL标识不能为空').regex(/^[a-z0-9_-]+$/, 'URL标识只能包含小写字母、数字、下划线和连字符').optional(),
  title: z.string().min(1, '页面标题不能为空').optional(),
  description: z.string().min(1, '服务描述不能为空').optional(),
  content: z.string().optional(),
  features: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive', 'draft']).optional(),
  sortOrder: z.number().optional(),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.string().optional(),
  images: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Simple auth check - in production, use proper JWT verification
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: '未授权访问' });
    }

    const { id } = req.query;
    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: '无效的服务ID' });
    }

    switch (req.method) {
      case 'GET':
        return handleGet(req, res, id);
      case 'PUT':
        return handlePut(req, res, id);
      case 'DELETE':
        return handleDelete(req, res, id);
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `方法 ${req.method} 不被允许` });
    }
  } catch (error) {
    console.error('Service API error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse, id: string) {
  const { includeHistory = 'false' } = req.query;
  
  const service = services.find(s => s.id === id);
  if (!service) {
    return res.status(404).json({ error: '服务不存在' });
  }

  const responseData = { ...service };
  if (includeHistory !== 'true') {
    delete responseData.history;
  }

  return res.status(200).json({ service: responseData });
}

async function handlePut(req: NextApiRequest, res: NextApiResponse, id: string) {
  try {
    const validatedData = UpdateServiceSchema.parse(req.body);
    
    const serviceIndex = services.findIndex(s => s.id === id);
    if (serviceIndex === -1) {
      return res.status(404).json({ error: '服务不存在' });
    }

    // Check if slug already exists (excluding current service)
    if (validatedData.slug) {
      const existingService = services.find(s => s.slug === validatedData.slug && s.id !== id);
      if (existingService) {
        return res.status(400).json({ error: 'URL标识已存在，请使用其他标识' });
      }
    }

    const currentService = services[serviceIndex];
    const updatedService = {
      ...currentService,
      ...validatedData,
      updatedAt: new Date().toISOString(),
      version: currentService.version + 1,
      history: [
        ...(currentService.history || []),
        {
          version: currentService.version + 1,
          updatedAt: new Date().toISOString(),
          updatedBy: 'admin', // In production, get from JWT token
          changes: Object.keys(validatedData).join(', ')
        }
      ]
    };

    services[serviceIndex] = updatedService;

    return res.status(200).json({
      message: '服务更新成功',
      service: updatedService
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: '数据验证失败',
        details: error.errors
      });
    }
    throw error;
  }
}

async function handleDelete(req: NextApiRequest, res: NextApiResponse, id: string) {
  const serviceIndex = services.findIndex(s => s.id === id);
  if (serviceIndex === -1) {
    return res.status(404).json({ error: '服务不存在' });
  }

  const deletedService = services[serviceIndex];
  services.splice(serviceIndex, 1);

  return res.status(200).json({
    message: '服务删除成功',
    service: deletedService
  });
}
