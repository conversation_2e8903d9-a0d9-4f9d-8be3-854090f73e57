import type { NextApiRequest, NextApiResponse } from 'next';
import jwt from 'jsonwebtoken';
import fs from 'fs';
import path from 'path';

interface ExportRequest {
  format: 'csv' | 'excel';
  dateRange?: {
    start: string;
    end: string;
  };
  serviceType?: string;
  status?: string;
  fields?: string[];
}

function verifyToken(token: string): any {
  try {
    return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
  } catch (error) {
    throw new Error('Invalid token');
  }
}

function generateMockSubmissions() {
  const submissions = [];
  const serviceTypes = ['foreign_trade_lines', 'ecommerce_lines', 'vpn_services', 'custom_solution'];
  const statuses = ['pending', 'contacted', 'closed'];
  const companies = ['阿里巴巴', '腾讯科技', '百度网络', '京东商城', '华为技术', '小米科技', '字节跳动', '美团网络'];
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];

  for (let i = 0; i < 100; i++) {
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 30));
    
    submissions.push({
      id: `SUB${String(i + 1).padStart(4, '0')}`,
      companyName: companies[Math.floor(Math.random() * companies.length)] + (Math.random() > 0.7 ? '有限公司' : ''),
      contactPerson: names[Math.floor(Math.random() * names.length)],
      phone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
      email: `user${i + 1}@example.com`,
      wechat: Math.random() > 0.5 ? `wx${String(Math.floor(Math.random() * 1000000)).padStart(6, '0')}` : '',
      qq: Math.random() > 0.6 ? String(Math.floor(Math.random() * *********0) + *********) : '',
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      message: `我们公司需要${serviceTypes[Math.floor(Math.random() * serviceTypes.length)]}服务，请联系我们详细了解。`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      submittedAt: date.toISOString(),
      responseTime: Math.random() > 0.3 ? Math.floor(Math.random() * 48) + 1 : null, // hours
      notes: Math.random() > 0.7 ? '客户已联系，等待进一步沟通' : ''
    });
  }

  return submissions;
}

function convertToCSV(data: any[], fields: string[]) {
  const headers = {
    id: 'ID',
    companyName: '公司名称',
    contactPerson: '联系人',
    phone: '电话',
    email: '邮箱',
    wechat: '微信',
    qq: 'QQ',
    serviceType: '服务类型',
    message: '详细需求',
    status: '状态',
    submittedAt: '提交时间',
    responseTime: '响应时间(小时)',
    notes: '备注'
  };

  const serviceTypeNames = {
    foreign_trade_lines: '外贸专线',
    ecommerce_lines: '跨境电商专线',
    vpn_services: 'VPN服务',
    custom_solution: '定制解决方案'
  };

  const statusNames = {
    pending: '待处理',
    contacted: '已联系',
    closed: '已关闭'
  };

  // Create header row
  const headerRow = fields.map(field => headers[field as keyof typeof headers]).join(',');
  
  // Create data rows
  const dataRows = data.map(item => {
    return fields.map(field => {
      let value = item[field];
      
      // Transform values for display
      if (field === 'serviceType' && value) {
        value = serviceTypeNames[value as keyof typeof serviceTypeNames] || value;
      } else if (field === 'status' && value) {
        value = statusNames[value as keyof typeof statusNames] || value;
      } else if (field === 'submittedAt' && value) {
        value = new Date(value).toLocaleString('zh-CN');
      }
      
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        value = `"${value.replace(/"/g, '""')}"`;
      }
      
      return value || '';
    }).join(',');
  });

  return [headerRow, ...dataRows].join('\n');
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Verify authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    // Check permissions (export:submissions)
    // In a real app, you would check user permissions here

    const exportRequest: ExportRequest = req.body;
    
    if (!exportRequest.format || !['csv', 'excel'].includes(exportRequest.format)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid export format'
      });
    }

    // Get submissions data
    // In a real app, you would query your database with filters
    let submissions = generateMockSubmissions();

    // Apply filters
    if (exportRequest.dateRange) {
      const startDate = new Date(exportRequest.dateRange.start);
      const endDate = new Date(exportRequest.dateRange.end);
      submissions = submissions.filter(sub => {
        const subDate = new Date(sub.submittedAt);
        return subDate >= startDate && subDate <= endDate;
      });
    }

    if (exportRequest.serviceType) {
      submissions = submissions.filter(sub => sub.serviceType === exportRequest.serviceType);
    }

    if (exportRequest.status) {
      submissions = submissions.filter(sub => sub.status === exportRequest.status);
    }

    // Default fields to export
    const defaultFields = [
      'id', 'companyName', 'contactPerson', 'phone', 'email', 
      'serviceType', 'status', 'submittedAt'
    ];
    const fieldsToExport = exportRequest.fields || defaultFields;

    if (exportRequest.format === 'csv') {
      const csvContent = convertToCSV(submissions, fieldsToExport);
      const filename = `submissions_${new Date().toISOString().split('T')[0]}.csv`;
      
      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'));
      
      return res.status(200).send('\uFEFF' + csvContent); // Add BOM for Excel compatibility
    } else {
      // For Excel format, you would use a library like 'exceljs' or 'xlsx'
      // For now, return CSV with Excel MIME type
      const csvContent = convertToCSV(submissions, fieldsToExport);
      const filename = `submissions_${new Date().toISOString().split('T')[0]}.xlsx`;
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      return res.status(200).send('\uFEFF' + csvContent);
    }

  } catch (error) {
    console.error('Export API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}
