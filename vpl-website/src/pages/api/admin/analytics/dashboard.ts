import type { NextApiRequest, NextApiResponse } from 'next';
import jwt from 'jsonwebtoken';

interface AnalyticsData {
  totalSubmissions: number;
  todaySubmissions: number;
  weeklySubmissions: number;
  monthlySubmissions: number;
  conversionRate: number;
  averageResponseTime: number;
  topServiceTypes: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  submissionTrends: Array<{
    date: string;
    count: number;
  }>;
  statusDistribution: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  geographicDistribution: Array<{
    region: string;
    count: number;
    percentage: number;
  }>;
  timeDistribution: Array<{
    hour: number;
    count: number;
  }>;
}

interface ApiResponse {
  success: boolean;
  message: string;
  data?: AnalyticsData;
}

function verifyToken(token: string): any {
  try {
    return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
  } catch (error) {
    throw new Error('Invalid token');
  }
}

function generateMockAnalytics(): AnalyticsData {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Generate submission trends for the last 30 days
  const submissionTrends = [];
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
    const count = Math.floor(Math.random() * 20) + 5; // 5-25 submissions per day
    submissionTrends.push({
      date: date.toISOString().split('T')[0],
      count
    });
  }

  const totalSubmissions = submissionTrends.reduce((sum, day) => sum + day.count, 0);
  const todaySubmissions = submissionTrends[submissionTrends.length - 1].count;
  const weeklySubmissions = submissionTrends.slice(-7).reduce((sum, day) => sum + day.count, 0);

  return {
    totalSubmissions: totalSubmissions + 1250, // Add historical data
    todaySubmissions,
    weeklySubmissions,
    monthlySubmissions: totalSubmissions,
    conversionRate: 68.5,
    averageResponseTime: 2.3, // hours
    topServiceTypes: [
      { type: 'foreign_trade_lines', count: 456, percentage: 38.2 },
      { type: 'ecommerce_lines', count: 342, percentage: 28.6 },
      { type: 'vpn_services', count: 278, percentage: 23.3 },
      { type: 'custom_solution', count: 118, percentage: 9.9 }
    ],
    submissionTrends,
    statusDistribution: [
      { status: 'pending', count: 89, percentage: 32.1 },
      { status: 'contacted', count: 156, percentage: 56.3 },
      { status: 'closed', count: 32, percentage: 11.6 }
    ],
    geographicDistribution: [
      { region: '广东省', count: 234, percentage: 28.5 },
      { region: '上海市', count: 189, percentage: 23.0 },
      { region: '北京市', count: 156, percentage: 19.0 },
      { region: '浙江省', count: 98, percentage: 11.9 },
      { region: '江苏省', count: 87, percentage: 10.6 },
      { region: '其他', count: 58, percentage: 7.0 }
    ],
    timeDistribution: [
      { hour: 0, count: 2 }, { hour: 1, count: 1 }, { hour: 2, count: 1 },
      { hour: 3, count: 0 }, { hour: 4, count: 1 }, { hour: 5, count: 2 },
      { hour: 6, count: 5 }, { hour: 7, count: 8 }, { hour: 8, count: 15 },
      { hour: 9, count: 28 }, { hour: 10, count: 35 }, { hour: 11, count: 42 },
      { hour: 12, count: 38 }, { hour: 13, count: 45 }, { hour: 14, count: 52 },
      { hour: 15, count: 48 }, { hour: 16, count: 41 }, { hour: 17, count: 35 },
      { hour: 18, count: 28 }, { hour: 19, count: 22 }, { hour: 20, count: 18 },
      { hour: 21, count: 12 }, { hour: 22, count: 8 }, { hour: 23, count: 4 }
    ]
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Verify authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    // Check permissions (analytics:read)
    // In a real app, you would check user permissions here

    const { dateRange, serviceType, region } = req.query;

    // Generate analytics data
    // In a real app, you would query your database with filters
    const analyticsData = generateMockAnalytics();

    // Apply filters if provided
    if (serviceType && typeof serviceType === 'string') {
      // Filter data by service type
      analyticsData.topServiceTypes = analyticsData.topServiceTypes.filter(
        service => service.type === serviceType
      );
    }

    if (region && typeof region === 'string') {
      // Filter data by region
      analyticsData.geographicDistribution = analyticsData.geographicDistribution.filter(
        geo => geo.region === region
      );
    }

    return res.status(200).json({
      success: true,
      message: 'Analytics data retrieved successfully',
      data: analyticsData
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}
