import type { NextApiRequest, NextApiResponse } from 'next';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';
import { AdminUser, CreateUserRequest, UpdateUserRequest, UserResponse } from '@/types/user';

const DATA_DIR = path.join(process.cwd(), 'data');
const USERS_FILE = path.join(DATA_DIR, 'admin-users.json');

// Ensure data directory and file exist
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

if (!fs.existsSync(USERS_FILE)) {
  // Create default admin user with enhanced structure
  const defaultAdmin: AdminUser = {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: '系统管理员',
    role: {
      id: '1',
      name: 'super_admin',
      displayName: '超级管理员',
      description: '拥有所有系统权限',
      permissions: [],
      isSystem: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    permissions: [],
    isActive: true,
    loginAttempts: 0,
    twoFactorEnabled: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    // Store hashed password separately (not in the AdminUser type for security)
    password: bcrypt.hashSync('admin123', 10)
  } as any;
  fs.writeFileSync(USERS_FILE, JSON.stringify([defaultAdmin], null, 2));
}

function verifyToken(token: string): any {
  try {
    return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
  } catch (error) {
    throw new Error('Invalid token');
  }
}

function checkPermission(user: any, permission: string): boolean {
  // Super admin has all permissions
  if (user.role === 'super_admin') return true;

  // Check specific permissions
  return user.permissions?.includes(permission) || false;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<UserResponse>
) {
  try {
    // Verify authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    if (req.method === 'GET') {
      // Check read permission
      if (!checkPermission(decoded, 'users:read')) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      const { page = '1', limit = '10', search, role, status } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      const data = fs.readFileSync(USERS_FILE, 'utf8');
      let users = JSON.parse(data);

      // Apply filters
      if (search && typeof search === 'string') {
        const searchLower = search.toLowerCase();
        users = users.filter((user: any) =>
          user.username.toLowerCase().includes(searchLower) ||
          user.fullName.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower)
        );
      }

      if (role && typeof role === 'string') {
        users = users.filter((user: any) => user.role?.name === role || user.role === role);
      }

      if (status && typeof status === 'string') {
        const isActive = status === 'active';
        users = users.filter((user: any) => user.isActive === isActive);
      }

      // Remove password from response
      const safeUsers = users.map((user: any) => {
        const { password, ...safeUser } = user;
        return safeUser;
      });

      // Pagination
      const total = safeUsers.length;
      const totalPages = Math.ceil(total / limitNum);
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;
      const paginatedUsers = safeUsers.slice(startIndex, endIndex);

      return res.status(200).json({
        success: true,
        message: 'Users retrieved successfully',
        data: paginatedUsers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages
        }
      });
    } else if (req.method === 'POST') {
      // Check create permission
      if (!checkPermission(decoded, 'users:create')) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      const createRequest: CreateUserRequest = req.body;

      // Validate required fields
      if (!createRequest.username || !createRequest.email || !createRequest.fullName || !createRequest.password || !createRequest.role) {
        return res.status(400).json({
          success: false,
          message: 'Missing required fields'
        });
      }

      const data = fs.readFileSync(USERS_FILE, 'utf8');
      const users = JSON.parse(data);

      // Check if username or email already exists
      const existingUser = users.find((user: any) =>
        user.username === createRequest.username || user.email === createRequest.email
      );

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'Username or email already exists'
        });
      }

      const hashedPassword = await bcrypt.hash(createRequest.password, 10);

      const newUser: AdminUser = {
        id: Date.now().toString(),
        username: createRequest.username,
        email: createRequest.email,
        fullName: createRequest.fullName,
        role: {
          id: '2',
          name: createRequest.role,
          displayName: createRequest.role === 'admin' ? '管理员' : '用户',
          description: '',
          permissions: [],
          isSystem: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        permissions: [],
        isActive: createRequest.isActive ?? true,
        loginAttempts: 0,
        twoFactorEnabled: false,
        phone: createRequest.phone,
        department: createRequest.department,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: decoded.username,
        password: hashedPassword
      } as any;

      users.push(newUser);
      fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));

      // Remove password from response
      const { password: _, ...safeUser } = newUser;

      return res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: safeUser
      });

    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

  } catch (error) {
    console.error('Users API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}
