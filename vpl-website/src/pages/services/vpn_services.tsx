import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/layout/Layout';
import { 
  ShieldCheckIcon, 
  GlobeAltIcon, 
  ServerIcon,
  LockClosedIcon,
  EyeSlashIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

export default function VPNServices() {
  const { t } = useTranslation(['common', 'services']);

  const features = [
    {
      icon: ShieldCheckIcon,
      title: '军用级加密',
      description: 'AES-256位加密，确保数据传输绝对安全'
    },
    {
      icon: EyeSlashIcon,
      title: '零日志政策',
      description: '严格的零日志政策，保护用户隐私'
    },
    {
      icon: GlobeAltIcon,
      title: '全球服务器',
      description: '遍布全球的高速服务器节点'
    },
    {
      icon: BoltIcon,
      title: '无限带宽',
      description: '不限制带宽使用，享受高速连接'
    },
    {
      icon: ServerIcon,
      title: '多协议支持',
      description: '支持OpenVPN、IKEv2、WireGuard等协议'
    },
    {
      icon: LockClosedIcon,
      title: '安全认证',
      description: '通过国际安全标准认证'
    }
  ];

  return (
    <>
      <Head>
        <title>VPN服务 - VPL专业网络解决方案</title>
        <meta name="description" content="VPL提供军用级加密的VPN服务，采用零日志政策，全球服务器覆盖，确保您的网络连接安全可靠。" />
        <meta name="keywords" content="VPN服务,网络安全,数据加密,隐私保护,全球服务器" />
      </Head>
      
      <Layout>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-purple-50 to-indigo-100 py-20">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                VPN服务
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
                军用级加密的VPN连接服务，保护您的数据安全和网络隐私
              </p>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">服务特性</h2>
              <p className="text-lg text-gray-600">专业的VPN服务，为您提供安全可靠的网络连接</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="relative p-8 bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-6">
                      <IconComponent className="w-6 h-6 text-purple-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-purple-600">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                需要VPN服务？
              </h2>
              <p className="mt-4 text-lg text-purple-100">
                联系我们获取专业的VPN解决方案
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-purple-600 shadow-sm hover:bg-gray-50 transition-colors duration-200"
                >
                  联系我们
                </Link>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'services'])),
    },
  };
};
