import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/layout/Layout';
import { 
  CogIcon, 
  UserGroupIcon, 
  ChartBarIcon,
  PhoneIcon,
  PuzzlePieceIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline';

export default function CustomSolution() {
  const { t } = useTranslation(['common', 'services']);

  const features = [
    {
      icon: CogIcon,
      title: '定制设计',
      description: '根据企业具体需求，量身定制网络解决方案'
    },
    {
      icon: UserGroupIcon,
      title: '专家咨询',
      description: '资深网络专家提供专业咨询和技术指导'
    },
    {
      icon: PuzzlePieceIcon,
      title: '可扩展架构',
      description: '灵活的架构设计，支持业务发展和扩展需求'
    },
    {
      icon: PhoneIcon,
      title: '持续支持',
      description: '提供长期的技术支持和维护服务'
    },
    {
      icon: ChartBarIcon,
      title: '性能优化',
      description: '持续监控和优化网络性能，确保最佳体验'
    },
    {
      icon: ArrowTrendingUpIcon,
      title: '业务增长',
      description: '助力企业业务增长，提供可持续的技术支撑'
    }
  ];

  const process = [
    {
      step: '01',
      title: '需求分析',
      description: '深入了解客户业务需求和技术要求'
    },
    {
      step: '02',
      title: '方案设计',
      description: '制定专业的定制化网络解决方案'
    },
    {
      step: '03',
      title: '实施部署',
      description: '专业团队负责方案的实施和部署'
    },
    {
      step: '04',
      title: '测试验收',
      description: '全面测试确保方案满足预期要求'
    },
    {
      step: '05',
      title: '上线运行',
      description: '正式上线运行，提供稳定的网络服务'
    },
    {
      step: '06',
      title: '持续优化',
      description: '持续监控和优化，确保最佳性能'
    }
  ];

  return (
    <>
      <Head>
        <title>定制解决方案 - VPL专业网络解决方案</title>
        <meta name="description" content="VPL提供定制化网络解决方案，根据企业特殊需求量身定制，专业团队提供全程技术支持。" />
        <meta name="keywords" content="定制解决方案,网络定制,企业网络,专业咨询,技术支持" />
      </Head>
      
      <Layout>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-orange-50 to-red-100 py-20">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                定制解决方案
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
                根据您的特殊需求定制专属网络解决方案，专业团队提供全程技术支持
              </p>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">服务特性</h2>
              <p className="text-lg text-gray-600">专业的定制化网络解决方案服务</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="relative p-8 bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mb-6">
                      <IconComponent className="w-6 h-6 text-orange-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-24 bg-gray-50">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">服务流程</h2>
              <p className="text-lg text-gray-600">专业的定制化服务流程，确保项目成功实施</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {process.map((item, index) => (
                <div key={index} className="relative">
                  <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                    <div className="flex items-center mb-6">
                      <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg text-orange-600 font-bold text-lg">
                        {item.step}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 ml-4">
                        {item.title}
                      </h3>
                    </div>
                    <p className="text-gray-600">
                      {item.description}
                    </p>
                  </div>
                  {index < process.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-orange-200 transform -translate-y-1/2"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">适用行业</h2>
              <p className="text-lg text-gray-600">我们为各行各业提供专业的定制化网络解决方案</p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { name: '金融服务', icon: '🏦' },
                { name: '制造业', icon: '🏭' },
                { name: '教育机构', icon: '🎓' },
                { name: '医疗健康', icon: '🏥' },
                { name: '零售电商', icon: '🛒' },
                { name: '物流运输', icon: '🚚' },
                { name: '政府机构', icon: '🏛️' },
                { name: '科技企业', icon: '💻' }
              ].map((industry, index) => (
                <div key={index} className="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="text-4xl mb-3">{industry.icon}</div>
                  <h3 className="font-semibold text-gray-900">{industry.name}</h3>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-orange-600">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                需要定制化解决方案？
              </h2>
              <p className="mt-4 text-lg text-orange-100">
                联系我们的专家团队，获取专业的定制化网络解决方案
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-orange-600 shadow-sm hover:bg-gray-50 transition-colors duration-200"
                >
                  联系我们
                </Link>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'services'])),
    },
  };
};
