import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/layout/Layout';
import { 
  ShoppingCartIcon, 
  GlobeAltIcon, 
  ChartBarIcon,
  ClockIcon,
  CpuChipIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

export default function EcommerceLines() {
  const { t } = useTranslation(['common', 'services']);

  const features = [
    {
      icon: ShoppingCartIcon,
      title: '多平台支持',
      description: '支持Amazon、eBay、Shopify等主流电商平台'
    },
    {
      icon: ChartBarIcon,
      title: '高可用性',
      description: '99.9%的服务可用性保障，确保业务不中断'
    },
    {
      icon: CpuChipIcon,
      title: '流量优化',
      description: '智能流量优化技术，提升页面加载速度'
    },
    {
      icon: EyeIcon,
      title: '实时监控',
      description: '24/7实时监控网络状态，及时发现并解决问题'
    },
    {
      icon: GlobeAltIcon,
      title: '全球覆盖',
      description: '覆盖全球主要电商市场的网络节点'
    },
    {
      icon: ClockIcon,
      title: '低延迟',
      description: '优化的网络路由，确保最低的访问延迟'
    }
  ];

  const platforms = [
    { name: 'Amazon', logo: '🛒' },
    { name: 'eBay', logo: '🏪' },
    { name: 'Shopify', logo: '🛍️' },
    { name: 'AliExpress', logo: '📦' },
    { name: 'Wish', logo: '⭐' },
    { name: 'Etsy', logo: '🎨' }
  ];

  return (
    <>
      <Head>
        <title>跨境电商专线 - VPL专业网络解决方案</title>
        <meta name="description" content="VPL跨境电商专线服务，优化网络连接，提升用户购物体验，支持主流电商平台，确保业务稳定运行。" />
        <meta name="keywords" content="跨境电商,网络专线,电商优化,Amazon,eBay,Shopify" />
      </Head>
      
      <Layout>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-green-50 to-emerald-100 py-20">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                跨境电商专线
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
                优化的跨境电商网络连接解决方案，提升用户购物体验，助力您的电商业务全球化
              </p>
            </div>
          </div>
        </section>

        {/* Platforms Section */}
        <section className="py-16 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">支持的电商平台</h2>
              <p className="text-gray-600">为主流电商平台提供专业的网络优化服务</p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
              {platforms.map((platform, index) => (
                <div key={index} className="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="text-4xl mb-3">{platform.logo}</div>
                  <h3 className="font-semibold text-gray-900">{platform.name}</h3>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-gray-50">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">服务特性</h2>
              <p className="text-lg text-gray-600">专业的跨境电商网络优化服务</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="relative p-8 bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-6">
                      <IconComponent className="w-6 h-6 text-green-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-green-600">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                提升您的电商业务
              </h2>
              <p className="mt-4 text-lg text-green-100">
                联系我们获取专业的跨境电商网络解决方案
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-green-600 shadow-sm hover:bg-gray-50 transition-colors duration-200"
                >
                  联系我们
                </Link>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'services'])),
    },
  };
};
