import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/layout/Layout';
import { 
  GlobeAltIcon, 
  BoltIcon, 
  ShieldCheckIcon,
  ClockIcon,
  CpuChipIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

export default function ForeignTradeLines() {
  const { t } = useTranslation(['common', 'services']);

  const features = [
    {
      icon: CpuChipIcon,
      title: '专用带宽',
      description: '独享带宽资源，确保网络连接的稳定性和速度'
    },
    {
      icon: GlobeAltIcon,
      title: '全球覆盖',
      description: '覆盖全球主要贸易市场的网络节点'
    },
    {
      icon: ClockIcon,
      title: '低延迟',
      description: '优化的网络路由，确保最低的访问延迟'
    },
    {
      icon: PhoneIcon,
      title: '企业级支持',
      description: '7x24小时专业技术支持服务'
    },
    {
      icon: BoltIcon,
      title: '高速稳定',
      description: '高质量线路资源，保障业务连续性'
    },
    {
      icon: ShieldCheckIcon,
      title: '安全可靠',
      description: '多重安全保障，确保数据传输安全'
    }
  ];

  const regions = [
    { name: '北美', countries: ['美国', '加拿大'], flag: '🇺🇸' },
    { name: '欧洲', countries: ['德国', '英国', '法国'], flag: '🇪🇺' },
    { name: '亚太', countries: ['日本', '韩国', '新加坡'], flag: '🌏' },
    { name: '中东', countries: ['阿联酋', '沙特'], flag: '🕌' },
    { name: '南美', countries: ['巴西', '阿根廷'], flag: '🌎' },
    { name: '非洲', countries: ['南非', '埃及'], flag: '🌍' }
  ];

  return (
    <>
      <Head>
        <title>外贸专线 - VPL专业网络解决方案</title>
        <meta name="description" content="VPL外贸专线服务，专为外贸企业设计的高速稳定网络线路，支持全球贸易业务，确保网络连接的可靠性。" />
        <meta name="keywords" content="外贸专线,国际专线,外贸网络,全球贸易,网络连接" />
      </Head>
      
      <Layout>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-cyan-100 py-20">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                外贸专线
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
                专为外贸企业设计的高速稳定网络线路，支持全球贸易业务，确保您的国际业务顺畅运行
              </p>
            </div>
          </div>
        </section>

        {/* Global Coverage Section */}
        <section className="py-16 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">全球网络覆盖</h2>
              <p className="text-gray-600">覆盖全球主要贸易市场，为您的外贸业务提供可靠保障</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {regions.map((region, index) => (
                <div key={index} className="text-center p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                  <div className="text-4xl mb-3">{region.flag}</div>
                  <h3 className="font-semibold text-gray-900 mb-2">{region.name}</h3>
                  <p className="text-sm text-gray-600">{region.countries.join('、')}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-gray-50">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">服务特性</h2>
              <p className="text-lg text-gray-600">专业的外贸网络专线服务</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="relative p-8 bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-6">
                      <IconComponent className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">为什么选择我们的外贸专线</h2>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <ul className="space-y-6">
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-semibold text-gray-900">提升业务效率</h3>
                      <p className="text-gray-600">高速稳定的网络连接，显著提升外贸业务处理效率</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-semibold text-gray-900">降低运营成本</h3>
                      <p className="text-gray-600">专业的网络解决方案，帮助企业降低IT运营成本</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-semibold text-gray-900">增强竞争优势</h3>
                      <p className="text-gray-600">优质的网络服务，为企业在国际市场竞争中提供技术优势</p>
                    </div>
                  </li>
                </ul>
              </div>
              <div className="bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">专业服务保障</h3>
                <p className="mb-6">我们为外贸企业提供专业的网络专线服务，确保您的业务在全球范围内稳定运行。</p>
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <ShieldCheckIcon className="w-5 h-5 mr-3" />
                    <span>99.9%服务可用性保障</span>
                  </li>
                  <li className="flex items-center">
                    <PhoneIcon className="w-5 h-5 mr-3" />
                    <span>7x24小时技术支持</span>
                  </li>
                  <li className="flex items-center">
                    <GlobeAltIcon className="w-5 h-5 mr-3" />
                    <span>全球网络覆盖</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-600">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                加速您的外贸业务
              </h2>
              <p className="mt-4 text-lg text-blue-100">
                联系我们获取专业的外贸网络专线解决方案
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-blue-600 shadow-sm hover:bg-gray-50 transition-colors duration-200"
                >
                  联系我们
                </Link>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'services'])),
    },
  };
};
