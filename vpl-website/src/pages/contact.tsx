import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import Layout from '../components/layout/Layout';
import ContactInfo from '../components/contact/ContactInfo';
import EnhancedContactForm from '../components/contact/EnhancedContactForm';

export default function Contact() {
  const { t } = useTranslation(['contact', 'common']);

  return (
    <>
      <Head>
        <title>联系我们 - VPL专业网络解决方案</title>
        <meta name="description" content="联系VPL获取专业的B2B网络解决方案咨询，我们提供外贸网络线路、跨境电商外网线路、VPN服务等多种联系方式。" />
      </Head>
      
      <Layout>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                {t('contact:title')}
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
                {t('contact:subtitle')}
              </p>
            </div>
          </div>
        </section>

        {/* Contact Methods */}
        <section className="py-16 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {t('contact:contact_methods.title')}
              </h2>
            </div>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {contactMethods.map((method, index) => {
                const IconComponent = method.icon;
                return (
                  <div key={index} className="text-center p-6 bg-gray-50 rounded-lg">
                    <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 mb-4">
                      <IconComponent className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {method.title}
                    </h3>
                    <p className="text-blue-600 font-medium mb-1">
                      {method.value}
                    </p>
                    <p className="text-sm text-gray-500">
                      {method.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Contact Form and Info */}
        <section className="py-20 bg-gray-50">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-3 lg:gap-16">
              {/* Contact Form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow-sm p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    在线咨询表单
                  </h2>
                  <ContactForm />
                </div>
              </div>

              {/* Contact Information */}
              <div className="mt-12 lg:mt-0">
                <div className="bg-white rounded-lg shadow-sm p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">
                    联系信息
                  </h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start">
                      <MapPinIcon className="flex-shrink-0 h-6 w-6 text-blue-600 mt-1" />
                      <div className="ml-3">
                        <h4 className="text-base font-medium text-gray-900">公司地址</h4>
                        <p className="text-sm text-gray-600">
                          中国广东省深圳市南山区<br />
                          科技园南区软件产业基地
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <ClockIcon className="flex-shrink-0 h-6 w-6 text-blue-600 mt-1" />
                      <div className="ml-3">
                        <h4 className="text-base font-medium text-gray-900">服务时间</h4>
                        <p className="text-sm text-gray-600">
                          周一至周五：9:00 - 18:00<br />
                          周末及节假日：10:00 - 16:00<br />
                          紧急技术支持：7x24小时
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <PhoneIcon className="flex-shrink-0 h-6 w-6 text-blue-600 mt-1" />
                      <div className="ml-3">
                        <h4 className="text-base font-medium text-gray-900">联系电话</h4>
                        <p className="text-sm text-gray-600">
                          销售咨询：+86 400-xxx-xxxx<br />
                          技术支持：+86 400-xxx-xxxx<br />
                          投诉建议：+86 400-xxx-xxxx
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                    <h4 className="text-base font-medium text-blue-900 mb-2">
                      为什么选择我们？
                    </h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• 专业的技术团队支持</li>
                      <li>• 7x24小时服务保障</li>
                      <li>• 定制化解决方案</li>
                      <li>• 银行级安全保护</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                常见问题
              </h2>
              <p className="text-lg text-gray-600">
                以下是客户经常询问的问题，如有其他疑问请联系我们
              </p>
            </div>
            
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    你们的服务覆盖哪些地区？
                  </h3>
                  <p className="text-gray-600">
                    我们的服务覆盖全球主要贸易区域，包括北美、欧洲、东南亚等地区，可以为您提供稳定的国际网络连接。
                  </p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    如何保证网络连接的稳定性？
                  </h3>
                  <p className="text-gray-600">
                    我们采用多线路冗余设计，配备专业的监控系统，确保99.9%的服务可用性，并提供7x24小时技术支持。
                  </p>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    你们的加密技术安全吗？
                  </h3>
                  <p className="text-gray-600">
                    我们采用军用级AES-256加密、RSA非对称加密、TLS协议等多重安全保障，确保您的数据传输绝对安全。
                  </p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    如何开始使用你们的服务？
                  </h3>
                  <p className="text-gray-600">
                    您可以通过填写上方的咨询表单或直接联系我们的销售团队，我们会根据您的需求提供最适合的解决方案。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'zh', ['contact', 'common'])),
    },
  };
};
