import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { 
  ServerIcon,
  CircleStackIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  ClockIcon,
  CpuChipIcon,
  ArrowRightOnRectangleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface SystemInfo {
  nodeVersion: string;
  platform: string;
  uptime: number;
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  diskUsage: {
    total: number;
    used: number;
    free: number;
  };
  lastBackup?: string;
  cacheSize: number;
  logSize: number;
}

export default function SystemMaintenance() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [isPerformingAction, setIsPerformingAction] = useState<string | null>(null);
  const [actionResults, setActionResults] = useState<{ [key: string]: any }>({});
  const router = useRouter();

  useEffect(() => {
    checkAuthentication();
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      fetchSystemInfo();
    }
  }, [isAuthenticated]);

  const checkAuthentication = () => {
    const token = localStorage.getItem('adminToken');
    if (!token) {
      router.push('/admin/login');
      return;
    }

    fetch('/api/admin/verify', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    .then(response => {
      if (response.ok) {
        setIsAuthenticated(true);
      } else {
        localStorage.removeItem('adminToken');
        router.push('/admin/login');
      }
    })
    .catch(() => {
      localStorage.removeItem('adminToken');
      router.push('/admin/login');
    })
    .finally(() => {
      setIsLoading(false);
    });
  };

  const fetchSystemInfo = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/system/maintenance', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const result = await response.json();
        setSystemInfo(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch system info:', error);
    }
  };

  const performMaintenanceAction = async (action: string, options?: any) => {
    setIsPerformingAction(action);
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/system/maintenance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ action, options })
      });

      const result = await response.json();

      if (response.ok) {
        setActionResults(prev => ({
          ...prev,
          [action]: { success: true, data: result.data, message: result.message }
        }));
        
        // Refresh system info after action
        fetchSystemInfo();
      } else {
        setActionResults(prev => ({
          ...prev,
          [action]: { success: false, message: result.message }
        }));
      }
    } catch (error) {
      setActionResults(prev => ({
        ...prev,
        [action]: { success: false, message: '操作失败' }
      }));
    } finally {
      setIsPerformingAction(null);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    router.push('/admin/login');
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>系统维护 - VPL管理后台</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3">
                  <span className="text-white font-bold text-lg">VPL</span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900">系统维护</h1>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/admin/dashboard')}
                  className="text-gray-700 hover:text-gray-900"
                >
                  返回仪表板
                </button>
                <button
                  onClick={handleLogout}
                  className="flex items-center text-gray-700 hover:text-gray-900"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5 mr-1" />
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {/* System Information */}
          {systemInfo && (
            <div className="mb-8">
              <h2 className="text-lg font-medium text-gray-900 mb-4">系统信息</h2>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ServerIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">系统运行时间</dt>
                          <dd className="text-lg font-medium text-gray-900">{formatUptime(systemInfo.uptime)}</dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <CpuChipIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">内存使用</dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {formatBytes(systemInfo.memoryUsage.heapUsed)} / {formatBytes(systemInfo.memoryUsage.heapTotal)}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <CircleStackIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">磁盘使用</dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {Math.round((systemInfo.diskUsage.used / systemInfo.diskUsage.total) * 100)}%
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ClockIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">最后备份</dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {systemInfo.lastBackup ? new Date(systemInfo.lastBackup).toLocaleDateString('zh-CN') : '无'}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Maintenance Actions */}
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">维护操作</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {/* Database Backup */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <DocumentArrowDownIcon className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="ml-5 flex-1">
                      <h3 className="text-lg font-medium text-gray-900">数据备份</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        创建完整的数据库和文件备份
                      </p>
                    </div>
                  </div>
                  
                  {actionResults.backup && (
                    <div className={`mt-4 p-3 rounded-md ${
                      actionResults.backup.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                    }`}>
                      <div className="flex">
                        <div className="flex-shrink-0">
                          {actionResults.backup.success ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-400" />
                          ) : (
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                          )}
                        </div>
                        <div className="ml-3">
                          <p className={`text-sm ${
                            actionResults.backup.success ? 'text-green-800' : 'text-red-800'
                          }`}>
                            {actionResults.backup.message}
                          </p>
                          {actionResults.backup.success && actionResults.backup.data && (
                            <p className="text-sm text-green-700 mt-1">
                              备份大小: {actionResults.backup.data.size}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="mt-6">
                    <button
                      onClick={() => performMaintenanceAction('backup', { includeDatabase: true, includeUploads: true })}
                      disabled={isPerformingAction === 'backup'}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isPerformingAction === 'backup' ? '备份中...' : '开始备份'}
                    </button>
                  </div>
                </div>
              </div>

              {/* Clear Cache */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <TrashIcon className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div className="ml-5 flex-1">
                      <h3 className="text-lg font-medium text-gray-900">清理缓存</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        清理系统缓存以释放空间
                      </p>
                      {systemInfo && (
                        <p className="mt-1 text-sm text-gray-400">
                          当前缓存: {formatBytes(systemInfo.cacheSize)}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {actionResults.clear_cache && (
                    <div className={`mt-4 p-3 rounded-md ${
                      actionResults.clear_cache.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                    }`}>
                      <div className="flex">
                        <div className="flex-shrink-0">
                          {actionResults.clear_cache.success ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-400" />
                          ) : (
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                          )}
                        </div>
                        <div className="ml-3">
                          <p className={`text-sm ${
                            actionResults.clear_cache.success ? 'text-green-800' : 'text-red-800'
                          }`}>
                            {actionResults.clear_cache.message}
                          </p>
                          {actionResults.clear_cache.success && actionResults.clear_cache.data && (
                            <p className="text-sm text-green-700 mt-1">
                              已清理: {actionResults.clear_cache.data.clearedSize}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="mt-6">
                    <button
                      onClick={() => performMaintenanceAction('clear_cache')}
                      disabled={isPerformingAction === 'clear_cache'}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isPerformingAction === 'clear_cache' ? '清理中...' : '清理缓存'}
                    </button>
                  </div>
                </div>
              </div>

              {/* Clear Logs */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <DocumentArrowDownIcon className="h-8 w-8 text-red-600" />
                    </div>
                    <div className="ml-5 flex-1">
                      <h3 className="text-lg font-medium text-gray-900">清理日志</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        清理旧的系统日志文件
                      </p>
                      {systemInfo && (
                        <p className="mt-1 text-sm text-gray-400">
                          当前日志: {formatBytes(systemInfo.logSize)}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {actionResults.clear_logs && (
                    <div className={`mt-4 p-3 rounded-md ${
                      actionResults.clear_logs.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                    }`}>
                      <div className="flex">
                        <div className="flex-shrink-0">
                          {actionResults.clear_logs.success ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-400" />
                          ) : (
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                          )}
                        </div>
                        <div className="ml-3">
                          <p className={`text-sm ${
                            actionResults.clear_logs.success ? 'text-green-800' : 'text-red-800'
                          }`}>
                            {actionResults.clear_logs.message}
                          </p>
                          {actionResults.clear_logs.success && actionResults.clear_logs.data && (
                            <p className="text-sm text-green-700 mt-1">
                              已清理: {actionResults.clear_logs.data.clearedSize}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="mt-6">
                    <button
                      onClick={() => performMaintenanceAction('clear_logs', { logLevel: 'info' })}
                      disabled={isPerformingAction === 'clear_logs'}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isPerformingAction === 'clear_logs' ? '清理中...' : '清理日志'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* System Details */}
          {systemInfo && (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">系统详细信息</h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">服务器和运行环境的详细信息</p>
              </div>
              <div className="border-t border-gray-200">
                <dl>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Node.js 版本</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{systemInfo.nodeVersion}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">操作系统</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{systemInfo.platform}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">内存使用详情</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <div className="space-y-1">
                        <div>RSS: {formatBytes(systemInfo.memoryUsage.rss)}</div>
                        <div>Heap Total: {formatBytes(systemInfo.memoryUsage.heapTotal)}</div>
                        <div>Heap Used: {formatBytes(systemInfo.memoryUsage.heapUsed)}</div>
                        <div>External: {formatBytes(systemInfo.memoryUsage.external)}</div>
                      </div>
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">磁盘空间</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <div className="space-y-1">
                        <div>总容量: {formatBytes(systemInfo.diskUsage.total)}</div>
                        <div>已使用: {formatBytes(systemInfo.diskUsage.used)}</div>
                        <div>可用空间: {formatBytes(systemInfo.diskUsage.free)}</div>
                      </div>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
