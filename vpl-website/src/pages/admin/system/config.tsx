import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useForm } from 'react-hook-form';
import { 
  CogIcon, 
  EnvelopeIcon, 
  ShieldCheckIcon, 
  GlobeAltIcon,
  DocumentTextIcon,
  CheckIcon,
  XMarkIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import { SystemConfig } from '@/types/system';

interface ConfigFormData {
  [key: string]: string | number | boolean;
}

const configCategories = [
  { key: 'general', label: '基本设置', icon: CogIcon },
  { key: 'email', label: '邮件配置', icon: EnvelopeIcon },
  { key: 'form', label: '表单设置', icon: DocumentTextIcon },
  { key: 'security', label: '安全设置', icon: ShieldCheckIcon },
  { key: 'localization', label: '多语言', icon: GlobeAltIcon },
];

export default function SystemConfig() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState('general');
  const [configs, setConfigs] = useState<SystemConfig[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const router = useRouter();

  const { register, handleSubmit, reset, formState: { errors, isDirty } } = useForm<ConfigFormData>();

  useEffect(() => {
    checkAuthentication();
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      fetchConfigs();
    }
  }, [isAuthenticated, activeCategory]);

  const checkAuthentication = () => {
    const token = localStorage.getItem('adminToken');
    if (!token) {
      router.push('/admin/login');
      return;
    }

    fetch('/api/admin/verify', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    .then(response => {
      if (response.ok) {
        setIsAuthenticated(true);
      } else {
        localStorage.removeItem('adminToken');
        router.push('/admin/login');
      }
    })
    .catch(() => {
      localStorage.removeItem('adminToken');
      router.push('/admin/login');
    })
    .finally(() => {
      setIsLoading(false);
    });
  };

  const fetchConfigs = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/system/config?category=${activeCategory}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const result = await response.json();
        setConfigs(Array.isArray(result.data) ? result.data : []);
        
        // Reset form with current values
        const formData: ConfigFormData = {};
        result.data?.forEach((config: SystemConfig) => {
          formData[config.key] = config.value;
        });
        reset(formData);
      }
    } catch (error) {
      console.error('Failed to fetch configs:', error);
    }
  };

  const onSubmit = async (data: ConfigFormData) => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      const token = localStorage.getItem('adminToken');
      const configUpdates = configs.map(config => ({
        key: config.key,
        value: data[config.key],
        category: config.category
      }));

      const response = await fetch('/api/admin/system/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ configs: configUpdates })
      });

      const result = await response.json();

      if (response.ok) {
        setSaveMessage({ type: 'success', text: '配置保存成功！' });
        fetchConfigs(); // Refresh data
      } else {
        setSaveMessage({ type: 'error', text: result.message || '保存失败' });
      }
    } catch (error) {
      setSaveMessage({ type: 'error', text: '网络错误，请重试' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    router.push('/admin/login');
  };

  const renderConfigField = (config: SystemConfig) => {
    const commonProps = {
      ...register(config.key, { 
        required: config.required ? `${config.label}是必填项` : false,
        min: config.validation?.min ? { value: config.validation.min, message: `最小值为 ${config.validation.min}` } : undefined,
        max: config.validation?.max ? { value: config.validation.max, message: `最大值为 ${config.validation.max}` } : undefined,
        pattern: config.validation?.pattern ? { value: new RegExp(config.validation.pattern), message: '格式不正确' } : undefined
      }),
      className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
    };

    switch (config.type) {
      case 'boolean':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              {...register(config.key)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-600">{config.description}</span>
          </div>
        );
      
      case 'number':
        return (
          <input
            type="number"
            {...commonProps}
            min={config.validation?.min}
            max={config.validation?.max}
          />
        );
      
      case 'password':
        return (
          <input
            type="password"
            {...commonProps}
            placeholder="••••••••"
          />
        );
      
      default:
        return (
          <input
            type={config.type === 'email' ? 'email' : 'text'}
            {...commonProps}
          />
        );
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>系统配置 - VPL管理后台</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3">
                  <span className="text-white font-bold text-lg">VPL</span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900">系统配置</h1>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/admin/dashboard')}
                  className="text-gray-700 hover:text-gray-900"
                >
                  返回仪表板
                </button>
                <button
                  onClick={handleLogout}
                  className="flex items-center text-gray-700 hover:text-gray-900"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5 mr-1" />
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="lg:grid lg:grid-cols-12 lg:gap-x-5">
            {/* Sidebar */}
            <aside className="py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3">
              <nav className="space-y-1">
                {configCategories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <button
                      key={category.key}
                      onClick={() => setActiveCategory(category.key)}
                      className={`${
                        activeCategory === category.key
                          ? 'bg-blue-50 border-blue-500 text-blue-700'
                          : 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900'
                      } group border-l-4 px-3 py-2 flex items-center text-sm font-medium w-full text-left`}
                    >
                      <Icon className="text-gray-400 group-hover:text-gray-500 flex-shrink-0 -ml-1 mr-3 h-6 w-6" />
                      <span className="truncate">{category.label}</span>
                    </button>
                  );
                })}
              </nav>
            </aside>

            {/* Main content */}
            <div className="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="shadow sm:rounded-md sm:overflow-hidden">
                  <div className="bg-white py-6 px-4 space-y-6 sm:p-6">
                    <div>
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {configCategories.find(c => c.key === activeCategory)?.label}
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        配置系统的基本参数和设置
                      </p>
                    </div>

                    {/* Save Message */}
                    {saveMessage && (
                      <div className={`rounded-md p-4 ${
                        saveMessage.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                      }`}>
                        <div className="flex">
                          <div className="flex-shrink-0">
                            {saveMessage.type === 'success' ? (
                              <CheckIcon className="h-5 w-5 text-green-400" />
                            ) : (
                              <XMarkIcon className="h-5 w-5 text-red-400" />
                            )}
                          </div>
                          <div className="ml-3">
                            <p className={`text-sm font-medium ${
                              saveMessage.type === 'success' ? 'text-green-800' : 'text-red-800'
                            }`}>
                              {saveMessage.text}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Config Fields */}
                    <div className="grid grid-cols-1 gap-6">
                      {configs.map((config) => (
                        <div key={config.key}>
                          <label htmlFor={config.key} className="block text-sm font-medium text-gray-700">
                            {config.label}
                            {config.required && <span className="text-red-500 ml-1">*</span>}
                          </label>
                          {renderConfigField(config)}
                          {config.description && config.type !== 'boolean' && (
                            <p className="mt-1 text-sm text-gray-500">{config.description}</p>
                          )}
                          {errors[config.key] && (
                            <p className="mt-1 text-sm text-red-600">{errors[config.key]?.message}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                    <button
                      type="submit"
                      disabled={isSaving || !isDirty}
                      className="bg-blue-600 border border-transparent rounded-md shadow-sm py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSaving ? '保存中...' : '保存配置'}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
