import { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';
import {
  CogIcon,
  PencilIcon,
  EyeIcon,
  PlusIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowsUpDownIcon,
  CheckIcon,
  XMarkIcon,
  PhotoIcon,
  DocumentDuplicateIcon,
  ClockIcon,
  TagIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';
import ServiceEditor from '../../../components/admin/ServiceEditor';
import DragDropList from '../../../components/admin/DragDropList';

interface ServiceContent {
  id: string;
  name: string;
  slug: string;
  title: string;
  description: string;
  content?: string;
  features: string[];
  status: 'active' | 'inactive' | 'draft';
  sortOrder: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  images: string[];
  tags: string[];
  createdAt: string;
  updatedAt: string;
  version: number;
}

export default function ServicesContent() {
  const [services, setServices] = useState<ServiceContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedService, setSelectedService] = useState<ServiceContent | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('sortOrder');
  const [sortOrder, setSortOrder] = useState<string>('asc');
  const [showBatchActions, setShowBatchActions] = useState(false);
  const [isPerformingBatch, setIsPerformingBatch] = useState(false);

  // Fetch services from API
  const fetchServices = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/services?search=${searchTerm}&status=${statusFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}`, {
        headers: {
          'Authorization': 'Bearer mock-token' // In production, use real JWT token
        }
      });

      if (response.ok) {
        const data = await response.json();
        setServices(data.services);
      } else {
        console.error('Failed to fetch services');
      }
    } catch (error) {
      console.error('Error fetching services:', error);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, statusFilter, sortBy, sortOrder]);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  // CRUD Operations
  const handleEdit = (service: ServiceContent) => {
    setSelectedService(service);
    setIsEditing(true);
  };

  const handleCreate = () => {
    setSelectedService(null);
    setIsEditing(true);
  };

  const handleSave = async (serviceData: Partial<ServiceContent>) => {
    try {
      const isUpdate = selectedService?.id;
      const url = isUpdate ? `/api/admin/services/${selectedService.id}` : '/api/admin/services';
      const method = isUpdate ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer mock-token'
        },
        body: JSON.stringify(serviceData)
      });

      if (response.ok) {
        await fetchServices(); // Refresh the list
        setIsEditing(false);
        setSelectedService(null);
        alert(isUpdate ? '服务更新成功' : '服务创建成功');
      } else {
        const error = await response.json();
        alert(`操作失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Save error:', error);
      alert('操作失败，请稍后重试');
    }
  };

  const handleDelete = async (serviceId: string) => {
    if (!confirm('确定要删除这个服务吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/services/${serviceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': 'Bearer mock-token'
        }
      });

      if (response.ok) {
        await fetchServices();
        alert('服务删除成功');
      } else {
        const error = await response.json();
        alert(`删除失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Delete error:', error);
      alert('删除失败，请稍后重试');
    }
  };

  const toggleStatus = async (serviceId: string, newStatus: 'active' | 'inactive') => {
    try {
      const response = await fetch(`/api/admin/services/${serviceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer mock-token'
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (response.ok) {
        await fetchServices();
      } else {
        const error = await response.json();
        alert(`状态更新失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Status toggle error:', error);
      alert('状态更新失败，请稍后重试');
    }
  };

  // Batch Operations
  const handleBatchAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedIds.length === 0) {
      alert('请先选择要操作的服务');
      return;
    }

    const actionText = action === 'activate' ? '启用' : action === 'deactivate' ? '禁用' : '删除';
    if (!confirm(`确定要${actionText}选中的 ${selectedIds.length} 个服务吗？`)) {
      return;
    }

    try {
      setIsPerformingBatch(true);
      const response = await fetch('/api/admin/services/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer mock-token'
        },
        body: JSON.stringify({
          action,
          ids: selectedIds
        })
      });

      if (response.ok) {
        await fetchServices();
        setSelectedIds([]);
        setShowBatchActions(false);
        const result = await response.json();
        alert(result.message);
      } else {
        const error = await response.json();
        alert(`批量操作失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Batch operation error:', error);
      alert('批量操作失败，请稍后重试');
    } finally {
      setIsPerformingBatch(false);
    }
  };

  // Drag and Drop
  const handleReorder = async (reorderedServices: ServiceContent[]) => {
    // Update local state immediately for better UX
    setServices(reorderedServices);

    // Update sort orders
    const orders: { [key: string]: number } = {};
    reorderedServices.forEach((item, index) => {
      orders[item.id] = index + 1;
    });

    try {
      const response = await fetch('/api/admin/services/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer mock-token'
        },
        body: JSON.stringify({
          action: 'updateOrder',
          ids: reorderedServices.map(item => item.id),
          data: { orders }
        })
      });

      if (!response.ok) {
        // Revert on error
        await fetchServices();
        const error = await response.json();
        alert(`排序更新失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Drag and drop error:', error);
      await fetchServices(); // Revert on error
      alert('排序更新失败，请稍后重试');
    }
  };

  // Selection handlers
  const handleSelectAll = () => {
    if (selectedIds.length === services.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(services.map(s => s.id));
    }
  };

  const handleSelectService = (serviceId: string) => {
    setSelectedIds(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <>
      <Head>
        <title>服务内容管理 - VPL后台管理系统</title>
        <meta name="description" content="管理网站服务内容" />
      </Head>
      
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center">
              <CogIcon className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">服务内容管理</h1>
                <p className="text-gray-600">管理网站上的服务内容和描述</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleCreate}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                添加服务
              </button>
            </div>
          </div>

          {/* Search and Filter Bar */}
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索服务名称、描述或标签..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">所有状态</option>
                  <option value="active">已启用</option>
                  <option value="inactive">已禁用</option>
                  <option value="draft">草稿</option>
                </select>
              </div>

              {/* Sort */}
              <div className="sm:w-48">
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-');
                    setSortBy(field);
                    setSortOrder(order);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="sortOrder-asc">排序 (升序)</option>
                  <option value="sortOrder-desc">排序 (降序)</option>
                  <option value="name-asc">名称 (A-Z)</option>
                  <option value="name-desc">名称 (Z-A)</option>
                  <option value="updatedAt-desc">最近更新</option>
                  <option value="createdAt-desc">最近创建</option>
                </select>
              </div>
            </div>
          </div>

          {/* Batch Actions Bar */}
          {selectedIds.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-sm font-medium text-blue-900">
                    已选择 {selectedIds.length} 个服务
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleBatchAction('activate')}
                    disabled={isPerformingBatch}
                    className="px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    批量启用
                  </button>
                  <button
                    onClick={() => handleBatchAction('deactivate')}
                    disabled={isPerformingBatch}
                    className="px-3 py-1.5 text-xs font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:opacity-50"
                  >
                    批量禁用
                  </button>
                  <button
                    onClick={() => handleBatchAction('delete')}
                    disabled={isPerformingBatch}
                    className="px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    批量删除
                  </button>
                  <button
                    onClick={() => setSelectedIds([])}
                    className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    取消选择
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Services List with Drag and Drop */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* List Header */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedIds.length === services.length && services.length > 0}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">全选</span>
                  </label>
                  <span className="text-sm text-gray-500">
                    共 {services.length} 个服务
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <ArrowsUpDownIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-xs text-gray-500">拖拽排序</span>
                </div>
              </div>
            </div>

            {/* Draggable Services List */}
            <DragDropList
              items={services}
              onReorder={handleReorder}
              keyExtractor={(service) => service.id}
              renderItem={(service, index, isDragging) => (
                <div className={`border-b border-gray-200 last:border-b-0 ${
                  isDragging ? 'bg-blue-50 shadow-lg' : 'bg-white'
                }`}>
                  <div className="px-6 py-4">
                    <div className="flex items-center space-x-4">
                      {/* Drag Handle */}
                      <div className="cursor-move">
                        <Bars3Icon className="h-5 w-5 text-gray-400" />
                      </div>

                      {/* Checkbox */}
                      <input
                        type="checkbox"
                        checked={selectedIds.includes(service.id)}
                        onChange={() => handleSelectService(service.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />

                      {/* Service Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div>
                              <h3 className="text-sm font-semibold text-gray-900">
                                {service.name}
                              </h3>
                              <p className="text-xs text-gray-500">
                                /{service.slug}
                              </p>
                            </div>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              service.status === 'active'
                                ? 'bg-green-100 text-green-800'
                                : service.status === 'inactive'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {service.status === 'active' ? '已启用' : service.status === 'inactive' ? '已禁用' : '草稿'}
                            </span>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleEdit(service)}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              <PencilIcon className="h-3 w-3 mr-1" />
                              编辑
                            </button>
                            <button
                              onClick={() => window.open(`/services/${service.slug}`, '_blank')}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                              <EyeIcon className="h-3 w-3 mr-1" />
                              预览
                            </button>
                            <button
                              onClick={() => toggleStatus(service.id, service.status === 'active' ? 'inactive' : 'active')}
                              className={`inline-flex items-center px-2 py-1 text-xs font-medium border rounded focus:outline-none focus:ring-2 ${
                                service.status === 'active'
                                  ? 'text-red-700 bg-red-100 border-red-300 hover:bg-red-200 focus:ring-red-500'
                                  : 'text-green-700 bg-green-100 border-green-300 hover:bg-green-200 focus:ring-green-500'
                              }`}
                            >
                              {service.status === 'active' ? '禁用' : '启用'}
                            </button>
                            <button
                              onClick={() => handleDelete(service.id)}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
                            >
                              <TrashIcon className="h-3 w-3 mr-1" />
                              删除
                            </button>
                          </div>
                        </div>

                        <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                          {service.description}
                        </p>

                        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                          <span className="flex items-center">
                            <ClockIcon className="h-3 w-3 mr-1" />
                            {new Date(service.updatedAt).toLocaleDateString()}
                          </span>
                          <span className="flex items-center">
                            <TagIcon className="h-3 w-3 mr-1" />
                            {service.features.length} 个特性
                          </span>
                          {service.images.length > 0 && (
                            <span className="flex items-center">
                              <PhotoIcon className="h-3 w-3 mr-1" />
                              {service.images.length} 张图片
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            />
          </div>

          {/* Empty State */}
          {services.length === 0 && !loading && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="text-center py-12">
                <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">暂无服务</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || statusFilter !== 'all'
                    ? '没有找到匹配的服务，请尝试调整搜索条件'
                    : '开始添加您的第一个服务内容'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && (
                  <div className="mt-6">
                    <button
                      onClick={handleCreate}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      添加服务
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Service Editor Modal */}
        {isEditing && (
          <ServiceEditor
            service={selectedService}
            onSave={handleSave}
            onCancel={() => {
              setIsEditing(false);
              setSelectedService(null);
            }}
          />
        )}
      </AdminLayout>
    </>
  );
}
