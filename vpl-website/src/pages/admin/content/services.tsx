import { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';
import { 
  CogIcon,
  PencilIcon,
  EyeIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

interface ServiceContent {
  id: string;
  name: string;
  slug: string;
  title: string;
  description: string;
  features: string[];
  status: 'active' | 'inactive';
  lastModified: string;
}

export default function ServicesContent() {
  const [services, setServices] = useState<ServiceContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedService, setSelectedService] = useState<ServiceContent | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockServices: ServiceContent[] = [
      {
        id: '1',
        name: 'VPN服务',
        slug: 'vpn_services',
        title: 'VPN服务 - 安全可靠的网络连接',
        description: '军用级加密的VPN连接服务，保护您的数据安全',
        features: ['军用级加密', '零日志政策', '全球服务器', '无限带宽'],
        status: 'active',
        lastModified: '2024-01-15'
      },
      {
        id: '2',
        name: '跨境电商专线',
        slug: 'ecommerce_lines',
        title: '跨境电商专线 - 优化购物体验',
        description: '优化的跨境电商网络连接解决方案，提升用户购物体验',
        features: ['多平台支持', '高可用性', '流量优化', '实时监控'],
        status: 'active',
        lastModified: '2024-01-14'
      },
      {
        id: '3',
        name: '外贸专线',
        slug: 'foreign_trade_lines',
        title: '外贸专线 - 全球贸易网络',
        description: '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
        features: ['专用带宽', '全球覆盖', '低延迟', '企业级支持'],
        status: 'active',
        lastModified: '2024-01-13'
      },
      {
        id: '4',
        name: '定制解决方案',
        slug: 'custom_solution',
        title: '定制解决方案 - 专属网络方案',
        description: '根据您的特殊需求定制专属网络解决方案',
        features: ['定制设计', '专家咨询', '可扩展架构', '持续支持'],
        status: 'active',
        lastModified: '2024-01-12'
      }
    ];

    setTimeout(() => {
      setServices(mockServices);
      setLoading(false);
    }, 1000);
  }, []);

  const handleEdit = (service: ServiceContent) => {
    setSelectedService(service);
    setIsEditing(true);
  };

  const handleSave = async (updatedService: ServiceContent) => {
    // In real app, this would make API call
    setServices(prev => 
      prev.map(s => s.id === updatedService.id ? updatedService : s)
    );
    setIsEditing(false);
    setSelectedService(null);
  };

  const handleDelete = async (serviceId: string) => {
    if (confirm('确定要删除这个服务吗？')) {
      // In real app, this would make API call
      setServices(prev => prev.filter(s => s.id !== serviceId));
    }
  };

  const toggleStatus = async (serviceId: string) => {
    // In real app, this would make API call
    setServices(prev => 
      prev.map(s => 
        s.id === serviceId 
          ? { ...s, status: s.status === 'active' ? 'inactive' : 'active' }
          : s
      )
    );
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <>
      <Head>
        <title>服务内容管理 - VPL后台管理系统</title>
        <meta name="description" content="管理网站服务内容" />
      </Head>
      
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CogIcon className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">服务内容管理</h1>
                <p className="text-gray-600">管理网站上的服务内容和描述</p>
              </div>
            </div>
            <button
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              添加服务
            </button>
          </div>

          {/* Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service) => (
              <div key={service.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      service.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {service.status === 'active' ? '已启用' : '已禁用'}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">{service.description}</p>
                  
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">特色功能</h4>
                    <div className="flex flex-wrap gap-1">
                      {service.features.slice(0, 3).map((feature, index) => (
                        <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          {feature}
                        </span>
                      ))}
                      {service.features.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600">
                          +{service.features.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 mb-4">
                    最后修改: {service.lastModified}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(service)}
                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <PencilIcon className="h-3 w-3 mr-1" />
                        编辑
                      </button>
                      <button
                        onClick={() => window.open(`/services/${service.slug}`, '_blank')}
                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <EyeIcon className="h-3 w-3 mr-1" />
                        预览
                      </button>
                    </div>
                    
                    <div className="flex space-x-2">
                      <button
                        onClick={() => toggleStatus(service.id)}
                        className={`inline-flex items-center px-3 py-1.5 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                          service.status === 'active'
                            ? 'border-red-300 text-red-700 bg-white hover:bg-red-50 focus:ring-red-500'
                            : 'border-green-300 text-green-700 bg-white hover:bg-green-50 focus:ring-green-500'
                        }`}
                      >
                        {service.status === 'active' ? '禁用' : '启用'}
                      </button>
                      <button
                        onClick={() => handleDelete(service.id)}
                        className="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        <TrashIcon className="h-3 w-3 mr-1" />
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty State */}
          {services.length === 0 && (
            <div className="text-center py-12">
              <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无服务</h3>
              <p className="mt-1 text-sm text-gray-500">开始添加您的第一个服务内容</p>
              <div className="mt-6">
                <button
                  onClick={() => setIsEditing(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  添加服务
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Edit Modal - Placeholder */}
        {isEditing && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {selectedService ? '编辑服务' : '添加服务'}
                </h3>
                <p className="text-gray-600 mb-4">
                  服务编辑功能正在开发中...
                </p>
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => {
                      setIsEditing(false);
                      setSelectedService(null);
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    取消
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
}
