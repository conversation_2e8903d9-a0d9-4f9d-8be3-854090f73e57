import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { 
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { ContactSubmission } from '../../../types/admin';

export default function InquiryDetailPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [submission, setSubmission] = useState<ContactSubmission | null>(null);
  const [notes, setNotes] = useState('');
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const router = useRouter();
  const { id } = router.query;

  useEffect(() => {
    checkAuthentication();
  }, []);

  useEffect(() => {
    if (isAuthenticated && id) {
      fetchSubmission();
    }
  }, [isAuthenticated, id]);

  const checkAuthentication = () => {
    const token = localStorage.getItem('adminToken');
    if (!token) {
      router.push('/admin/login');
      return;
    }
    setIsAuthenticated(true);
    setIsLoading(false);
  };

  const fetchSubmission = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/submissions/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSubmission(data.data);
        setNotes(data.data.notes || '');
      } else {
        router.push('/admin/inquiries');
      }
    } catch (error) {
      console.error('Failed to fetch submission:', error);
      router.push('/admin/inquiries');
    }
  };

  const updateSubmission = async (updates: Partial<ContactSubmission>) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/submissions/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        fetchSubmission();
      }
    } catch (error) {
      console.error('Failed to update submission:', error);
    }
  };

  const handleStatusChange = (status: string) => {
    updateSubmission({ status: status as any });
  };

  const handlePriorityChange = (priority: string) => {
    updateSubmission({ priority: priority as any });
  };

  const handleNotesUpdate = () => {
    updateSubmission({ notes });
    setIsEditingNotes(false);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },
      contacted: { color: 'bg-blue-100 text-blue-800', text: '已联系' },
      'in-progress': { color: 'bg-purple-100 text-purple-800', text: '处理中' },
      completed: { color: 'bg-green-100 text-green-800', text: '已完成' },
      closed: { color: 'bg-gray-100 text-gray-800', text: '已关闭' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', text: '低' },
      medium: { color: 'bg-blue-100 text-blue-800', text: '中' },
      high: { color: 'bg-orange-100 text-orange-800', text: '高' },
      urgent: { color: 'bg-red-100 text-red-800', text: '紧急' },
    };
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getServiceTypeName = (serviceType: string) => {
    const types: { [key: string]: string } = {
      'foreign_trade_lines': '外贸网络线路',
      'ecommerce_lines': '跨境电商线路',
      'vpn_services': 'VPN服务',
      'custom_solution': '定制解决方案',
    };
    return types[serviceType] || serviceType;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated || !submission) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{`咨询详情 - ${submission.companyName} - VPL后台管理系统`}</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/admin/inquiries')}
                  className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-md"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">咨询详情</h1>
                  <p className="text-gray-600">{submission.companyName}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push(`/admin/inquiries/${id}/edit`)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  编辑
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Info */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">基本信息</h3>
                </div>
                <div className="px-6 py-4">
                  <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">公司名称</dt>
                      <dd className="mt-1 text-sm text-gray-900">{submission.companyName}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">联系人</dt>
                      <dd className="mt-1 text-sm text-gray-900">{submission.contactPerson}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">服务类型</dt>
                      <dd className="mt-1 text-sm text-gray-900">{getServiceTypeName(submission.serviceType)}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">提交时间</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {new Date(submission.submittedAt).toLocaleString('zh-CN')}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">联系方式</h3>
                </div>
                <div className="px-6 py-4">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <PhoneIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-900">{submission.phone}</span>
                      <a
                        href={`tel:${submission.phone}`}
                        className="ml-auto text-blue-600 hover:text-blue-800 text-sm"
                      >
                        拨打电话
                      </a>
                    </div>
                    <div className="flex items-center">
                      <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-900">{submission.email}</span>
                      <a
                        href={`mailto:${submission.email}`}
                        className="ml-auto text-blue-600 hover:text-blue-800 text-sm"
                      >
                        发送邮件
                      </a>
                    </div>
                    {submission.wechat && (
                      <div className="flex items-center">
                        <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-500">微信：</span>
                        <span className="text-sm text-gray-900 ml-1">{submission.wechat}</span>
                      </div>
                    )}
                    {submission.qq && (
                      <div className="flex items-center">
                        <UserIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-500">QQ：</span>
                        <span className="text-sm text-gray-900 ml-1">{submission.qq}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Message */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">详细需求</h3>
                </div>
                <div className="px-6 py-4">
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">{submission.message}</p>
                </div>
              </div>

              {/* Notes */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium text-gray-900">备注</h3>
                    {!isEditingNotes && (
                      <button
                        onClick={() => setIsEditingNotes(true)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        编辑
                      </button>
                    )}
                  </div>
                </div>
                <div className="px-6 py-4">
                  {isEditingNotes ? (
                    <div>
                      <textarea
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        rows={4}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="添加备注..."
                      />
                      <div className="mt-3 flex justify-end space-x-3">
                        <button
                          onClick={() => {
                            setNotes(submission.notes || '');
                            setIsEditingNotes(false);
                          }}
                          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                          取消
                        </button>
                        <button
                          onClick={handleNotesUpdate}
                          className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                        >
                          保存
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">
                      {submission.notes || '暂无备注'}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Status and Priority */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">状态管理</h3>
                </div>
                <div className="px-6 py-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">状态</label>
                    <select
                      value={submission.status}
                      onChange={(e) => handleStatusChange(e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="pending">待处理</option>
                      <option value="contacted">已联系</option>
                      <option value="in-progress">处理中</option>
                      <option value="completed">已完成</option>
                      <option value="closed">已关闭</option>
                    </select>
                    <div className="mt-2">
                      {getStatusBadge(submission.status)}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                    <select
                      value={submission.priority}
                      onChange={(e) => handlePriorityChange(e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="low">低</option>
                      <option value="medium">中</option>
                      <option value="high">高</option>
                      <option value="urgent">紧急</option>
                    </select>
                    <div className="mt-2">
                      {getPriorityBadge(submission.priority)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">时间线</h3>
                </div>
                <div className="px-6 py-4">
                  <div className="space-y-3">
                    <div className="flex items-center text-sm">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-500">提交时间：</span>
                      <span className="text-gray-900 ml-1">
                        {new Date(submission.submittedAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                    {submission.lastContactedAt && (
                      <div className="flex items-center text-sm">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-500">最后联系：</span>
                        <span className="text-gray-900 ml-1">
                          {new Date(submission.lastContactedAt).toLocaleString('zh-CN')}
                        </span>
                      </div>
                    )}
                    {submission.followUpDate && (
                      <div className="flex items-center text-sm">
                        <TagIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-500">跟进日期：</span>
                        <span className="text-gray-900 ml-1">
                          {new Date(submission.followUpDate).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">快速操作</h3>
                </div>
                <div className="px-6 py-4 space-y-3">
                  <button
                    onClick={() => handleStatusChange('contacted')}
                    className="w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md"
                  >
                    标记为已联系
                  </button>
                  <button
                    onClick={() => handleStatusChange('completed')}
                    className="w-full text-left px-3 py-2 text-sm text-green-600 hover:bg-green-50 rounded-md"
                  >
                    标记为已完成
                  </button>
                  <button
                    onClick={() => updateSubmission({ followUpDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() })}
                    className="w-full text-left px-3 py-2 text-sm text-orange-600 hover:bg-orange-50 rounded-md"
                  >
                    设置7天后跟进
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
