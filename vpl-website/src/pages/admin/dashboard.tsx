import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import {
  ChartBarIcon,
  EnvelopeIcon,
  UsersIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  BellIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';
import NotificationCenter from '../../components/admin/NotificationCenter';

interface DashboardStats {
  totalInquiries: number;
  todayInquiries: number;
  pendingInquiries: number;
  totalUsers: number;
}

interface ContactSubmission {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  serviceType: string;
  message: string;
  submittedAt: string;
  status: 'pending' | 'contacted' | 'closed';
}

export default function AdminDashboard() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalInquiries: 0,
    todayInquiries: 0,
    pendingInquiries: 0,
    totalUsers: 0,
  });
  const [recentSubmissions, setRecentSubmissions] = useState<ContactSubmission[]>([]);
  const router = useRouter();

  useEffect(() => {
    checkAuthentication();
    if (isAuthenticated) {
      fetchDashboardData();
    }
  }, [isAuthenticated]);

  const checkAuthentication = () => {
    const token = localStorage.getItem('adminToken');
    if (!token) {
      router.push('/admin/login');
      return;
    }

    // Verify token with backend
    fetch('/api/admin/verify', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (response.ok) {
        setIsAuthenticated(true);
      } else {
        localStorage.removeItem('adminToken');
        router.push('/admin/login');
      }
    })
    .catch(() => {
      localStorage.removeItem('adminToken');
      router.push('/admin/login');
    })
    .finally(() => {
      setIsLoading(false);
    });
  };

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
        setRecentSubmissions(data.recentSubmissions);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    router.push('/admin/login');
  };

  const getServiceTypeName = (serviceType: string) => {
    const types: { [key: string]: string } = {
      'foreign_trade_lines': '外贸网络线路',
      'ecommerce_lines': '跨境电商外网线路',
      'vpn_services': 'VPN服务',
      'custom_solution': '定制解决方案',
    };
    return types[serviceType] || serviceType;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待处理' },
      contacted: { color: 'bg-blue-100 text-blue-800', text: '已联系' },
      closed: { color: 'bg-green-100 text-green-800', text: '已完成' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>管理后台 - VPL后台管理系统</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg mr-3">
                  <span className="text-white font-bold text-lg">VPL</span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900">管理后台</h1>
              </div>
              <div className="flex items-center space-x-4">
                <NotificationCenter />
                <button
                  onClick={handleLogout}
                  className="flex items-center text-gray-700 hover:text-gray-900"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5 mr-1" />
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {/* Stats */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <EnvelopeIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">总咨询数</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalInquiries}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">今日咨询</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.todayInquiries}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BellIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">待处理</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.pendingInquiries}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UsersIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalUsers}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Access */}
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">快速访问</h2>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <Link
                href="/admin/users"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                    <UsersIcon className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    <span className="absolute inset-0" />
                    用户管理
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    管理管理员账户、角色权限和登录安全
                  </p>
                </div>
                <span className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400">
                  <ArrowTopRightOnSquareIcon className="h-6 w-6" />
                </span>
              </Link>

              <Link
                href="/admin/analytics"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                    <ChartBarIcon className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    <span className="absolute inset-0" />
                    数据分析
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    查看咨询统计、转化率和业务报表
                  </p>
                </div>
                <span className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400">
                  <ArrowTopRightOnSquareIcon className="h-6 w-6" />
                </span>
              </Link>

              <Link
                href="/admin/system/config"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                    <CogIcon className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    <span className="absolute inset-0" />
                    系统配置
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    配置网站基本信息、邮件服务和安全设置
                  </p>
                </div>
                <span className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400">
                  <ArrowTopRightOnSquareIcon className="h-6 w-6" />
                </span>
              </Link>

              <Link
                href="/admin/content/services"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-orange-50 text-orange-700 ring-4 ring-white">
                    <DocumentTextIcon className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    <span className="absolute inset-0" />
                    内容管理
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    管理服务类型、页面内容和多语言文本
                  </p>
                </div>
                <span className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400">
                  <ArrowTopRightOnSquareIcon className="h-6 w-6" />
                </span>
              </Link>
            </div>
          </div>

          {/* Recent Submissions */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">最近的咨询</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">最新提交的客户咨询信息</p>
            </div>
            <ul className="divide-y divide-gray-200">
              {recentSubmissions.map((submission) => (
                <li key={submission.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-medium text-sm">
                              {submission.companyName.charAt(0)}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="flex items-center">
                            <p className="text-sm font-medium text-gray-900">
                              {submission.companyName}
                            </p>
                            <span className="ml-2">
                              {getStatusBadge(submission.status)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">
                            {submission.contactPerson} • {submission.email}
                          </p>
                          <p className="text-sm text-gray-500">
                            {getServiceTypeName(submission.serviceType)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <p className="text-sm text-gray-500">
                          {new Date(submission.submittedAt).toLocaleDateString('zh-CN')}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {submission.message}
                      </p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
            {recentSubmissions.length === 0 && (
              <div className="px-4 py-8 text-center">
                <p className="text-gray-500">暂无咨询记录</p>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-3">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <EnvelopeIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">管理咨询</h3>
                    <p className="text-sm text-gray-500">查看和处理客户咨询</p>
                  </div>
                </div>
                <div className="mt-3">
                  <button className="text-blue-600 hover:text-blue-500 text-sm font-medium">
                    查看全部 →
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CogIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">系统设置</h3>
                    <p className="text-sm text-gray-500">配置邮件和系统参数</p>
                  </div>
                </div>
                <div className="mt-3">
                  <button className="text-blue-600 hover:text-blue-500 text-sm font-medium">
                    进入设置 →
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">数据报告</h3>
                    <p className="text-sm text-gray-500">查看业务数据和统计</p>
                  </div>
                </div>
                <div className="mt-3">
                  <button className="text-blue-600 hover:text-blue-500 text-sm font-medium">
                    查看报告 →
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8 bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <button
                onClick={() => router.push('/admin/inquiries')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors"
              >
                <div className="text-sm font-medium text-gray-900">客户咨询管理</div>
                <div className="text-sm text-gray-500">查看和管理所有客户咨询</div>
              </button>
              <button
                onClick={() => router.push('/admin/analytics')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors"
              >
                <div className="text-sm font-medium text-gray-900">数据分析</div>
                <div className="text-sm text-gray-500">查看统计数据和趋势</div>
              </button>
              <button
                onClick={() => router.push('/admin/email-settings')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors"
              >
                <div className="text-sm font-medium text-gray-900">邮件配置</div>
                <div className="text-sm text-gray-500">配置SMTP服务器设置</div>
              </button>
              <button
                onClick={() => router.push('/admin/users')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors"
              >
                <div className="text-sm font-medium text-gray-900">用户管理</div>
                <div className="text-sm text-gray-500">管理管理员账户</div>
              </button>
              <button
                onClick={() => router.push('/admin/settings')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors"
              >
                <div className="text-sm font-medium text-gray-900">系统设置</div>
                <div className="text-sm text-gray-500">网站和系统配置</div>
              </button>
              <button
                onClick={() => router.push('/admin/logs')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left transition-colors"
              >
                <div className="text-sm font-medium text-gray-900">系统日志</div>
                <div className="text-sm text-gray-500">查看系统运行日志</div>
              </button>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
