import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import Layout from '../components/layout/Layout';
import HeroSection from '../components/home/<USER>';
import ServicesSection from '../components/home/<USER>';
import StatsSection from '../components/home/<USER>';

export default function Home() {
  const { t } = useTranslation(['common', 'home']);

  return (
    <>
      <Head>
        <title>{`VPL - ${t('brand.tagline')}`}</title>
        <meta name="description" content={t('home:hero.subtitle')} />
        <meta name="keywords" content="外贸网络线路,跨境电商,VPN服务,网络加密,AES加密,RSA加密,TLS加密" />
      </Head>

      <Layout>
        <HeroSection />
        <ServicesSection />
        <StatsSection />
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'home'])),
    },
  };
};
