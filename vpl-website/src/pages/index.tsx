import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../components/layout/Layout';

export default function Home() {
  const { t } = useTranslation(['common', 'home']);

  return (
    <>
      <Head>
        <title>VPL - 专业外贸网络线路与VPN服务</title>
        <meta name="description" content="VPL专业提供外贸网络线路、跨境电商外网线路、VPN服务，采用AES、RSA、TLS等多重加密技术，确保您的业务安全稳定运行。" />
        <meta name="keywords" content="外贸网络线路,跨境电商,VPN服务,网络加密,AES加密,RSA加密,TLS加密" />
      </Head>
      
      <Layout>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 min-h-screen flex items-center justify-center">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/50 to-transparent">
            <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10"></div>
          </div>
          
          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="max-w-4xl mx-auto">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100/20 text-blue-100 border border-blue-300/30 backdrop-blur-sm mb-8">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                全球领先的网络解决方案提供商
              </span>

              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
                <span className="block">VPL专业网络</span>
                <span className="block bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent">
                  解决方案
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
                为您的跨境电商业务提供安全、稳定、高速的网络线路和VPN服务
              </p>

              <p className="text-lg text-blue-200/80 mb-12 max-w-2xl mx-auto">
                我们专注于为外贸企业提供专业的网络线路服务，包括跨境电商外网线路、VPN服务等，确保您的业务安全稳定运行。
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                <Link
                  href="/contact"
                  className="group inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  立即开始
                  <svg className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>
                
                <button className="group inline-flex items-center px-8 py-4 text-lg font-semibold text-white border-2 border-white/30 rounded-full hover:border-white/60 hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                  <svg className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  观看演示
                </button>
              </div>

              <div className="flex flex-wrap justify-center items-center gap-8 text-blue-200/60">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="text-sm font-medium">军用级加密</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                  <span className="text-sm font-medium">99.9%可靠性</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                  <span className="text-sm font-medium">专业团队</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-24 bg-gradient-to-b from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4">
                核心服务
              </span>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                专业网络解决方案
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                为不同行业和需求提供定制化的网络服务，确保您的业务在全球范围内稳定运行
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  title: "外贸专线",
                  description: "专为外贸企业设计的高速稳定网络线路，支持全球贸易业务",
                  color: "from-blue-500 to-cyan-500",
                  bgColor: "bg-blue-50",
                  iconColor: "text-blue-600"
                },
                {
                  title: "跨境电商专线",
                  description: "优化的跨境电商网络连接解决方案，提升用户购物体验",
                  color: "from-green-500 to-emerald-500",
                  bgColor: "bg-green-50",
                  iconColor: "text-green-600"
                },
                {
                  title: "VPN服务",
                  description: "军用级加密的VPN连接服务，保护您的数据安全",
                  color: "from-purple-500 to-indigo-500",
                  bgColor: "bg-purple-50",
                  iconColor: "text-purple-600"
                },
                {
                  title: "定制解决方案",
                  description: "根据您的特殊需求定制专属网络解决方案",
                  color: "from-orange-500 to-red-500",
                  bgColor: "bg-orange-50",
                  iconColor: "text-orange-600"
                }
              ].map((service, index) => (
                <div key={index} className="group relative">
                  <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden">
                    <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                    
                    <div className={`inline-flex items-center justify-center w-16 h-16 ${service.bgColor} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <div className={`h-8 w-8 ${service.iconColor} rounded`}></div>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {service.description}
                    </p>

                    <Link
                      href="/contact"
                      className={`inline-flex items-center text-sm font-semibold bg-gradient-to-r ${service.color} bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300`}
                    >
                      了解更多
                      <svg className="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </Link>

                    <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${service.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left`} />
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-16">
              <p className="text-lg text-gray-600 mb-8">
                需要定制化解决方案？我们的专家团队随时为您服务
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                联系我们
                <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-600">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                准备开始了吗？
              </h2>
              <p className="mt-4 text-lg text-blue-100">
                联系我们获取专业的网络解决方案
              </p>
              <p className="mt-2 text-base text-blue-200">
                我们的专业团队将为您提供最适合的网络服务方案
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-md bg-white px-6 py-3 text-base font-semibold text-blue-600 shadow-sm hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors duration-200"
                >
                  联系我们
                </Link>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'zh', ['common', 'home'])),
    },
  };
};
