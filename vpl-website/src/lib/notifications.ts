import { io, Socket } from 'socket.io-client';

class NotificationService {
  private socket: Socket | null = null;
  private isConnected = false;
  private connectionAttempts = 0;
  private maxRetries = 3;

  connect() {
    if (typeof window !== 'undefined' && !this.socket && this.connectionAttempts < this.maxRetries) {
      try {
        this.connectionAttempts++;
        console.log(`Attempting to connect to Socket.IO server (attempt ${this.connectionAttempts})`);

        this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {
          path: '/api/socket',
          transports: ['polling', 'websocket'],
          timeout: 5000,
          forceNew: true,
        });

        this.socket.on('connect', () => {
          console.log('Connected to notification service');
          this.isConnected = true;
          this.connectionAttempts = 0; // Reset on successful connection
        });

        this.socket.on('disconnect', () => {
          console.log('Disconnected from notification service');
          this.isConnected = false;
        });

        this.socket.on('connect_error', (error) => {
          console.warn('Socket.IO connection error:', error.message);
          this.isConnected = false;

          if (this.connectionAttempts >= this.maxRetries) {
            console.warn('Max connection attempts reached. Socket.IO features will be disabled.');
          }
        });

        this.socket.on('admin-joined', (data) => {
          console.log('Successfully joined admin room:', data);
        });

      } catch (error) {
        console.error('Failed to initialize Socket.IO:', error);
        this.isConnected = false;
      }
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  joinAdminRoom() {
    if (this.socket && this.isConnected) {
      this.socket.emit('join-admin');
    } else {
      console.warn('Cannot join admin room: Socket.IO not connected');
    }
  }

  onNewSubmission(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('new-submission', callback);
    } else {
      console.warn('Cannot listen for new submissions: Socket.IO not available');
    }
  }

  offNewSubmission(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.off('new-submission', callback);
    }
  }

  emitNewSubmission(data: any) {
    if (this.socket && this.isConnected) {
      this.socket.emit('new-submission', data);
    } else {
      console.warn('Cannot emit new submission: Socket.IO not connected');
    }
  }

  // Admin notification methods
  notifyAdmins(type: string, data: any) {
    if (this.socket && this.isConnected) {
      this.socket.to('admin').emit('admin-notification', {
        type,
        data,
        timestamp: new Date().toISOString(),
      });
    } else {
      console.warn('Cannot notify admins: Socket.IO not connected');
    }
  }

  onAdminNotification(callback: (notification: any) => void) {
    if (this.socket) {
      this.socket.on('admin-notification', callback);
    } else {
      console.warn('Cannot listen for admin notifications: Socket.IO not available');
    }
  }

  offAdminNotification(callback: (notification: any) => void) {
    if (this.socket) {
      this.socket.off('admin-notification', callback);
    }
  }

  // Check if service is available
  isAvailable(): boolean {
    return this.socket !== null && this.isConnected;
  }

  // Get connection status
  getStatus(): string {
    if (!this.socket) return 'not-initialized';
    if (this.isConnected) return 'connected';
    return 'disconnected';
  }
}

export const notificationService = new NotificationService();

// Browser notification utilities
export const requestNotificationPermission = async (): Promise<boolean> => {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission === 'denied') {
    return false;
  }

  const permission = await Notification.requestPermission();
  return permission === 'granted';
};

export const showBrowserNotification = (title: string, options?: NotificationOptions) => {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return;
  }

  if (Notification.permission === 'granted') {
    new Notification(title, {
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      ...options,
    });
  }
};

export default NotificationService;
