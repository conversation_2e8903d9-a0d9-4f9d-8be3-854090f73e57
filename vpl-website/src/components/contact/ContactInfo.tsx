'use client';

import { useTranslation } from 'next-i18next';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon, 
  ClockIcon,
  GlobeAltIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

const contactMethods = [
  {
    id: 'phone',
    icon: PhoneIcon,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    value: '+86 ************',
    description: '24/7 技术支持热线'
  },
  {
    id: 'email',
    icon: EnvelopeIcon,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    value: '<EMAIL>',
    description: '商务合作邮箱'
  },
  {
    id: 'wechat',
    icon: ChatBubbleLeftRightIcon,
    color: 'text-emerald-600',
    bgColor: 'bg-emerald-100',
    value: 'VPL-Service',
    description: '微信客服'
  },
  {
    id: 'qq',
    icon: ChatBubbleLeftRightIcon,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    value: '888999000',
    description: 'QQ客服群'
  }
];

const offices = [
  {
    id: 'beijing',
    city: '北京总部',
    address: '北京市朝阳区建国门外大街1号国贸大厦A座2801室',
    phone: '+86 10-8888-9999',
    timezone: 'GMT+8'
  },
  {
    id: 'shanghai',
    city: '上海分公司',
    address: '上海市浦东新区陆家嘴环路1000号恒生银行大厦30楼',
    phone: '+86 21-6666-8888',
    timezone: 'GMT+8'
  },
  {
    id: 'shenzhen',
    city: '深圳分公司',
    address: '深圳市南山区科技园南区高新南一道6号TCL大厦B座15楼',
    phone: '+86 755-8888-6666',
    timezone: 'GMT+8'
  }
];

export default function ContactInfo() {
  const { t } = useTranslation(['contact', 'common']);

  return (
    <div className="space-y-12">
      {/* Contact Methods */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-8">联系方式</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {contactMethods.map((method) => {
            const Icon = method.icon;
            return (
              <div key={method.id} className="group">
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                  <div className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-12 h-12 ${method.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`h-6 w-6 ${method.color}`} />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">
                        {method.value}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {method.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Office Locations */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-8">办公地址</h3>
        <div className="space-y-6">
          {offices.map((office) => (
            <div key={office.id} className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MapPinIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    {office.city}
                  </h4>
                  <p className="text-gray-600 mb-2">
                    {office.address}
                  </p>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <PhoneIcon className="h-4 w-4 mr-1" />
                      {office.phone}
                    </div>
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-4 w-4 mr-1" />
                      {office.timezone}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Business Hours */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-8">服务时间</h3>
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <ClockIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">营业时间</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">技术支持</h5>
                  <p className="text-gray-600 text-sm">24小时 × 7天</p>
                  <p className="text-gray-500 text-xs">全年无休技术支持</p>
                </div>
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">商务咨询</h5>
                  <p className="text-gray-600 text-sm">周一至周五 9:00-18:00</p>
                  <p className="text-gray-500 text-xs">北京时间 (GMT+8)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Service Commitment */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-8">服务承诺</h3>
        <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">15</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">分钟响应</h4>
              <p className="text-gray-600 text-sm">技术问题快速响应</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">99.9</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">% 可用性</h4>
              <p className="text-gray-600 text-sm">服务稳定性保障</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">24</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">小时监控</h4>
              <p className="text-gray-600 text-sm">全天候网络监控</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
