'use client';

import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  PaperAirplaneIcon, 
  DocumentArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const contactSchema = z.object({
  name: z.string().min(2, '姓名至少需要2个字符'),
  company: z.string().min(1, '请填写公司名称'),
  email: z.string().email('请输入有效的邮箱地址'),
  phone: z.string().min(10, '请输入有效的电话号码'),
  serviceType: z.string().min(1, '请选择服务类型'),
  urgency: z.string().min(1, '请选择紧急程度'),
  budget: z.string().optional(),
  message: z.string().min(10, '需求描述至少需要10个字符'),
  verificationCode: z.string().min(4, '请输入验证码'),
  agreement: z.boolean().refine(val => val === true, '请同意服务条款')
});

type ContactFormData = z.infer<typeof contactSchema>;

const serviceTypes = [
  { value: 'foreign_trade_lines', label: '外贸专线' },
  { value: 'ecommerce_lines', label: '跨境电商专线' },
  { value: 'vpn_services', label: 'VPN服务' },
  { value: 'custom_solution', label: '定制解决方案' },
  { value: 'consultation', label: '技术咨询' }
];

const urgencyLevels = [
  { value: 'low', label: '一般（7个工作日内）' },
  { value: 'medium', label: '较急（3个工作日内）' },
  { value: 'high', label: '紧急（24小时内）' },
  { value: 'critical', label: '非常紧急（立即响应）' }
];

const budgetRanges = [
  { value: 'under_10k', label: '1万元以下' },
  { value: '10k_50k', label: '1-5万元' },
  { value: '50k_100k', label: '5-10万元' },
  { value: '100k_500k', label: '10-50万元' },
  { value: 'over_500k', label: '50万元以上' },
  { value: 'discuss', label: '面议' }
];

export default function EnhancedContactForm() {
  const { t } = useTranslation(['contact', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [verificationCode, setVerificationCode] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema)
  });

  const generateVerificationCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setVerificationCode(result);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setUploadedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: ContactFormData) => {
    if (data.verificationCode !== verificationCode) {
      alert('验证码错误，请重新输入');
      return;
    }

    setIsSubmitting(true);
    try {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value.toString());
      });
      
      uploadedFiles.forEach((file, index) => {
        formData.append(`file_${index}`, file);
      });

      const response = await fetch('/api/contact', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        setSubmitStatus('success');
        reset();
        setUploadedFiles([]);
        generateVerificationCode();
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate initial verification code
  useState(() => {
    generateVerificationCode();
  });

  if (submitStatus === 'success') {
    return (
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">提交成功！</h3>
          <p className="text-gray-600 mb-6">
            感谢您的咨询，我们已收到您的需求。我们的专业团队将在24小时内与您联系。
          </p>
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <p className="text-blue-800 text-sm">
              <strong>下一步：</strong>我们的技术专家将根据您的需求准备详细的解决方案，并通过您提供的联系方式与您沟通。
            </p>
          </div>
          <button
            onClick={() => setSubmitStatus('idle')}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            提交新的咨询
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
      <div className="mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">专业咨询</h3>
        <p className="text-gray-600">
          请详细填写您的需求，我们的专家团队将为您提供最适合的解决方案
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              姓名 <span className="text-red-500">*</span>
            </label>
            <input
              {...register('name')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
              placeholder="请输入您的姓名"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              公司名称 <span className="text-red-500">*</span>
            </label>
            <input
              {...register('company')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
              placeholder="请输入公司名称"
            />
            {errors.company && (
              <p className="mt-1 text-sm text-red-600">{errors.company.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址 <span className="text-red-500">*</span>
            </label>
            <input
              {...register('email')}
              type="email"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
              placeholder="请输入邮箱地址"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              联系电话 <span className="text-red-500">*</span>
            </label>
            <input
              {...register('phone')}
              type="tel"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
              placeholder="请输入联系电话"
            />
            {errors.phone && (
              <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
            )}
          </div>
        </div>

        {/* Service Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              服务类型 <span className="text-red-500">*</span>
            </label>
            <select
              {...register('serviceType')}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
            >
              <option value="">请选择服务类型</option>
              {serviceTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {errors.serviceType && (
              <p className="mt-1 text-sm text-red-600">{errors.serviceType.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              紧急程度 <span className="text-red-500">*</span>
            </label>
            <select
              {...register('urgency')}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
            >
              <option value="">请选择紧急程度</option>
              {urgencyLevels.map((level) => (
                <option key={level.value} value={level.value}>
                  {level.label}
                </option>
              ))}
            </select>
            {errors.urgency && (
              <p className="mt-1 text-sm text-red-600">{errors.urgency.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            预算范围
          </label>
          <select
            {...register('budget')}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
          >
            <option value="">请选择预算范围（可选）</option>
            {budgetRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
        </div>

        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            需求描述 <span className="text-red-500">*</span>
          </label>
          <textarea
            {...register('message')}
            rows={6}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
            placeholder="请详细描述您的需求，包括业务场景、技术要求、预期目标等..."
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
          )}
        </div>

        {/* File Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            附件上传
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors duration-200">
            <DocumentArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">点击上传或拖拽文件到此处</p>
            <p className="text-sm text-gray-500">支持 PDF, DOC, DOCX, XLS, XLSX 格式，单个文件不超过10MB</p>
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.xls,.xlsx"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
            />
            <label
              htmlFor="file-upload"
              className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 cursor-pointer transition-colors duration-200"
            >
              选择文件
            </label>
          </div>
          
          {uploadedFiles.length > 0 && (
            <div className="mt-4 space-y-2">
              {uploadedFiles.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                  <span className="text-sm text-gray-700">{file.name}</span>
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    删除
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Verification Code */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              验证码 <span className="text-red-500">*</span>
            </label>
            <input
              {...register('verificationCode')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
              placeholder="请输入验证码"
            />
            {errors.verificationCode && (
              <p className="mt-1 text-sm text-red-600">{errors.verificationCode.message}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              &nbsp;
            </label>
            <div className="flex items-center space-x-3">
              <div className="bg-gray-100 px-4 py-3 rounded-lg font-mono text-lg tracking-wider border">
                {verificationCode}
              </div>
              <button
                type="button"
                onClick={generateVerificationCode}
                className="px-4 py-3 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                刷新
              </button>
            </div>
          </div>
        </div>

        {/* Agreement */}
        <div className="flex items-start space-x-3">
          <input
            {...register('agreement')}
            type="checkbox"
            className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label className="text-sm text-gray-700">
            我已阅读并同意 <a href="/privacy" className="text-blue-600 hover:text-blue-700">隐私政策</a> 和 <a href="/terms" className="text-blue-600 hover:text-blue-700">服务条款</a>
            <span className="text-red-500 ml-1">*</span>
          </label>
        </div>
        {errors.agreement && (
          <p className="text-sm text-red-600">{errors.agreement.message}</p>
        )}

        {/* Submit Button */}
        <div className="pt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                提交中...
              </>
            ) : (
              <>
                <PaperAirplaneIcon className="h-5 w-5 mr-2" />
                提交咨询
              </>
            )}
          </button>
        </div>

        {submitStatus === 'error' && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-3 mt-0.5" />
              <div>
                <h4 className="text-red-800 font-medium">提交失败</h4>
                <p className="text-red-700 text-sm mt-1">
                  抱歉，提交过程中出现了问题。请检查网络连接后重试，或直接联系我们的客服。
                </p>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
