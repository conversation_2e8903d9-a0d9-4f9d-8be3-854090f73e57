'use client';

import { useTranslation } from 'next-i18next';
import CountUpNumber from '../ui/CountUpNumber';
import AnimatedSection from '../ui/AnimatedSection';
import { 
  UsersIcon, 
  GlobeAltIcon, 
  ClockIcon, 
  ShieldCheckIcon,
  TrophyIcon,
  ServerIcon
} from '@heroicons/react/24/outline';

const stats = [
  {
    id: 'clients',
    icon: UsersIcon,
    value: 5000,
    suffix: '+',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100'
  },
  {
    id: 'countries',
    icon: GlobeAltIcon,
    value: 120,
    suffix: '+',
    color: 'text-green-600',
    bgColor: 'bg-green-100'
  },
  {
    id: 'uptime',
    icon: ClockIcon,
    value: 99.9,
    suffix: '%',
    color: 'text-purple-600',
    bgColor: 'bg-purple-100'
  },
  {
    id: 'experience',
    icon: TrophyIcon,
    value: 15,
    suffix: '+',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100'
  },
  {
    id: 'servers',
    icon: ServerIcon,
    value: 200,
    suffix: '+',
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100'
  },
  {
    id: 'security',
    icon: ShieldCheckIcon,
    value: 100,
    suffix: '%',
    color: 'text-red-600',
    bgColor: 'bg-red-100'
  }
];

export default function StatsSection() {
  const { t } = useTranslation(['home']);

  return (
    <section className="py-24 bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10"></div>
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/50 to-transparent"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-blue-100/20 text-blue-100 rounded-full text-sm font-semibold mb-4 backdrop-blur-sm border border-blue-300/30">
            {t('home:stats.badge')}
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('home:stats.title')}
          </h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            {t('home:stats.subtitle')}
          </p>
        </AnimatedSection>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <AnimatedSection
                key={stat.id}
                delay={index * 0.1}
                className="text-center group"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-2xl">
                  {/* Icon */}
                  <div className={`inline-flex items-center justify-center w-16 h-16 ${stat.bgColor} rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className={`h-8 w-8 ${stat.color}`} />
                  </div>

                  {/* Number */}
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    <CountUpNumber
                      end={stat.value}
                      suffix={stat.suffix}
                      duration={2500}
                    />
                  </div>

                  {/* Label */}
                  <p className="text-blue-100 font-medium">
                    {t(`home:stats.items.${stat.id}.label`)}
                  </p>

                  {/* Description */}
                  <p className="text-blue-200/70 text-sm mt-2">
                    {t(`home:stats.items.${stat.id}.description`)}
                  </p>
                </div>
              </AnimatedSection>
            );
          })}
        </div>

        {/* Bottom Text */}
        <AnimatedSection delay={0.6} className="text-center mt-16">
          <p className="text-lg text-blue-100 max-w-4xl mx-auto leading-relaxed">
            {t('home:stats.bottom_text')}
          </p>
        </AnimatedSection>
      </div>
    </section>
  );
}
