'use client';

import { motion } from 'framer-motion';
import { useTranslation } from 'next-i18next';
import AnimatedSection from '../ui/AnimatedSection';
import { StarIcon, QuoteIcon } from '@heroicons/react/24/solid';

// Mock client logos and testimonials
const clients = [
  { name: '<PERSON><PERSON><PERSON>', logo: '/images/clients/alibaba.svg' },
  { name: 'Tencent', logo: '/images/clients/tencent.svg' },
  { name: '<PERSON><PERSON><PERSON>', logo: '/images/clients/huawei.svg' },
  { name: '<PERSON><PERSON>', logo: '/images/clients/xiaomi.svg' },
  { name: 'ByteDance', logo: '/images/clients/bytedance.svg' },
  { name: '<PERSON><PERSON>', logo: '/images/clients/baidu.svg' },
  { name: 'JD.com', logo: '/images/clients/jd.svg' },
  { name: '<PERSON><PERSON><PERSON>', logo: '/images/clients/meituan.svg' },
];

const testimonials = [
  {
    id: 1,
    rating: 5,
    company: 'Global Trade Corp',
    industry: 'Foreign Trade',
    logo: '/images/testimonials/company1.svg'
  },
  {
    id: 2,
    rating: 5,
    company: 'E-Commerce Plus',
    industry: 'Cross-border E-commerce',
    logo: '/images/testimonials/company2.svg'
  },
  {
    id: 3,
    rating: 5,
    company: 'Tech Solutions Ltd',
    industry: 'Technology Services',
    logo: '/images/testimonials/company3.svg'
  }
];

export default function ClientsSection() {
  const { t } = useTranslation(['home']);

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Trusted By Section */}
        <AnimatedSection className="text-center mb-20">
          <span className="inline-block px-4 py-2 bg-gray-100 text-gray-800 rounded-full text-sm font-semibold mb-4">
            {t('home:clients.badge')}
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-12">
            {t('home:clients.title')}
          </h2>
          
          {/* Client Logos */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
            {clients.map((client, index) => (
              <motion.div
                key={client.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors duration-300 flex items-center justify-center h-20">
                  {/* Placeholder for client logos */}
                  <div className="w-full h-8 bg-gray-300 rounded opacity-60 group-hover:opacity-80 transition-opacity duration-300 flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">{client.name}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>

        {/* Testimonials Section */}
        <AnimatedSection delay={0.3}>
          <div className="text-center mb-16">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {t('home:testimonials.title')}
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('home:testimonials.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative overflow-hidden">
                  {/* Quote Icon */}
                  <div className="absolute top-6 right-6 opacity-10">
                    <QuoteIcon className="h-12 w-12 text-blue-600" />
                  </div>

                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <StarIcon key={i} className="h-5 w-5 text-yellow-400" />
                    ))}
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="text-gray-700 mb-6 leading-relaxed">
                    "{t(`home:testimonials.items.${testimonial.id}.text`)}"
                  </blockquote>

                  {/* Company Info */}
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        {testimonial.company.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">
                        {t(`home:testimonials.items.${testimonial.id}.author`)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {t(`home:testimonials.items.${testimonial.id}.position`)} • {testimonial.company}
                      </div>
                      <div className="text-xs text-gray-500">
                        {testimonial.industry}
                      </div>
                    </div>
                  </div>

                  {/* Hover Effect */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </div>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>

        {/* Certifications */}
        <AnimatedSection delay={0.5} className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              {t('home:certifications.title')}
            </h3>
            <p className="text-lg text-gray-600">
              {t('home:certifications.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {['ISO27001', 'SOC2', 'GDPR', 'PCI-DSS'].map((cert, index) => (
              <motion.div
                key={cert}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 transform group-hover:scale-105">
                  <div className="w-16 h-16 bg-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{cert}</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">
                    {t(`home:certifications.items.${cert.toLowerCase()}.name`)}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {t(`home:certifications.items.${cert.toLowerCase()}.description`)}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
