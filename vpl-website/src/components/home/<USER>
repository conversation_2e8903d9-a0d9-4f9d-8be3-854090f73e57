'use client';

import { motion } from 'framer-motion';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import AnimatedSection from '../ui/AnimatedSection';
import { 
  CalendarIcon, 
  ArrowRightIcon, 
  TagIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

const newsItems = [
  {
    id: 1,
    category: 'company',
    date: '2024-01-15',
    readTime: '3 min',
    image: '/images/news/news1.jpg',
    featured: true
  },
  {
    id: 2,
    category: 'industry',
    date: '2024-01-10',
    readTime: '5 min',
    image: '/images/news/news2.jpg',
    featured: false
  },
  {
    id: 3,
    category: 'technology',
    date: '2024-01-05',
    readTime: '4 min',
    image: '/images/news/news3.jpg',
    featured: false
  },
  {
    id: 4,
    category: 'partnership',
    date: '2024-01-01',
    readTime: '2 min',
    image: '/images/news/news4.jpg',
    featured: false
  }
];

const categoryColors = {
  company: 'bg-blue-100 text-blue-800',
  industry: 'bg-green-100 text-green-800',
  technology: 'bg-purple-100 text-purple-800',
  partnership: 'bg-orange-100 text-orange-800'
};

export default function NewsSection() {
  const { t } = useTranslation(['home']);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section className="py-24 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4">
            {t('home:news.badge')}
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('home:news.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('home:news.subtitle')}
          </p>
        </AnimatedSection>

        {/* News Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Featured Article */}
          <AnimatedSection className="lg:row-span-2">
            <motion.article
              whileHover={{ y: -8 }}
              transition={{ duration: 0.3 }}
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100"
            >
              {/* Image */}
              <div className="relative h-64 lg:h-80 overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-cyan-500"></div>
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                
                {/* Featured Badge */}
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-red-500 text-white text-xs font-semibold rounded-full">
                    {t('home:news.featured')}
                  </span>
                </div>

                {/* Category */}
                <div className="absolute top-4 right-4">
                  <span className={`px-3 py-1 text-xs font-semibold rounded-full ${categoryColors[newsItems[0].category as keyof typeof categoryColors]}`}>
                    {t(`home:news.categories.${newsItems[0].category}`)}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <div className="flex items-center text-sm text-gray-500 mb-4">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  <span>{formatDate(newsItems[0].date)}</span>
                  <ClockIcon className="h-4 w-4 ml-4 mr-2" />
                  <span>{newsItems[0].readTime}</span>
                </div>

                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                  {t(`home:news.items.${newsItems[0].id}.title`)}
                </h3>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  {t(`home:news.items.${newsItems[0].id}.excerpt`)}
                </p>

                <Link
                  href={`/news/${newsItems[0].id}`}
                  className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors duration-300"
                >
                  {t('common:read_more')}
                  <ArrowRightIcon className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </motion.article>
          </AnimatedSection>

          {/* Other Articles */}
          <div className="space-y-8">
            {newsItems.slice(1).map((item, index) => (
              <AnimatedSection key={item.id} delay={index * 0.1}>
                <motion.article
                  whileHover={{ x: 8 }}
                  transition={{ duration: 0.3 }}
                  className="group flex bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
                >
                  {/* Image */}
                  <div className="relative w-32 h-32 flex-shrink-0 overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400"></div>
                    <div className="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-colors duration-300"></div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${categoryColors[item.category as keyof typeof categoryColors]}`}>
                        {t(`home:news.categories.${item.category}`)}
                      </span>
                      <div className="flex items-center text-xs text-gray-500">
                        <CalendarIcon className="h-3 w-3 mr-1" />
                        <span>{formatDate(item.date)}</span>
                      </div>
                    </div>

                    <h4 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                      {t(`home:news.items.${item.id}.title`)}
                    </h4>

                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {t(`home:news.items.${item.id}.excerpt`)}
                    </p>

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {item.readTime}
                      </span>
                      <Link
                        href={`/news/${item.id}`}
                        className="text-blue-600 hover:text-blue-700 transition-colors duration-300"
                      >
                        <ArrowRightIcon className="h-4 w-4" />
                      </Link>
                    </div>
                  </div>
                </motion.article>
              </AnimatedSection>
            ))}
          </div>
        </div>

        {/* View All Button */}
        <AnimatedSection delay={0.4} className="text-center">
          <Link
            href="/news"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            {t('home:news.view_all')}
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </AnimatedSection>
      </div>
    </section>
  );
}
