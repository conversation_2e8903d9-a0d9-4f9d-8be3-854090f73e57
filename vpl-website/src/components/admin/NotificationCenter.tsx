'use client';

import { useState, useEffect } from 'react';
import { BellIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { notificationService, showBrowserNotification, requestNotificationPermission } from '../../lib/notifications';

interface Notification {
  id: string;
  type: 'new-submission' | 'system' | 'warning';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

export default function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<string>('not-initialized');

  useEffect(() => {
    // Initialize notification service with error handling
    try {
      notificationService.connect();

      // Check connection status periodically
      const statusInterval = setInterval(() => {
        setConnectionStatus(notificationService.getStatus());
      }, 2000);

      // Try to join admin room after a short delay
      setTimeout(() => {
        notificationService.joinAdminRoom();
      }, 1000);

      // Request browser notification permission
      requestNotificationPermission();

      return () => {
        clearInterval(statusInterval);
      };
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      setConnectionStatus('error');
    }

    // Listen for new submissions
    const handleNewSubmission = (data: any) => {
      const notification: Notification = {
        id: Date.now().toString(),
        type: 'new-submission',
        title: '新的客户咨询',
        message: `${data.companyName} 提交了新的咨询`,
        timestamp: new Date().toISOString(),
        read: false,
      };

      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);

      // Show browser notification
      showBrowserNotification(notification.title, {
        body: notification.message,
        tag: 'new-submission',
      });
    };

    // Listen for admin notifications
    const handleAdminNotification = (notification: any) => {
      const newNotification: Notification = {
        id: Date.now().toString(),
        type: notification.type,
        title: notification.title || '系统通知',
        message: notification.message,
        timestamp: notification.timestamp,
        read: false,
      };

      setNotifications(prev => [newNotification, ...prev]);
      setUnreadCount(prev => prev + 1);

      // Show browser notification
      showBrowserNotification(newNotification.title, {
        body: newNotification.message,
        tag: notification.type,
      });
    };

    notificationService.onNewSubmission(handleNewSubmission);
    notificationService.onAdminNotification(handleAdminNotification);

    return () => {
      notificationService.offNewSubmission(handleNewSubmission);
      notificationService.offAdminNotification(handleAdminNotification);
      notificationService.disconnect();
    };
  }, []);

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  const removeNotification = (id: string) => {
    const notification = notifications.find(n => n.id === id);
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new-submission':
        return '📧';
      case 'system':
        return '⚙️';
      case 'warning':
        return '⚠️';
      default:
        return '📢';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return '刚刚';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}小时前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
      >
        <BellIcon className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
        {/* Connection status indicator */}
        <span className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full ${
          connectionStatus === 'connected' ? 'bg-green-500' :
          connectionStatus === 'disconnected' ? 'bg-yellow-500' :
          'bg-gray-400'
        }`} title={`Socket.IO: ${connectionStatus}`} />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 z-20 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 max-h-96 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">通知</h3>
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    全部标记为已读
                  </button>
                )}
              </div>
              {/* Connection status */}
              <div className="mt-2 text-xs text-gray-500">
                实时通知: {
                  connectionStatus === 'connected' ? '🟢 已连接' :
                  connectionStatus === 'disconnected' ? '🟡 连接中断' :
                  connectionStatus === 'error' ? '🔴 连接错误' :
                  '⚪ 未连接'
                }
              </div>
            </div>
            
            <div className="max-h-64 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  暂无通知
                </div>
              ) : (
                notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${
                      !notification.read ? 'bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <span className="text-lg">
                          {getNotificationIcon(notification.type)}
                        </span>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-400 mt-1">
                            {formatTimestamp(notification.timestamp)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="w-2 h-2 bg-blue-500 rounded-full"
                            title="标记为已读"
                          />
                        )}
                        <button
                          onClick={() => removeNotification(notification.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
