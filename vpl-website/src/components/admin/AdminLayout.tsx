import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { 
  HomeIcon,
  UsersIcon,
  EnvelopeIcon,
  ChartBarIcon,
  CogIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  ServerIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { NotificationCenter } from './NotificationCenter';

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const navigation = [
  { name: '仪表板', href: '/admin/dashboard', icon: HomeIcon },
  { name: '咨询管理', href: '/admin/inquiries', icon: EnvelopeIcon },
  { name: '用户管理', href: '/admin/users', icon: UsersIcon },
  { name: '数据分析', href: '/admin/analytics', icon: ChartBarIcon },
  { 
    name: '系统设置', 
    icon: CogIcon,
    children: [
      { name: '基本配置', href: '/admin/system/config', icon: CogIcon },
      { name: '邮件设置', href: '/admin/email-settings', icon: EnvelopeIcon },
      { name: '多语言管理', href: '/admin/system/localization', icon: GlobeAltIcon },
      { name: '安全设置', href: '/admin/system/security', icon: ShieldCheckIcon },
    ]
  },
  { 
    name: '内容管理', 
    icon: DocumentTextIcon,
    children: [
      { name: '页面内容', href: '/admin/content/pages', icon: DocumentTextIcon },
      { name: '服务类型', href: '/admin/content/services', icon: ServerIcon },
    ]
  },
];

export default function AdminLayout({ children, title }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userInfo, setUserInfo] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = () => {
    const token = localStorage.getItem('adminToken');
    if (!token) {
      router.push('/admin/login');
      return;
    }

    fetch('/api/admin/verify', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        setIsAuthenticated(true);
        setUserInfo(result.user);
      } else {
        localStorage.removeItem('adminToken');
        router.push('/admin/login');
      }
    })
    .catch(() => {
      localStorage.removeItem('adminToken');
      router.push('/admin/login');
    })
    .finally(() => {
      setIsLoading(false);
    });
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    router.push('/admin/login');
  };

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isCurrentPath = (href: string) => {
    return router.pathname === href || router.pathname.startsWith(href + '/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)}></div>
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6 text-white" />
            </button>
          </div>
          <SidebarContent 
            navigation={navigation} 
            expandedItems={expandedItems}
            toggleExpanded={toggleExpanded}
            isCurrentPath={isCurrentPath}
          />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <SidebarContent 
            navigation={navigation} 
            expandedItems={expandedItems}
            toggleExpanded={toggleExpanded}
            isCurrentPath={isCurrentPath}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Top bar */}
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
          <button
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          
          <div className="flex-1 px-4 flex justify-between">
            <div className="flex-1 flex items-center">
              {title && (
                <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
              )}
            </div>
            
            <div className="ml-4 flex items-center md:ml-6 space-x-4">
              <NotificationCenter />
              
              {/* User menu */}
              <div className="flex items-center space-x-3">
                <div className="text-sm">
                  <div className="font-medium text-gray-700">{userInfo?.fullName || userInfo?.username}</div>
                  <div className="text-gray-500">{userInfo?.role}</div>
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center text-gray-400 hover:text-gray-600"
                  title="退出登录"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          {children}
        </main>
      </div>
    </div>
  );
}

function SidebarContent({ 
  navigation, 
  expandedItems, 
  toggleExpanded, 
  isCurrentPath 
}: {
  navigation: any[];
  expandedItems: string[];
  toggleExpanded: (name: string) => void;
  isCurrentPath: (href: string) => boolean;
}) {
  return (
    <div className="flex flex-col h-0 flex-1 border-r border-gray-200 bg-white">
      {/* Logo */}
      <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg mr-3">
            <span className="text-white font-bold">VPL</span>
          </div>
          <span className="text-xl font-semibold text-gray-900">管理后台</span>
        </div>
        
        {/* Navigation */}
        <nav className="mt-5 flex-1 px-2 space-y-1">
          {navigation.map((item) => (
            <div key={item.name}>
              {item.children ? (
                <div>
                  <button
                    onClick={() => toggleExpanded(item.name)}
                    className={`group w-full flex items-center pl-2 pr-1 py-2 text-left text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      expandedItems.includes(item.name)
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <item.icon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
                    {item.name}
                    <svg
                      className={`ml-auto h-5 w-5 transform transition-colors duration-150 ${
                        expandedItems.includes(item.name) ? 'rotate-90 text-gray-400' : 'text-gray-300'
                      }`}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                  {expandedItems.includes(item.name) && (
                    <div className="mt-1 space-y-1">
                      {item.children.map((subItem: any) => (
                        <Link
                          key={subItem.name}
                          href={subItem.href}
                          className={`group w-full flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md ${
                            isCurrentPath(subItem.href)
                              ? 'bg-blue-100 text-blue-700'
                              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                          }`}
                        >
                          <subItem.icon className="mr-3 h-4 w-4" />
                          {subItem.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  href={item.href}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    isCurrentPath(item.href)
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon className={`mr-3 h-5 w-5 ${
                    isCurrentPath(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`} />
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  );
}
