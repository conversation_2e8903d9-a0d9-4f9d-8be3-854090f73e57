// User Management Types
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: AdminRole;
  permissions: Permission[];
  isActive: boolean;
  lastLoginAt?: string;
  loginAttempts: number;
  lockedUntil?: string;
  twoFactorEnabled: boolean;
  avatar?: string;
  phone?: string;
  department?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  lastPasswordChange?: string;
}

export interface AdminRole {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: Permission[];
  isSystem: boolean; // Cannot be deleted
  createdAt: string;
  updatedAt: string;
}

export interface Permission {
  id: string;
  name: string;
  displayName: string;
  description: string;
  category: PermissionCategory;
  resource: string;
  action: PermissionAction;
}

export type PermissionCategory = 
  | 'system'
  | 'users'
  | 'content'
  | 'inquiries'
  | 'analytics'
  | 'settings';

export type PermissionAction = 
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'manage'
  | 'export'
  | 'import';

// Login Activity
export interface LoginLog {
  id: string;
  userId: string;
  username: string;
  ipAddress: string;
  userAgent: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  success: boolean;
  failureReason?: string;
  sessionId?: string;
  loggedOutAt?: string;
  createdAt: string;
}

// User Session
export interface UserSession {
  id: string;
  userId: string;
  token: string;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  expiresAt: string;
  createdAt: string;
  lastActivityAt: string;
}

// Password Reset
export interface PasswordReset {
  id: string;
  userId: string;
  token: string;
  expiresAt: string;
  used: boolean;
  createdAt: string;
  usedAt?: string;
}

// Two Factor Authentication
export interface TwoFactorAuth {
  id: string;
  userId: string;
  secret: string;
  backupCodes: string[];
  enabled: boolean;
  enabledAt?: string;
  lastUsedAt?: string;
}

// User Creation/Update Requests
export interface CreateUserRequest {
  username: string;
  email: string;
  fullName: string;
  password: string;
  role: string;
  phone?: string;
  department?: string;
  isActive?: boolean;
}

export interface UpdateUserRequest {
  email?: string;
  fullName?: string;
  role?: string;
  phone?: string;
  department?: string;
  isActive?: boolean;
  permissions?: string[];
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// API Response Types
export interface UserResponse {
  success: boolean;
  message: string;
  data?: AdminUser | AdminUser[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface RoleResponse {
  success: boolean;
  message: string;
  data?: AdminRole | AdminRole[];
}

export interface PermissionResponse {
  success: boolean;
  message: string;
  data?: Permission[];
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: {
    id: string;
    username: string;
    fullName: string;
    role: string;
    permissions: string[];
  };
  requiresTwoFactor?: boolean;
}

// User Statistics
export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  lockedUsers: number;
  recentLogins: number;
  failedLogins: number;
  roleDistribution: Array<{
    role: string;
    count: number;
    percentage: number;
  }>;
}
