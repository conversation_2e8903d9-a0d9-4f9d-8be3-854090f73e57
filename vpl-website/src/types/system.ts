// System Configuration Types
export interface SystemConfig {
  id: string;
  category: 'general' | 'email' | 'security' | 'localization' | 'form';
  key: string;
  value: string | number | boolean | object;
  type: 'string' | 'number' | 'boolean' | 'json' | 'email' | 'url' | 'password';
  label: string;
  description?: string;
  required: boolean;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: string[];
  };
  updatedAt: string;
  updatedBy: string;
}

// Website Basic Information
export interface WebsiteInfo {
  companyName: string;
  companyNameEn: string;
  companyNameRu: string;
  description: string;
  descriptionEn: string;
  descriptionRu: string;
  address: string;
  addressEn: string;
  addressRu: string;
  phone: string;
  email: string;
  website: string;
  businessHours: string;
  businessHoursEn: string;
  businessHoursRu: string;
  logo?: string;
  favicon?: string;
  socialMedia: {
    wechat?: string;
    qq?: string;
    linkedin?: string;
    facebook?: string;
    twitter?: string;
  };
}

// SMTP Configuration
export interface SMTPConfig {
  host: string;
  port: number;
  secure: boolean;
  username: string;
  password: string;
  fromEmail: string;
  fromName: string;
  replyTo?: string;
  testEmail?: string;
  enabled: boolean;
}

// Form Configuration
export interface FormConfig {
  verificationCodeExpiry: number; // minutes
  requiredFields: {
    companyName: boolean;
    contactPerson: boolean;
    phone: boolean;
    email: boolean;
    wechat: boolean;
    qq: boolean;
    serviceType: boolean;
    message: boolean;
  };
  maxMessageLength: number;
  enableCaptcha: boolean;
  autoResponse: boolean;
  notificationEmails: string[];
}

// Security Configuration
export interface SecurityConfig {
  sessionTimeout: number; // minutes
  maxLoginAttempts: number;
  lockoutDuration: number; // minutes
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
  twoFactorAuth: boolean;
  ipWhitelist: string[];
}

// Localization Content
export interface LocalizationContent {
  key: string;
  zh: string;
  en: string;
  ru: string;
  category: 'navigation' | 'content' | 'form' | 'message' | 'service';
  description?: string;
  updatedAt: string;
}

// Service Type Configuration
export interface ServiceType {
  id: string;
  key: string;
  name: string;
  nameEn: string;
  nameRu: string;
  description: string;
  descriptionEn: string;
  descriptionRu: string;
  icon?: string;
  color?: string;
  order: number;
  enabled: boolean;
  features: string[];
  featuresEn: string[];
  featuresRu: string[];
  pricing?: {
    currency: string;
    startingPrice?: number;
    priceRange?: string;
    contactForPrice: boolean;
  };
}

// System Maintenance
export interface SystemMaintenance {
  backups: {
    lastBackup?: string;
    autoBackup: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    retentionDays: number;
  };
  cache: {
    lastCleared?: string;
    autoClean: boolean;
    cleanFrequency: 'hourly' | 'daily' | 'weekly';
  };
  logs: {
    retentionDays: number;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    maxFileSize: number; // MB
  };
  performance: {
    enableMonitoring: boolean;
    alertThreshold: number; // response time in ms
    alertEmails: string[];
  };
}

// API Response Types
export interface ConfigResponse {
  success: boolean;
  message: string;
  data?: SystemConfig | SystemConfig[];
}

export interface ConfigUpdateRequest {
  configs: Array<{
    key: string;
    value: any;
    category: string;
  }>;
}
