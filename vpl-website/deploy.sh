#!/bin/bash

# VPL Website Docker Deployment Script
# This script provides one-click deployment for VPL website with Docker

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="vpl-website"
DOCKER_COMPOSE_FILE="docker-compose.yml"
DOCKER_COMPOSE_DEV_FILE="docker-compose.dev.yml"
ENV_FILE=".env"
ENV_EXAMPLE_FILE=".env.example"

# Default values
ENVIRONMENT="production"
FORCE_REBUILD=false
SKIP_CHECKS=false
BACKUP_BEFORE_DEPLOY=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
VPL Website Docker Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV    Set environment (production|development) [default: production]
    -r, --rebuild           Force rebuild of Docker images
    -s, --skip-checks       Skip system requirement checks
    -b, --backup            Create backup before deployment
    -h, --help              Show this help message

EXAMPLES:
    $0                      # Deploy production environment
    $0 -e development       # Deploy development environment
    $0 -r -b               # Force rebuild with backup
    $0 --environment development --rebuild

ENVIRONMENTS:
    production: Full production setup with optimized images
    development: Development setup with hot reload and debug tools

EOF
}

# Function to parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -r|--rebuild)
                FORCE_REBUILD=true
                shift
                ;;
            -s|--skip-checks)
                SKIP_CHECKS=true
                shift
                ;;
            -b|--backup)
                BACKUP_BEFORE_DEPLOY=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate environment
    if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "development" ]]; then
        print_error "Invalid environment: $ENVIRONMENT. Must be 'production' or 'development'"
        exit 1
    fi
}

# Function to check system requirements
check_requirements() {
    if [[ "$SKIP_CHECKS" == true ]]; then
        print_warning "Skipping system requirement checks"
        return 0
    fi

    print_status "Checking system requirements..."

    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        print_status "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi

    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        print_status "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi

    # Check Docker version
    DOCKER_VERSION=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    print_status "Docker version: $DOCKER_VERSION"

    # Check Docker Compose version
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        COMPOSE_CMD="docker-compose"
    else
        COMPOSE_VERSION=$(docker compose version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        COMPOSE_CMD="docker compose"
    fi
    print_status "Docker Compose version: $COMPOSE_VERSION"

    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi

    print_success "System requirements check passed"
}

# Function to generate random password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."

    if [[ ! -f "$ENV_EXAMPLE_FILE" ]]; then
        print_error "Environment example file not found: $ENV_EXAMPLE_FILE"
        exit 1
    fi

    if [[ -f "$ENV_FILE" ]]; then
        print_warning "Environment file already exists: $ENV_FILE"
        read -p "Do you want to regenerate it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Using existing environment file"
            return 0
        fi
    fi

    # Copy example file
    cp "$ENV_EXAMPLE_FILE" "$ENV_FILE"

    # Generate secure passwords
    POSTGRES_PASSWORD=$(generate_password 32)
    REDIS_PASSWORD=$(generate_password 32)
    NEXTAUTH_SECRET=$(generate_password 32)
    JWT_SECRET=$(generate_password 32)
    ADMIN_PASSWORD=$(generate_password 16)

    # Update environment file with generated values
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your-secure-postgres-password/$POSTGRES_PASSWORD/g" "$ENV_FILE"
        sed -i '' "s/your-secure-redis-password/$REDIS_PASSWORD/g" "$ENV_FILE"
        sed -i '' "s/your-nextauth-secret-here/$NEXTAUTH_SECRET/g" "$ENV_FILE"
        sed -i '' "s/your-jwt-secret-here/$JWT_SECRET/g" "$ENV_FILE"
        sed -i '' "s/your-secure-admin-password/$ADMIN_PASSWORD/g" "$ENV_FILE"
    else
        # Linux
        sed -i "s/your-secure-postgres-password/$POSTGRES_PASSWORD/g" "$ENV_FILE"
        sed -i "s/your-secure-redis-password/$REDIS_PASSWORD/g" "$ENV_FILE"
        sed -i "s/your-nextauth-secret-here/$NEXTAUTH_SECRET/g" "$ENV_FILE"
        sed -i "s/your-jwt-secret-here/$JWT_SECRET/g" "$ENV_FILE"
        sed -i "s/your-secure-admin-password/$ADMIN_PASSWORD/g" "$ENV_FILE"
    fi

    # Set environment-specific values
    if [[ "$ENVIRONMENT" == "development" ]]; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            sed -i '' "s/NODE_ENV=production/NODE_ENV=development/g" "$ENV_FILE"
        else
            sed -i "s/NODE_ENV=production/NODE_ENV=development/g" "$ENV_FILE"
        fi
    fi

    print_success "Environment file created: $ENV_FILE"
    print_warning "Generated admin password: $ADMIN_PASSWORD"
    print_warning "Please save this password securely and change it after first login!"
}

# Function to create backup
create_backup() {
    if [[ "$BACKUP_BEFORE_DEPLOY" != true ]]; then
        return 0
    fi

    print_status "Creating backup before deployment..."

    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"

    # Backup database if container exists
    if docker ps -a --format 'table {{.Names}}' | grep -q "${PROJECT_NAME}-postgres"; then
        print_status "Backing up database..."
        docker exec "${PROJECT_NAME}-postgres" pg_dump -U vpl_user vpl_website > "$BACKUP_DIR/database.sql" 2>/dev/null || true
    fi

    # Backup uploads if volume exists
    if docker volume ls --format 'table {{.Name}}' | grep -q "${PROJECT_NAME}_uploads"; then
        print_status "Backing up uploads..."
        docker run --rm -v "${PROJECT_NAME}_uploads:/data" -v "$(pwd)/$BACKUP_DIR:/backup" alpine tar czf /backup/uploads.tar.gz -C /data . 2>/dev/null || true
    fi

    print_success "Backup created in: $BACKUP_DIR"
}

# Function to deploy application
deploy_application() {
    print_status "Deploying VPL Website ($ENVIRONMENT environment)..."

    # Set compose file based on environment
    if [[ "$ENVIRONMENT" == "development" ]]; then
        COMPOSE_FILE="$DOCKER_COMPOSE_DEV_FILE"
    else
        COMPOSE_FILE="$DOCKER_COMPOSE_FILE"
    fi

    # Build and start services
    if [[ "$FORCE_REBUILD" == true ]]; then
        print_status "Force rebuilding Docker images..."
        $COMPOSE_CMD -f "$COMPOSE_FILE" build --no-cache
    fi

    print_status "Starting services..."
    $COMPOSE_CMD -f "$COMPOSE_FILE" up -d

    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 10

    # Check service health
    check_service_health
}

# Function to check service health
check_service_health() {
    print_status "Checking service health..."

    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            print_success "Application is healthy and ready!"
            return 0
        fi

        print_status "Attempt $attempt/$max_attempts: Waiting for application to be ready..."
        sleep 5
        ((attempt++))
    done

    print_error "Application failed to become healthy within expected time"
    print_status "Checking container logs..."
    $COMPOSE_CMD logs --tail=20
    return 1
}

# Function to show deployment summary
show_deployment_summary() {
    print_success "Deployment completed successfully!"
    echo
    echo "=== Deployment Summary ==="
    echo "Environment: $ENVIRONMENT"
    echo "Application URL: http://localhost:3000"

    if [[ "$ENVIRONMENT" == "development" ]]; then
        echo "pgAdmin URL: http://localhost:8080"
        echo "Redis Commander URL: http://localhost:8081"
        echo "Database Port: 5432"
        echo "Redis Port: 6379"
    fi

    echo
    echo "=== Admin Access ==="
    echo "Username: admin"
    echo "Email: <EMAIL>"
    echo "Password: (check .env file for ADMIN_PASSWORD)"
    echo
    echo "=== Useful Commands ==="
    echo "View logs: $COMPOSE_CMD logs -f"
    echo "Stop services: $COMPOSE_CMD down"
    echo "Restart services: $COMPOSE_CMD restart"
    echo "Update services: $0 --rebuild"
    echo
}

# Main deployment function
main() {
    echo "=== VPL Website Docker Deployment ==="
    echo

    parse_arguments "$@"
    check_requirements
    setup_environment
    create_backup
    deploy_application
    show_deployment_summary
}

# Run main function with all arguments
main "$@"
