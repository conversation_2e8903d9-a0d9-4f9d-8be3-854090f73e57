#!/bin/bash

# VPL Website Service Management Script
# This script provides service management utilities for Docker containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_NAME="vpl-website"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
VPL Website Service Management Script

Usage: $0 [COMMAND] [OPTIONS]

COMMANDS:
    start [service]         Start all services or specific service
    stop [service]          Stop all services or specific service
    restart [service]       Restart all services or specific service
    status                  Show status of all services
    logs [service]          Show logs for all services or specific service
    update                  Update and restart services
    clean                   Clean up unused Docker resources
    health                  Check health of all services
    scale <service> <count> Scale a service to specified number of replicas

OPTIONS:
    -f, --follow           Follow log output (for logs command)
    -h, --help             Show this help message

SERVICES:
    app                    Next.js application
    postgres               PostgreSQL database
    redis                  Redis cache

EXAMPLES:
    $0 start                    # Start all services
    $0 start app                # Start only app service
    $0 logs app                 # Show app logs
    $0 logs -f                  # Follow all logs
    $0 restart postgres         # Restart database
    $0 scale app 3              # Scale app to 3 replicas

EOF
}

# Function to detect compose command
detect_compose_cmd() {
    if command -v docker-compose &> /dev/null; then
        echo "docker-compose"
    elif docker compose version &> /dev/null; then
        echo "docker compose"
    else
        print_error "Docker Compose not found"
        exit 1
    fi
}

# Function to get compose file
get_compose_file() {
    if [[ -f "$PROJECT_DIR/docker-compose.dev.yml" ]] && [[ "${NODE_ENV:-}" == "development" ]]; then
        echo "$PROJECT_DIR/docker-compose.dev.yml"
    else
        echo "$PROJECT_DIR/docker-compose.yml"
    fi
}

# Function to start services
start_services() {
    local service="$1"
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    cd "$PROJECT_DIR"
    
    if [[ -n "$service" ]]; then
        print_status "Starting service: $service"
        $compose_cmd -f "$compose_file" up -d "$service"
    else
        print_status "Starting all services"
        $compose_cmd -f "$compose_file" up -d
    fi
    
    print_success "Services started successfully"
}

# Function to stop services
stop_services() {
    local service="$1"
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    cd "$PROJECT_DIR"
    
    if [[ -n "$service" ]]; then
        print_status "Stopping service: $service"
        $compose_cmd -f "$compose_file" stop "$service"
    else
        print_status "Stopping all services"
        $compose_cmd -f "$compose_file" down
    fi
    
    print_success "Services stopped successfully"
}

# Function to restart services
restart_services() {
    local service="$1"
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    cd "$PROJECT_DIR"
    
    if [[ -n "$service" ]]; then
        print_status "Restarting service: $service"
        $compose_cmd -f "$compose_file" restart "$service"
    else
        print_status "Restarting all services"
        $compose_cmd -f "$compose_file" restart
    fi
    
    print_success "Services restarted successfully"
}

# Function to show service status
show_status() {
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    cd "$PROJECT_DIR"
    
    print_status "Service Status:"
    echo
    $compose_cmd -f "$compose_file" ps
    
    echo
    print_status "Docker System Info:"
    docker system df
}

# Function to show logs
show_logs() {
    local service="$1"
    local follow="$2"
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    cd "$PROJECT_DIR"
    
    local log_cmd="$compose_cmd -f $compose_file logs"
    
    if [[ "$follow" == "true" ]]; then
        log_cmd="$log_cmd -f"
    fi
    
    if [[ -n "$service" ]]; then
        print_status "Showing logs for service: $service"
        $log_cmd "$service"
    else
        print_status "Showing logs for all services"
        $log_cmd
    fi
}

# Function to update services
update_services() {
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    cd "$PROJECT_DIR"
    
    print_status "Updating services..."
    
    # Pull latest images
    print_status "Pulling latest images..."
    $compose_cmd -f "$compose_file" pull
    
    # Rebuild and restart
    print_status "Rebuilding and restarting services..."
    $compose_cmd -f "$compose_file" up -d --build
    
    print_success "Services updated successfully"
}

# Function to clean up Docker resources
clean_resources() {
    print_warning "This will remove unused Docker resources"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleanup cancelled"
        exit 0
    fi
    
    print_status "Cleaning up Docker resources..."
    
    # Remove unused containers
    print_status "Removing unused containers..."
    docker container prune -f
    
    # Remove unused images
    print_status "Removing unused images..."
    docker image prune -f
    
    # Remove unused volumes
    print_status "Removing unused volumes..."
    docker volume prune -f
    
    # Remove unused networks
    print_status "Removing unused networks..."
    docker network prune -f
    
    print_success "Cleanup completed"
    
    # Show disk usage after cleanup
    echo
    print_status "Docker disk usage after cleanup:"
    docker system df
}

# Function to check service health
check_health() {
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    cd "$PROJECT_DIR"
    
    print_status "Checking service health..."
    echo
    
    # Check container health
    local containers=$($compose_cmd -f "$compose_file" ps -q)
    
    for container in $containers; do
        local name=$(docker inspect --format='{{.Name}}' "$container" | sed 's/\///')
        local health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
        local status=$(docker inspect --format='{{.State.Status}}' "$container")
        
        if [[ "$status" == "running" ]]; then
            if [[ "$health" == "healthy" ]]; then
                print_success "$name: running (healthy)"
            elif [[ "$health" == "unhealthy" ]]; then
                print_error "$name: running (unhealthy)"
            else
                print_warning "$name: running (no health check)"
            fi
        else
            print_error "$name: $status"
        fi
    done
    
    echo
    
    # Check application health endpoint
    if curl -f http://localhost:3000/api/health &> /dev/null; then
        print_success "Application health endpoint: OK"
        
        # Show health details
        echo
        print_status "Health Details:"
        curl -s http://localhost:3000/api/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:3000/api/health
    else
        print_error "Application health endpoint: FAILED"
    fi
}

# Function to scale services
scale_service() {
    local service="$1"
    local count="$2"
    local compose_cmd=$(detect_compose_cmd)
    local compose_file=$(get_compose_file)
    
    if [[ -z "$service" || -z "$count" ]]; then
        print_error "Service name and replica count required"
        show_usage
        exit 1
    fi
    
    if ! [[ "$count" =~ ^[0-9]+$ ]]; then
        print_error "Replica count must be a number"
        exit 1
    fi
    
    cd "$PROJECT_DIR"
    
    print_status "Scaling service '$service' to $count replicas..."
    $compose_cmd -f "$compose_file" up -d --scale "$service=$count"
    
    print_success "Service scaled successfully"
}

# Main function
main() {
    local command="$1"
    local follow=false
    
    # Parse options
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--follow)
                follow=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                break
                ;;
        esac
    done
    
    command="$1"
    
    if [[ -z "$command" ]]; then
        show_usage
        exit 1
    fi
    
    case "$command" in
        start)
            start_services "$2"
            ;;
        stop)
            stop_services "$2"
            ;;
        restart)
            restart_services "$2"
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2" "$follow"
            ;;
        update)
            update_services
            ;;
        clean)
            clean_resources
            ;;
        health)
            check_health
            ;;
        scale)
            scale_service "$2" "$3"
            ;;
        *)
            print_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
