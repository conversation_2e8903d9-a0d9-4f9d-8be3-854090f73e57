#!/bin/bash

# VPL Website Database Management Script
# This script provides database backup, restore, and migration utilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_NAME="vpl-website"
CONTAINER_NAME="${PROJECT_NAME}-postgres"
DB_NAME="vpl_website"
DB_USER="vpl_user"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
VPL Website Database Management Script

Usage: $0 [COMMAND] [OPTIONS]

COMMANDS:
    backup [filename]       Create database backup
    restore <filename>      Restore database from backup
    reset                   Reset database to initial state
    migrate                 Run database migrations
    seed                    Seed database with sample data
    shell                   Open database shell
    logs                    Show database logs
    status                  Show database status

OPTIONS:
    -h, --help             Show this help message

EXAMPLES:
    $0 backup                    # Create backup with timestamp
    $0 backup my-backup.sql      # Create backup with custom name
    $0 restore backup.sql        # Restore from backup file
    $0 reset                     # Reset database
    $0 shell                     # Open psql shell

EOF
}

# Function to check if database container is running
check_database_container() {
    if ! docker ps --format 'table {{.Names}}' | grep -q "$CONTAINER_NAME"; then
        print_error "Database container '$CONTAINER_NAME' is not running"
        print_status "Start the application first: ./deploy.sh"
        exit 1
    fi
}

# Function to create database backup
create_backup() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    fi
    
    # Ensure backup directory exists
    mkdir -p "$PROJECT_DIR/backups"
    local backup_path="$PROJECT_DIR/backups/$backup_file"
    
    print_status "Creating database backup..."
    print_status "Backup file: $backup_path"
    
    if docker exec "$CONTAINER_NAME" pg_dump -U "$DB_USER" -d "$DB_NAME" > "$backup_path"; then
        print_success "Database backup created successfully"
        print_status "Backup size: $(du -h "$backup_path" | cut -f1)"
    else
        print_error "Failed to create database backup"
        exit 1
    fi
}

# Function to restore database from backup
restore_backup() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        print_error "Backup file not specified"
        show_usage
        exit 1
    fi
    
    local backup_path
    if [[ -f "$backup_file" ]]; then
        backup_path="$backup_file"
    elif [[ -f "$PROJECT_DIR/backups/$backup_file" ]]; then
        backup_path="$PROJECT_DIR/backups/$backup_file"
    else
        print_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    print_warning "This will overwrite the current database!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Restore cancelled"
        exit 0
    fi
    
    print_status "Restoring database from backup..."
    print_status "Backup file: $backup_path"
    
    # Drop and recreate database
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "CREATE DATABASE $DB_NAME;"
    
    # Restore from backup
    if docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" < "$backup_path"; then
        print_success "Database restored successfully"
    else
        print_error "Failed to restore database"
        exit 1
    fi
}

# Function to reset database
reset_database() {
    print_warning "This will completely reset the database to initial state!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Reset cancelled"
        exit 0
    fi
    
    print_status "Resetting database..."
    
    # Drop and recreate database
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "CREATE DATABASE $DB_NAME;"
    
    # Run initialization script
    local init_script="$PROJECT_DIR/docker/postgres/init/01-init-database.sql"
    if [[ -f "$init_script" ]]; then
        docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" < "$init_script"
        print_success "Database reset completed"
    else
        print_error "Initialization script not found: $init_script"
        exit 1
    fi
}

# Function to run migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # In a real application, you would run your migration tool here
    # For now, we'll just run the initialization script
    local init_script="$PROJECT_DIR/docker/postgres/init/01-init-database.sql"
    if [[ -f "$init_script" ]]; then
        docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" < "$init_script"
        print_success "Migrations completed"
    else
        print_error "Migration script not found: $init_script"
        exit 1
    fi
}

# Function to seed database
seed_database() {
    print_status "Seeding database with sample data..."
    
    # Run the initialization script which includes seed data
    local init_script="$PROJECT_DIR/docker/postgres/init/01-init-database.sql"
    if [[ -f "$init_script" ]]; then
        docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" < "$init_script"
        print_success "Database seeded successfully"
    else
        print_error "Seed script not found: $init_script"
        exit 1
    fi
}

# Function to open database shell
open_shell() {
    print_status "Opening database shell..."
    print_status "Use \\q to exit"
    docker exec -it "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME"
}

# Function to show database logs
show_logs() {
    print_status "Showing database logs..."
    docker logs "$CONTAINER_NAME" --tail=50 -f
}

# Function to show database status
show_status() {
    print_status "Database Status:"
    echo
    
    # Container status
    if docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep -q "$CONTAINER_NAME"; then
        print_success "Container is running"
        docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep "$CONTAINER_NAME"
    else
        print_error "Container is not running"
    fi
    
    echo
    
    # Database connection test
    if docker exec "$CONTAINER_NAME" pg_isready -U "$DB_USER" -d "$DB_NAME" &> /dev/null; then
        print_success "Database is accepting connections"
        
        # Show database info
        echo
        print_status "Database Information:"
        docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT 
                current_database() as database,
                current_user as user,
                version() as version;
        " 2>/dev/null || true
        
        # Show table count
        echo
        print_status "Table Count:"
        docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT count(*) as table_count 
            FROM information_schema.tables 
            WHERE table_schema = 'public';
        " 2>/dev/null || true
        
    else
        print_error "Database is not accepting connections"
    fi
}

# Main function
main() {
    local command="$1"
    
    if [[ -z "$command" ]]; then
        show_usage
        exit 1
    fi
    
    # Check if database container is running (except for status command)
    if [[ "$command" != "status" ]]; then
        check_database_container
    fi
    
    case "$command" in
        backup)
            create_backup "$2"
            ;;
        restore)
            restore_backup "$2"
            ;;
        reset)
            reset_database
            ;;
        migrate)
            run_migrations
            ;;
        seed)
            seed_database
            ;;
        shell)
            open_shell
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        -h|--help)
            show_usage
            ;;
        *)
            print_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
