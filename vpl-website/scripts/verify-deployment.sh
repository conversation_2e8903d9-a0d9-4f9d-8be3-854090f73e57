#!/bin/bash

# VPL Website Deployment Verification Script
# This script verifies that the deployment is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_URL="http://localhost:3000"
ADMIN_URL="http://localhost:3000/admin"
HEALTH_URL="http://localhost:3000/api/health"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check HTTP endpoint
check_endpoint() {
    local url="$1"
    local name="$2"
    local expected_status="${3:-200}"
    
    print_status "Checking $name..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [[ "$response" == "$expected_status" ]]; then
        print_success "$name is responding correctly (HTTP $response)"
        return 0
    else
        print_error "$name is not responding correctly (HTTP $response)"
        return 1
    fi
}

# Function to check container health
check_containers() {
    print_status "Checking container status..."
    
    local containers=("vpl-website-app" "vpl-website-postgres" "vpl-website-redis")
    local all_healthy=true
    
    for container in "${containers[@]}"; do
        if docker ps --format 'table {{.Names}}\t{{.Status}}' | grep -q "$container"; then
            local status=$(docker ps --format 'table {{.Names}}\t{{.Status}}' | grep "$container" | awk '{print $2}')
            print_success "$container: $status"
        else
            print_error "$container: Not running"
            all_healthy=false
        fi
    done
    
    if [[ "$all_healthy" == true ]]; then
        return 0
    else
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    print_status "Checking database connectivity..."
    
    if docker exec vpl-website-postgres pg_isready -U vpl_user -d vpl_website >/dev/null 2>&1; then
        print_success "Database is accepting connections"
        
        # Check if tables exist
        local table_count=$(docker exec vpl-website-postgres psql -U vpl_user -d vpl_website -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
        
        if [[ "$table_count" -gt 0 ]]; then
            print_success "Database has $table_count tables"
        else
            print_warning "Database has no tables (may need initialization)"
        fi
        
        return 0
    else
        print_error "Database is not accepting connections"
        return 1
    fi
}

# Function to check Redis connectivity
check_redis() {
    print_status "Checking Redis connectivity..."
    
    if docker exec vpl-website-redis redis-cli ping >/dev/null 2>&1; then
        print_success "Redis is responding to ping"
        return 0
    else
        print_error "Redis is not responding"
        return 1
    fi
}

# Function to check application health
check_application_health() {
    print_status "Checking application health endpoint..."
    
    local health_response=$(curl -s "$HEALTH_URL" 2>/dev/null || echo "{}")
    
    if echo "$health_response" | grep -q '"status":"healthy"'; then
        print_success "Application health check passed"
        
        # Parse and display health details
        if command -v python3 >/dev/null 2>&1; then
            echo "$health_response" | python3 -m json.tool 2>/dev/null | head -20
        else
            echo "$health_response"
        fi
        
        return 0
    else
        print_error "Application health check failed"
        echo "Response: $health_response"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    print_status "Checking disk space..."
    
    local available_space=$(df -h . | awk 'NR==2 {print $4}')
    local used_percentage=$(df -h . | awk 'NR==2 {print $5}' | tr -d '%')
    
    print_status "Available space: $available_space"
    print_status "Used: $used_percentage%"
    
    if [[ "$used_percentage" -lt 90 ]]; then
        print_success "Disk space is sufficient"
        return 0
    else
        print_warning "Disk space is running low ($used_percentage% used)"
        return 1
    fi
}

# Function to check Docker resources
check_docker_resources() {
    print_status "Checking Docker resource usage..."
    
    # Check Docker system info
    docker system df
    
    echo
    print_status "Container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
}

# Function to run performance test
run_performance_test() {
    print_status "Running basic performance test..."
    
    local start_time=$(date +%s%N)
    local response=$(curl -s -o /dev/null -w "%{time_total}" "$APP_URL" 2>/dev/null || echo "0")
    local end_time=$(date +%s%N)
    
    local response_time_ms=$(echo "scale=2; $response * 1000" | bc 2>/dev/null || echo "$response")
    
    print_status "Response time: ${response_time_ms}ms"
    
    if (( $(echo "$response < 2.0" | bc -l 2>/dev/null || echo "1") )); then
        print_success "Response time is good"
        return 0
    else
        print_warning "Response time is slow (>${response_time_ms}ms)"
        return 1
    fi
}

# Function to show summary
show_summary() {
    echo
    echo "=================================="
    echo "     DEPLOYMENT VERIFICATION"
    echo "=================================="
    echo
    echo "🌐 Application URLs:"
    echo "   Main Website: $APP_URL"
    echo "   Admin Panel:  $ADMIN_URL"
    echo "   Health Check: $HEALTH_URL"
    echo
    echo "🔧 Management Commands:"
    echo "   View logs:    docker-compose logs -f"
    echo "   Stop:         docker-compose down"
    echo "   Restart:      ./deploy.sh"
    echo "   DB backup:    ./scripts/db-manage.sh backup"
    echo
}

# Main verification function
main() {
    echo "=================================="
    echo "  VPL Website Deployment Verification"
    echo "=================================="
    echo
    
    local all_checks_passed=true
    
    # Run all checks
    if ! check_containers; then
        all_checks_passed=false
    fi
    
    echo
    if ! check_database; then
        all_checks_passed=false
    fi
    
    echo
    if ! check_redis; then
        all_checks_passed=false
    fi
    
    echo
    if ! check_endpoint "$APP_URL" "Main website"; then
        all_checks_passed=false
    fi
    
    echo
    if ! check_endpoint "$ADMIN_URL" "Admin panel"; then
        all_checks_passed=false
    fi
    
    echo
    if ! check_application_health; then
        all_checks_passed=false
    fi
    
    echo
    check_disk_space
    
    echo
    check_docker_resources
    
    echo
    run_performance_test
    
    echo
    show_summary
    
    if [[ "$all_checks_passed" == true ]]; then
        print_success "All critical checks passed! 🎉"
        print_success "VPL Website is ready for use!"
        exit 0
    else
        print_error "Some checks failed. Please review the output above."
        print_status "Check logs with: docker-compose logs"
        exit 1
    fi
}

# Run main function
main "$@"
