-- VPL Website Database Initialization Script
-- This script creates the initial database schema and seed data

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VA<PERSON>HAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create services table
CREATE TABLE IF NOT EXISTS services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    content TEXT,
    features JSONB DEFAULT '[]',
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('active', 'inactive', 'draft')),
    sort_order INTEGER DEFAULT 0,
    seo_title VARCHAR(500),
    seo_description TEXT,
    seo_keywords TEXT,
    images JSONB DEFAULT '[]',
    tags JSONB DEFAULT '[]',
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Create service_history table for version control
CREATE TABLE IF NOT EXISTS service_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_id UUID NOT NULL REFERENCES services(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    changes TEXT,
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create inquiries table
CREATE TABLE IF NOT EXISTS inquiries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    company VARCHAR(255),
    service_type VARCHAR(100),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    assigned_to UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create sessions table for user sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create audit_logs table for tracking changes
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_services_slug ON services(slug);
CREATE INDEX IF NOT EXISTS idx_services_status ON services(status);
CREATE INDEX IF NOT EXISTS idx_services_sort_order ON services(sort_order);
CREATE INDEX IF NOT EXISTS idx_inquiries_status ON inquiries(status);
CREATE INDEX IF NOT EXISTS idx_inquiries_created_at ON inquiries(created_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inquiries_updated_at BEFORE UPDATE ON inquiries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin user (password: admin123)
-- Note: In production, this should be changed immediately
INSERT INTO users (id, username, email, password_hash, first_name, last_name, role, is_active, email_verified)
VALUES (
    uuid_generate_v4(),
    'admin',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO8G', -- admin123
    'Admin',
    'User',
    'admin',
    true,
    true
) ON CONFLICT (username) DO NOTHING;

-- Insert sample services data
INSERT INTO services (id, name, slug, title, description, content, features, status, sort_order, seo_title, seo_description, seo_keywords, images, tags)
VALUES 
(
    uuid_generate_v4(),
    'VPN服务',
    'vpn_services',
    'VPN服务 - 安全可靠的网络连接',
    '军用级加密的VPN连接服务，保护您的数据安全',
    '<h2>VPN服务详情</h2><p>我们提供军用级加密的VPN连接服务，确保您的网络连接安全可靠。</p><h3>主要特性</h3><ul><li>AES-256军用级加密</li><li>零日志政策，保护隐私</li><li>全球50+国家服务器</li><li>无限带宽，高速连接</li><li>24/7技术支持</li></ul>',
    '["军用级加密", "零日志政策", "全球服务器", "无限带宽"]',
    'active',
    1,
    'VPN服务 - 安全可靠的网络连接 | VPL',
    '军用级加密的VPN连接服务，保护您的数据安全，零日志政策，全球服务器覆盖',
    'VPN,网络安全,加密,隐私保护',
    '["/images/services/vpn-hero.jpg"]',
    '["网络安全", "VPN", "加密"]'
),
(
    uuid_generate_v4(),
    '跨境电商专线',
    'ecommerce_lines',
    '跨境电商专线 - 优化购物体验',
    '优化的跨境电商网络连接解决方案，提升用户购物体验',
    '<h2>跨境电商专线详情</h2><p>专为跨境电商设计的网络解决方案，显著提升用户购物体验。</p><h3>核心优势</h3><ul><li>多平台无缝对接</li><li>99.9%高可用性保障</li><li>智能流量优化</li><li>实时性能监控</li><li>专业技术团队支持</li></ul>',
    '["多平台支持", "高可用性", "流量优化", "实时监控"]',
    'active',
    2,
    '跨境电商专线 - 优化购物体验 | VPL',
    '优化的跨境电商网络连接解决方案，提升用户购物体验，支持多平台',
    '跨境电商,网络专线,购物体验,电商优化',
    '["/images/services/ecommerce-hero.jpg"]',
    '["跨境电商", "网络专线", "购物优化"]'
),
(
    uuid_generate_v4(),
    '外贸专线',
    'foreign_trade_lines',
    '外贸专线 - 全球贸易网络',
    '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
    '<h2>外贸专线详情</h2><p>专为外贸企业设计的高速稳定网络线路，全面支持全球贸易业务。</p><h3>服务特色</h3><ul><li>专用带宽保障</li><li>全球网络覆盖</li><li>超低延迟连接</li><li>企业级技术支持</li><li>7x24小时监控</li></ul>',
    '["专用带宽", "全球覆盖", "低延迟", "企业级支持"]',
    'active',
    3,
    '外贸专线 - 全球贸易网络 | VPL',
    '专为外贸企业设计的高速稳定网络线路，支持全球贸易业务',
    '外贸专线,全球贸易,网络线路,企业网络',
    '["/images/services/trade-hero.jpg"]',
    '["外贸", "专线", "全球网络"]'
),
(
    uuid_generate_v4(),
    '定制解决方案',
    'custom_solution',
    '定制解决方案 - 专属网络方案',
    '根据您的特殊需求定制专属网络解决方案',
    '<h2>定制解决方案详情</h2><p>根据您的特殊需求定制专属网络解决方案，提供个性化服务。</p><h3>定制服务</h3><ul><li>个性化方案设计</li><li>专业技术咨询</li><li>可扩展系统架构</li><li>持续技术支持</li><li>定期优化升级</li></ul>',
    '["定制设计", "专家咨询", "可扩展架构", "持续支持"]',
    'active',
    4,
    '定制解决方案 - 专属网络方案 | VPL',
    '根据您的特殊需求定制专属网络解决方案，专家咨询，可扩展架构',
    '定制方案,网络解决方案,专家咨询,企业定制',
    '["/images/services/custom-hero.jpg"]',
    '["定制", "解决方案", "专家咨询"]'
)
ON CONFLICT (slug) DO NOTHING;
